<?php

namespace app\common\model;

class RepurchaseCardTaskViews extends BaseModel
{
    protected $table = 'bwc_repurchase_card_task_views';

    protected $schema = [
        'id' => 'int',
        'idx_stat_date' => 'date',
        'store_id' => 'int',
        'store_name' => 'varchar',
        'task_type' => 'tinyint',
        'home_page_views' => 'bigint',
        'page_views' => 'bigint',
        'browsing_users_number' => 'int',
        'registered_number' => 'int',
        'registered_users_number' => 'int',
        'task_start_time' => 'datetime',
        'task_end_time' => 'datetime',
        'data_state' => 'tinyint',
        'create_time' => 'timestamp',
        'update_time' => 'timestamp'
    ];
}
