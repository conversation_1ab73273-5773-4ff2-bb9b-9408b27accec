<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2023/8/28 10:41
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\admin\service;

use app\admin\model\Admin as MAdmin;
use app\admin\model\Agent as MAgent;
use app\admin\model\Order as MOrder;
use app\admin\model\Platform;
use app\admin\model\Store as MStore;
use app\common\model\RepurchaseCardTask as MRepurchaseCardTask;
use app\common\model\RepurchaseCardTaskRecord as MRepurchaseCardTaskRecord;
use app\common\model\RepurchaseCardTaskViews as MRepurchaseCardTaskViews;
use think\exception\ValidateException;

class RepurchaseCardTask extends BaseService
{
    public function getById($id)
    {
        $data = MRepurchaseCardTask::get_one(['id' => $id]);
        if (empty($data)) {
            throw new ValidateException('复购卡活动任务不存在');
        }

        return $data;
    }

    public function checkAgentDeadlineAmount($agentId)
    {
        $data = MAgent::where('id', $agentId)
            ->where('check_amount', '=', 2)
            ->whereRaw('current_balance < deadline_amount')
            ->value('agent_name');
        if (!empty($data)) {
            throw new ValidateException("店铺所属代理{$data}余额不足，无法发布任务");
        }
    }

    public function getQuery($params)
    {
        // 只能查询31天的数据
        return MRepurchaseCardTask::where('data_state', 0)
            ->when(!empty($params['store_name']), function ($query) use ($params) {
                $query->where('store_name', 'like', like($params['store_name']));
            })
            ->when(!empty($params['type']), function ($query) use ($params) {
                $query->where('task_type', $params['type']);
            })
            ->when(!empty($params['agent_id']), function ($query) use ($params) {
                $query->where('agent_id', $params['agent_id']);
            })
            ->when(!empty($params['status']), function ($query) use ($params) {
                $date = date('Y-m-d');
                if ($params['status'] == 1) {
                    // 进行中：task_end_time > 当前时间
                    $query->where('task_end_date', '>=', $date)->where('status', MRepurchaseCardTask::PROCESSING);
                } elseif ($params['status'] == 2) {
                    // 已关闭：status = 2 或者 task_end_time <= 当前时间
                    $query->where('status', MRepurchaseCardTask::CLOSED);
                } elseif ($params['status'] == 3) {
                    // 已关闭：status = 2 或者 task_end_time <= 当前时间
                    $query->where('task_end_date', '<', $date);
                }
            })
            ->when(!empty($params['start_time']) || !empty($params['end_time']), function ($query) use ($params) {
                // 筛选与指定日期范围有交集的任务
                if (!empty($params['start_time']) && !empty($params['end_time'])) {
                    // 任务结束时间 >= 筛选开始时间 AND 任务开始时间 <= 筛选结束时间
                    $query->where('task_end_date', '>=', $params['start_time'])
                        ->where('task_start_date', '<=', $params['end_time']);
                } elseif (!empty($params['start_time'])) {
                    // 只有开始时间：任务结束时间 >= 筛选开始时间
                    $query->where('task_end_date', '>=', $params['start_time']);
                } elseif (!empty($params['end_time'])) {
                    // 只有结束时间：任务开始时间 <= 筛选结束时间
                    $query->where('task_start_date', '<=', $params['end_time']);
                }
            })
            ->when(!empty($params['admin_id']), function ($query) use ($params) {
                $query->where('promoter_id', $params['admin_id']);
            });
    }

    public function getList($params, $options)
    {
        $query = $this->getQuery($params);
        $list = $query->page($options['page'])
            ->limit($options['limit'])
            ->order(['id' => 'desc'])
            ->select();
        $taskIds = $list->column('id');
        //任务浏览统计
        $taskViewList = MRepurchaseCardTaskViews::whereIn('id', $taskIds)
            ->field([
                'id',
                'browsing_users_number',
                'page_views',
                'registered_number',
                'registered_users_number',
                'home_page_views'
            ])
            ->select();

        // 已报名数量非取消

        // 主页
        $pageViewsList = $taskViewList->column('page_views', 'id');
        // 浏览
        $homePageViewsList = $taskViewList->column('home_page_views', 'id');
        // 浏览用户
        $browsingUsersNumberList = $taskViewList->column('browsing_users_number', 'id');
        // 报名
        $registeredNumberList = $taskViewList->column('registered_number', 'id');
        // 取消
        $orderQuery = MOrder::alias('a')
            ->join('bwc_order_repurchase_card b', 'b.order_id = a.id')
            ->where('b.repurchase_card_task_id', 'in', $taskIds);

        // 根据筛选日期范围过滤订单
        $orderQuery = $this->applyDateFilterToOrders($orderQuery, $params);

        $orderCancelNumList = (clone $orderQuery)
            ->where('a.order_status', '=', -1)
            ->field(['b.repurchase_card_task_id', 'count(b.id) as num'])
            ->group('b.repurchase_card_task_id')
            ->select()
            ->column('num', 'repurchase_card_task_id');
        $orderNumList = (clone $orderQuery)
            ->whereNotIn('a.order_status', [-1, 1])
            ->field(['b.repurchase_card_task_id', 'count(b.id) as num', 'sum(if(a.order_status=4,1,0)) as finish_num'])
            ->group('b.repurchase_card_task_id')
            ->select();
        $orderFinishNumList = $orderNumList->column('finish_num', 'repurchase_card_task_id');
        $orderNumList = $orderNumList->column('num', 'repurchase_card_task_id');
        $praiseList = array_column(MRepurchaseCardTask::$praise_list, 'name', 'id');
        $typeList = invoke(['app\admin\service\Platform', 'getRepurchaseCardPlatformList']);
        $typeList = $typeList->column('name', 'id');
        // 推广所属
        $promoterIds = $list->column('promoter_id');
        $adminMap = MAdmin::whereIn('id', $promoterIds)
            ->field('real_name, id')
            ->column('real_name', 'id');
        $agentIds = $list->column('agent_id');
        // 代理
        $generalTaskMap = MAgent::whereIn('id', $agentIds)
            ->field('agent_name, id')
            ->column('agent_name', 'id');
        foreach ($list as &$item) {
            // 浏览统计 - 根据筛选日期范围调整显示数据
            $viewsData = $this->adjustViewsDataByDateFilter($item, $params, [
                'page_views' => $pageViewsList[$item['id']] ?? 0,
                'home_page_views' => $homePageViewsList[$item['id']] ?? 0,
                'browsing_users_number' => $browsingUsersNumberList[$item['id']] ?? 0,
                'registered_number' => $registeredNumberList[$item['id']] ?? 0,
            ]);

            $item['page_views'] = $viewsData['page_views'];
            $item['home_page_views'] = $viewsData['home_page_views'];
            $item['browsing_users_number'] = $viewsData['browsing_users_number'];
            $item['registered_number'] = $viewsData['registered_number'];

            // 订单统计
            $item['order_num'] = $orderNumList[$item['id']] ?? 0; // 总订单数
            $item['finish_num'] = $orderFinishNumList[$item['id']] ?? 0; // 已完成订单数
            $item['order_cancel_number'] = $orderCancelNumList[$item['id']] ?? 0; // 取消订单数

            // 好评类型
            $item['is_praise_name'] = $praiseList[$item['is_praise']] ?? null;
            $item['task_type_name'] = $typeList[$item['task_type']] ?? null;

            // 如果任务已过期，强制显示为“已关闭”，未开始，进行中，已关闭
            $this->getStatusName($item);
            $item['agent_name'] = $generalTaskMap[$item['agent_id']] ?? null;
            $item['promoter_name'] = $adminMap[$item['promoter_id']] ?? null;
        }
        return ['list' => $list];
    }

    public function create($admin, $params)
    {
        $store = MStore::where('data_state', 0)->where('id', $params['store_id'])->find();
        $this->checkAgentDeadlineAmount($store['agent_id']);
        $taskStartTime = $params['start_date'];
        $taskEndTime = $params['end_date'];
        $taskData = [
            'store_id' => $params['store_id'],
            'task_cover' => $params['task_cover'],
            'store_name' => $store['store_name'],
            'search_keyword' => $store['store_name'],
            'store_address' => $store['store_address'],
            'store_longitude' => $store['store_longitude'],
            'store_latitude' => $store['store_latitude'],
            'agent_id' => $store['agent_id'],
            'task_start_date' => $taskStartTime,
            'task_end_date' => $taskEndTime,
            'task_total_quota' => $params['task_total_quota'],
            'task_applicants_quota' => 0, // 默认值
            'meal_price' => $params['meal_price'],
            'cash_back_amount' => $params['cash_back_amount'],
            'is_praise' => $params['is_praise'],
            'is_machine_audit' => 1,
            'promoter_id' => $params['promoter_id'],
            'task_type' => $this->convertRepurchaseCardTaskType($store['store_type']),
            'status' => MRepurchaseCardTask::PROCESSING,
        ];
        $model = MRepurchaseCardTask::create($taskData);
        // 日志
        $data = [
            'repurchase_card_task_id' => $model['id'],
            'admin_id' => $admin['id'],
            'before_data' => '',
            'after_data' => json_encode($taskData, JSON_UNESCAPED_UNICODE),
            'operation_ip' => get_client_ip()
        ];
        MRepurchaseCardTaskRecord::create($data);
        MRepurchaseCardTaskViews::create([
            'id' => $model['id'],
            'store_id' => $model['store_id'],
            'store_name' => $model['store_name'],
            'task_type' => $model['task_type'],
            'task_start_time' => $model['task_start_time'],
            'task_end_time' => $model['task_end_time']
        ]);
    }

    public function update($admin, $params)
    {
        $store = MStore::where('data_state', 0)->where('id', $params['store_id'])->find();
        $model = $this->getById($params['id']);
        $isInProgress = $model->isInProgress();
        $isClosed = $model->isClosed();
        $isFinished = $model->isFinished();
        $taskStartTime = $params['start_date'];
        $taskEndTime = $params['end_date'];
        // 活动封面图、活动开始/结束时间、总名额（只能修改比当前报名名额更多的数量）
        $taskData = [
            'task_cover' => $params['task_cover'],
            'task_total_quota' => $params['task_total_quota'],
            'task_start_date' => $taskStartTime,
            'task_end_date' => $taskEndTime,
        ];
        // 只能修改比当前报名名额更多的数量
        if ($params['task_total_quota'] < $model['task_applicants_quota']) {
            throw new ValidateException('任务名额必须大于当前已报名人数' . $model['task_applicants_quota']);
        }
        if (!$isInProgress && !$isClosed && !$isFinished) {
            $taskData['store_id'] = $params['store_id'];
            $taskData['promoter_id'] = $params['promoter_id'];
            $taskData['meal_price'] = $params['meal_price'];
            $taskData['cash_back_amount'] = $params['cash_back_amount'];
            $taskData['is_praise'] = $params['is_praise'];
            $taskData['store_name'] = $store['store_name'];
            $taskData['search_keyword'] = $store['store_name'];
            $taskData['store_address'] = $store['store_address'];
            $taskData['store_longitude'] = $store['store_longitude'];
            $taskData['store_latitude'] = $store['store_latitude'];
            $taskData['agent_id'] = $store['agent_id'];
            $taskData['is_machine_audit'] = 1;
            $taskData['task_type'] = $this->convertRepurchaseCardTaskType($store['store_type']);
            // 校验代理余额
            $this->checkAgentDeadlineAmount($model['agent_id']);
        }
        MRepurchaseCardTask::where('id', $params['id'])->update($taskData);
        // 日志
        $data = [
            'repurchase_card_task_id' => $model['id'],
            'admin_id' => $admin['id'],
            'before_data' => json_encode($model->toArray(), JSON_UNESCAPED_UNICODE),
            'after_data' => json_encode($taskData, JSON_UNESCAPED_UNICODE),
            'operation_ip' => get_client_ip()
        ];
        MRepurchaseCardTaskRecord::create($data);
    }

    public function getDetails($admin, $id): array
    {
        /**
         * @var $details MRepurchaseCardTask
         */
        $details = MRepurchaseCardTask::get_one(['id' => $id]);
        if (!empty($details)) {
            $details['is_in_progress'] = (int)$details->isInProgress();
            $details['start_date'] = $details['task_start_date'];
            $details['promoter_name'] = MAdmin::where('id', $details['promoter_id'])->value('real_name');
            $details['end_date'] = $details['task_end_date'];
            $details['editable'] = (int)(!$details->isInProgress() && !$details->isClosed() && !$details->isFinished());
            $this->getStatusName($details);
        } else {
            $details['editable'] = 1;
        }
        $praiseList = MRepurchaseCardTask::$praise_list;
        return compact('details', 'praiseList');
    }

    private function getStatusName(&$detail)
    {
        $date = date('Y-m-d');
        // 如果任务已过期，强制显示为“已关闭”，未开始，进行中，已关闭
        if ($detail['status'] == MRepurchaseCardTask::CLOSED) {
            $detail['status_name'] = '已关闭';
        } else {
            if ($date < $detail['task_start_date']) {
                $detail['status_name'] = '未开始';
            } elseif ($date >= $detail['task_start_date'] && $date <= $detail['task_end_date']) {
                $detail['status_name'] = '进行中';
            } else {
                $detail['status_name'] = '已结束';
            }
        }
    }


    public function convertRepurchaseCardTaskType($taskType)
    {
        $platformAbbreviation = Platform::where('id', $taskType)->value('platform_abbreviation');
        $map = [
            Platform::PLATFORM_MT => Platform::BUSINESS_PLATFORM_REPURCHASE_CARD_MT,
            Platform::PLATFORM_ELE => Platform::BUSINESS_PLATFORM_REPURCHASE_CARD_ELE,
            Platform::PLATFORM_JD_MS => Platform::BUSINESS_PLATFORM_REPURCHASE_CARD_JD_MS,
        ];

        return Platform::where('platform_abbreviation', $map[$platformAbbreviation])->value('id');
    }

    /**
     * 根据筛选日期范围过滤订单
     * 只统计在筛选日期范围内创建的订单
     *
     * @param $orderQuery
     * @param $params
     * @return mixed
     */
    private function applyDateFilterToOrders($orderQuery, $params)
    {
        if (!empty($params['start_time']) || !empty($params['end_time'])) {
            if (!empty($params['start_time']) && !empty($params['end_time'])) {
                // 订单创建时间在筛选日期范围内
                $orderQuery->whereTime('a.create_time', '>=', $params['start_time'] . ' 00:00:00')
                    ->whereTime('a.create_time', '<=', $params['end_time'] . ' 23:59:59');
            } elseif (!empty($params['start_time'])) {
                // 订单创建时间 >= 筛选开始时间
                $orderQuery->whereTime('a.create_time', '>=', $params['start_time'] . ' 00:00:00');
            } elseif (!empty($params['end_time'])) {
                // 订单创建时间 <= 筛选结束时间
                $orderQuery->whereTime('a.create_time', '<=', $params['end_time'] . ' 23:59:59');
            }
        }

        return $orderQuery;
    }

    /**
     * 根据筛选日期范围调整浏览统计数据
     * 如果有日期筛选，按比例计算显示数据
     *
     * @param $task
     * @param $params
     * @param $originalData
     * @return array
     */
    private function adjustViewsDataByDateFilter($task, $params, $originalData)
    {
        // 如果没有日期筛选，返回原始数据
        if (empty($params['start_time']) && empty($params['end_time'])) {
            return $originalData;
        }

        // 计算任务的实际运行日期范围
        $taskStartDate = $task['task_start_date'];
        $taskEndDate = $task['task_end_date'];

        // 计算筛选日期范围
        $filterStartDate = $params['start_time'] ?? $taskStartDate;
        $filterEndDate = $params['end_time'] ?? $taskEndDate;

        // 计算任务与筛选日期的交集
        $intersectionStartDate = max($taskStartDate, $filterStartDate);
        $intersectionEndDate = min($taskEndDate, $filterEndDate);

        // 如果没有交集，返回0数据
        if ($intersectionStartDate > $intersectionEndDate) {
            return [
                'page_views' => 0,
                'home_page_views' => 0,
                'browsing_users_number' => 0,
                'registered_number' => 0,
            ];
        }

        // 计算比例：交集天数 / 任务总天数
        $taskTotalDays = (strtotime($taskEndDate) - strtotime($taskStartDate)) / 86400 + 1;
        $intersectionDays = (strtotime($intersectionEndDate) - strtotime($intersectionStartDate)) / 86400 + 1;
        $ratio = $intersectionDays / $taskTotalDays;

        // 按比例调整数据（浏览数据按比例，用户数据不按比例因为用户是唯一的）
        return [
            'page_views' => (int)($originalData['page_views'] * $ratio),
            'home_page_views' => (int)($originalData['home_page_views'] * $ratio),
            'browsing_users_number' => $originalData['browsing_users_number'], // 用户数不按比例
            'registered_number' => $originalData['registered_number'], // 报名数通过订单统计已经过滤
        ];
    }
}
