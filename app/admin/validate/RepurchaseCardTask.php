<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2021/9/12 18:25
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------


namespace app\admin\validate;

use app\admin\model\Admin as MAdmin;
use app\admin\model\Platform;
use app\admin\model\Store as MStore;
use app\common\model\RepurchaseCardTask as MRepurchaseCardTask;
use think\Validate;

class RepurchaseCardTask extends Validate
{
    protected $rule = [
        'store_id|所属店铺' => 'require|checkStoreId',
        'task_cover|封面' => 'require|max:255|url',
        'promoter_id|推广所属' => 'require|checkPromoterId',
        'task_total_quota|任务名额' => "require|number|>:0",
        'start_date|开始日期' => 'require|dateFormat:Y-m-d',
        'end_date|结束日期' => 'require|dateFormat:Y-m-d|afterStartDate',
        'meal_price|餐标价格' => 'require|float|>=:0.01|regex:/^\d+(\.\d{1,2})?$/',
        'cash_back_amount|返现价格' => 'require|float|>=:0.01|regex:/^\d+(\.\d{1,2})?$/',
        'is_praise|反馈需求' => 'require|in:1,2,3,4',
        // 列表查询验证规则
        'start_time|开始日期' => 'require|dateFormat:Y-m-d|checkDateRange',
        'end_time|结束日期' => 'dateFormat:Y-m-d',
    ];

    protected $message = [
        'meal_price.regex' => 'meal_price只能输入大于0.01的两位小数',
        'cash_back_amount.regex' => '返现价格只能输入大于0.01的两位小数',
    ];

    protected $scene = [
        'create' => [
            'store_id',
            'task_cover',
            'promoter_id',
            'start_date',
            'end_date',
            'task_total_quota',
            'meal_price',
            'cash_back_amount',
            'is_praise' => 1
        ],
        'list' => [
            'start_time',
            'end_time',
        ],
        'id' => ['id']
    ];

    public function sceneUpdate(): RepurchaseCardTask
    {
        return $this->only([
            'id',
            'store_id',
            'task_cover',
            'promoter_id',
            'start_date',
            'end_date',
            'task_total_quota',
            'meal_price',
            'cash_back_amount',
            'is_praise' => 1
        ])
            ->append('task_total_quota', 'checkQuota')
            ->append('store_name', 'checkName');
    }

    protected function checkQuota($value, $rule, $data)
    {
        $task = MRepurchaseCardTask::get_one(['id' => $data['id']]);
        if (empty($task)) {
            return "复购卡活动不存在";
        }
        if ($value < $task['task_applicants_quota']) {
            return "总名额不得小于已报名额";
        }
        return true;
    }

    protected function afterStartDate($value, $rule, $data)
    {
        if (strtotime($value) < strtotime($data['start_date'])) {
            return "结束日期不能小于开始日期";
        }
        return true;
    }

    protected function afterStartTime($value, $rule, $data)
    {
        if (strtotime($value) <= strtotime($data['start_time'])) {
            return "结束日期不能小于开始日期";
        }
        return true;
    }

    protected function checkStoreId($value, $rule, $data)
    {
        $data = MStore::where('data_state', 0)->where('id', $value)->find();
        if (empty($data)) {
            return "所属店铺不存在";
        }
        if (!in_array($data['store_type'], Platform::getStoreRepurchaseCardIds())) {
            return "所属店铺类型不能发布复购卡活动";
        }
        return true;
    }

    protected function checkPromoterId($value, $rule, $data)
    {
        $data = MAdmin::where('data_state', 0)->where('id', $value)->find();
        if (empty($data)) {
            return "推广所属不存在";
        }
        return true;
    }

    /**
     * 验证日期范围不能超过31天
     *
     * @param string $value 开始日期
     * @param string $rule 验证规则
     * @param array $data 所有数据
     * @return bool|string
     */
    protected function checkDateRange($value, $rule, $data)
    {
        // 如果没有结束日期，不需要验证范围
        if (empty($data['end_time'])) {
            return '结束日期不能为空';
        }

        $startTime = $value;
        $endTime = $data['end_time'];

        $startTimestamp = strtotime($startTime);
        $endTimestamp = strtotime($endTime);

        // 验证开始日期不能大于结束日期
        if ($startTimestamp > $endTimestamp) {
            return '开始日期不能大于结束日期';
        }

        // 计算天数差（包含开始和结束日期）
        $daysDiff = ($endTimestamp - $startTimestamp) / 86400 + 1;

        // 验证不能超过31天
        if ($daysDiff > 31) {
            return '查询日期范围不能超过31天';
        }

        return true;
    }
}
