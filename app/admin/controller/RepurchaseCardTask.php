<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2021/9/12 10:57
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\admin\controller;

use app\admin\service\RepurchaseCardTask as Service;
use app\admin\validate\RepurchaseCardTask as Validate;
use app\client\service\wechat\Applet;
use app\client\service\wechat\AppletAir;
use app\common\model\RepurchaseCardTask as MRepurchaseCardTask;
use app\common\model\RepurchaseCardTaskRecord as MRepurchaseCardTaskRecord;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelHigh;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\RoundBlockSizeMode\RoundBlockSizeModeEnlarge;
use Endroid\QrCode\Writer\PngWriter;
use think\exception\ValidateException;
use think\facade\Db;
use think\response\Json;

/**
 * @mixin \app\admin\logic\Task
 */
class RepurchaseCardTask extends Base
{
    protected $middleware = [
        'checkToken' => ['except' => 'mini_qrcode']
    ];

    public function index(): Json
    {
        $admin = $this->request->user;
        $typeList = invoke(['app\admin\service\Platform', 'getRepurchaseCardPlatformList']);
        $statusList = MRepurchaseCardTask::$status_list;
        $agentList = invoke(['app\admin\service\Agent', 'getAgentSelectList'], [$admin, 1]);
        $data = compact('typeList', 'statusList', 'agentList');
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 列表
     * @return Json
     */
    public function list(): Json
    {
        $params = $this->request->get([
            'store_name',
            'type',
            'agent_id',
            'status',
            'start_time',
            'end_time',
            'admin_id'
        ]);

        $this->validate($params, Validate::class . '.list');
        $options = $this->request->get(['page' => 0, 'limit' => 15]);
        $data = invoke([Service::class, 'getList'], [$params, $options]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 详情
     * @return Json
     */
    public function details(): Json
    {
        // 请求id并过滤
        $admin = $this->request->user;
        $id = $this->request->get('id', 0, 'intval');
        $data = invoke([Service::class, 'getDetails'], [$admin, $id]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 新增
     *
     * @return Json
     * @throws \RedisException
     */
    public function create(): Json
    {
        $user = $this->request->user;
        $this->checkRepeat($user['id']);
        // 请求参数
        $params = $this->request->post([
            'store_id',
            'promoter_id',
            'task_cover',
            'start_date',
            'end_date',
            'task_total_quota',
            'meal_price',
            'cash_back_amount',
            'is_praise' => 1
        ]);
        $this->validate($params, Validate::class . '.create');
        Db::startTrans();
        try {
            $data = invoke([Service::class, 'create'], [$user, $params]);
            Db::commit();
            return json(HBKReturn('success', $data, 200));
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    public function update(): Json
    {
        $user = $this->request->user;
        $this->checkRepeat($user['id']);
        $params = $this->request->post([
            'id',
            'store_id',
            'promoter_id',
            'task_cover',
            'start_date',
            'end_date',
            'task_total_quota',
            'meal_price',
            'cash_back_amount',
            'is_praise' => 1
        ]);
        $this->validate($params, Validate::class . '.update');
        Db::startTrans();
        try {
            $data = invoke([Service::class, 'update'], [$user, $params]);
            Db::commit();
            return json(HBKReturn('success', $data, 200));
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    public function change_status(): Json
    {
        // 请求id并过滤
        $user = $this->request->user;
        $id = $this->request->post('id', 0, 'intval');
        $this->validate(['id' => $id], Validate::class . '.id');
        $task = MRepurchaseCardTask::get_one(['id' => $id]);
        $date = date('Y-m-d');
        if ($task['task_end_date'] < $date) {
            throw new ValidateException('已结束，不可切换状态');
        }
        $status = $task['status'] === MRepurchaseCardTask::PROCESSING ? MRepurchaseCardTask::CLOSED : MRepurchaseCardTask::PROCESSING;
        if ($status == MRepurchaseCardTask::PROCESSING) {
            invoke([Service::class, 'checkAgentDeadlineAmount'], [$task['agent_id']]);
        }
        Db::starttrans();
        try {
            MRepurchaseCardTask::update(['id' => $id, 'status' => $status]);
            $taskNew = MRepurchaseCardTask::get_one(['id' => $id]);
            // 日志
            $data = [
                'repurchase_card_task_id' => $id,
                'admin_id' => $user['id'],
                'before_data' => json_encode($task->toArray(), JSON_UNESCAPED_UNICODE),
                'after_data' => json_encode($taskNew->toArray(), JSON_UNESCAPED_UNICODE),
                'operation_ip' => get_client_ip()
            ];
            MRepurchaseCardTaskRecord::create($data);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
        return json(HBKReturn('success', null, 200));
    }

    public function mini_qrcode(): Json
    {
        // todo gjn 确认path
        try {
            $token = invoke([Applet::class, 'getToken']);
            $mini = invoke([Applet::class, 'getWxQrCodeByBase64'], [$token, 'package/repurchaseActivity/index', 'mini_qrcode']);
            $airToken = invoke([AppletAir::class, 'getToken']);
            $air = invoke([Applet::class, 'getWxQrCodeByBase64'], [$airToken, 'package/repurchaseActivity/index', 'mini_qrcode']);
            $host = env('WEB_BASE_URL');
            $data = $host . 'package/repurchaseActivity/index?enterScene=qrcode';
            $writer = new PngWriter();
            $qrCode = QrCode::create($data)
                ->setEncoding(new Encoding('UTF-8'))
                ->setErrorCorrectionLevel(new ErrorCorrectionLevelHigh)
                ->setSize(510)
                ->setMargin(0)
                ->setRoundBlockSizeMode(new RoundBlockSizeModeEnlarge())
                ->setForegroundColor(new Color(0, 0, 0))
                ->setBackgroundColor(new Color(255, 255, 255));
            $result = $writer->write($qrCode);
            return json(HBKReturn('success', ['mini' => $mini, 'h5' => $result->getDataUri(), 'air' => $air], 200));
        } catch (\Exception $e) {
            \app\Log::error(
                'RepurchaseCardTaskMiniQrcodeError', $e, 'RepurchaseCardTaskMiniQrcodeError', $e->getMessage(), [], true
            );
            throw $e;
        }
    }
}
