<?php

namespace app\client\controller;

use app\client\exception\OrderNumUpLimitException;
use app\client\model\Config;
use app\client\model\Platform;
use app\client\service\BusinessPlatform;
use app\client\service\EleTask as SEleTask;
use app\client\service\UserHistory as SUserHistory;
use app\common\exception\ExceptionCode;
use app\common\exception\TokenException;
use app\Log;
use think\Exception;
use think\exception\ValidateException;
use think\response\Json;

class EleTask extends Base
{
    protected $middleware = [
        'checkToken' => [
            'only' => ['sign_up', 'feedback_sign_up']
        ]
    ];

    /**
     * 获取饿了么活动详情
     * @param $type
     * @return Json
     * @throws \Exception
     */
    public function task_detail($type): Json
    {
        $id = $this->request->get('id');
        $position = $this->request->get(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $params = $this->request->get(['tag' => null, 'rush' => false, 'from' => 0]);

        if (empty($id)) {
            throw new Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }

        $userId = $this->getUserByToken();
        // 领取红包的文案
        $redEnvelopText = (string)Config::getValueByKey('red_envelop_text');
        if (intval($type) === 2) {
            $task = invoke([SEleTask::class, 'getTaskDetail'], [$userId, $id, $position, $params]);
            $task['red_envelop_text'] = $redEnvelopText;
            $data = ['task' => $task];
        } else {
            // 饿了么专享
            $task = invoke([SEleTask::class, 'getFeedbackTaskDetail'], [$userId, $id, $position, $params]);
            // 新增高亮标签
            if (cjf_scg_activity_version_compare()) {
                $tips = Config::getValueByKey('elez_complete_information_text');
            } else {
                $tips = Config::getValueByKey('ele_task_tips');
            }
            $tips = json_decode($tips, true) ?? [];
            $replaceTag = Config::getValueByKey('high_light_replace_tag');
            $replaceTag = json_decode($replaceTag, true);
            foreach ($tips as &$item) {
                $item['content'] = str_replace('{highlightTextStart}', $replaceTag['highlightTextStart'], $item['content']);
                $item['content'] = str_replace('{highlightTextEnd}', $replaceTag['highlightTextEnd'], $item['content']);
            }

            $task['red_envelop_text'] = $redEnvelopText;
            $data = ['task' => $task, 'tips' => $tips];
        }

        if (user_client_improve_v_compare() && !empty($userId)) {
            invoke([SUserHistory::class, 'addFootprintList'], [$userId, [
                'task_id' => $task['seller_id'],
                'platform_abbr' => $task['platform_abbr'],
                'store_name' => $task['store_name'],
                'task_cover' => $task['task_cover']
            ]]);
        }

        $returnData = aesEncryption($data);
        return json(HBKReturn('success', $returnData, 200));
    }

    /**
     * 饿了么官方活动规则
     * @param $type
     * @return Json
     */
    public function task_detailed_rules($type): Json
    {
        return json(HBKReturn('success', [
            'task_detailed_rules' => invoke([BusinessPlatform::class, 'getTaskDetailedRules'], [
                intval($type) === 2 ? Platform::PLT_ELEG : Platform::PLT_ELEZ
            ])
        ], 200));
    }

    /**
     * 饿了么报名
     * @throws \Exception
     */
    public function sign_up(): Json
    {
        limitLowVersionSignUp();
        $id = $this->request->post('id');
        $position = $this->request->post(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $user = $this->request->user;

        try {
            if (empty($user['user_mobile'])) {
                throw new ValidateException('请先登录！');
            }
            if ($user->status == 1) {
                throw new ValidateException('您无法报名，请联系管理员。');
            }
            $order = invoke([SEleTask::class, 'signUp'], [$user, $id, $position, getCurrentPlatform()]);
        } catch (OrderNumUpLimitException $e) {
            return json(HBKReturn($e->getMessage(), [], 4001));
        } catch (\Exception $e) {
            Log::error('sign_up', $e, '报名失败', $id,  $this->request->param());
            return json(HBKReturn($e->getMessage(), [], 422));
        }
        return json(HBKReturn('success', ['order_id' => $order['id']], 200));
    }

    /**
     * 饿了么评价活动报名
     * @throws \Exception
     * @throws TokenException
     */
    public function feedback_sign_up(): Json
    {
        limitLowVersionSignUp();
        $position = $this->request->post(['lat' => 0, 'lng' => 0, 'sel_lat' => 0, 'sel_lng' => 0], 0, 'doubleval');
        $id = $this->request->post('id');
        $elePhone = $this->request->post('ele_phone');
        $code = $this->request->post('code');
        $platform = $this->request->platform;
        $user = $this->request->user;

        try {
            if (empty($user['user_mobile'])) {
                throw new ValidateException('请先登录！');
            }

            if (empty($elePhone)) {
                throw new ValidateException('饿了么手机号不能为空');
            }

            if ($user->status == 1) {
                throw new ValidateException('您无法报名，请联系管理员。');
            }

            $order = invoke([SEleTask::class, 'feedbackSignUp'], [$user, $id, $position, $platform, $elePhone, $code]);
        } catch (OrderNumUpLimitException $e) {
            return json(HBKReturn($e->getMessage(), ['verify' => 1], 4001));
        } catch (ValidateException $e) {
            Log::error('sign_up', $e, '报名验证失败', $elePhone,  $this->request->param());
            return json(HBKReturn($e->getMessage(), ['verify' => 0], 422));
        } catch (\Exception $e) {
            Log::error('sign_up', $e, '报名失败', $elePhone,  $this->request->param());
            return json(HBKReturn($e->getMessage(), ['verify' => 1], 422));
        }

        return json(HBKReturn('success', ['order_id' => $order['id'], 'verify' => 1], 200));
    }
}
