<?php

namespace app\client\controller;

use app\client\exception\OrderNumUpLimitException;
use app\client\model\Platform;
use app\client\service\BusinessPlatform;
use app\client\service\HtlTask as SHtlTask;
use app\common\exception\ExceptionCode;
use app\Log;
use think\Exception;
use think\exception\ValidateException;
use think\response\Json;

class HtlTask extends Base
{
    protected $middleware = [
        'checkToken' => [
            'only' => ['sign_up']
        ]
    ];

    /**
     * 获取灰太狼活动详情
     * @return Json
     * @throws \Exception
     */
    public function task_detail(): Json
    {
        $id = $this->request->get('id');
        $position = $this->request->get(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $params = $this->request->get(['tag' => null, 'rush' => false, 'from' => 0]);

        if (empty($id)) {
            throw new Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }

        $userId = $this->getUserByToken();
        $task = invoke([SHtlTask::class, 'getTaskDetail'], [intval($userId), $id, $position, $params]);
        $data = ['task' => $task];
        $returnData = aesEncryption($data);
        return json(HBKReturn('success', $returnData, 200));
    }

    /**
     * 灰太狼活动规则
     * @return Json
     */
    public function task_detailed_rules(): Json
    {
        $platformAbbr = $this->request->get('platform_abbr', '');

        if (!in_array($platformAbbr, [Platform::PLT_HTL_MT, Platform::PLT_HTL_ELE])) {
            $platformAbbr = Platform::PLT_HTL_MT;
        }

        return json(HBKReturn('success', [
            'task_detailed_rules' => invoke([BusinessPlatform::class, 'getTaskDetailedRules'], [
                $platformAbbr
            ])
        ], 200));
    }

    /**
     * 报名
     * @return Json
     * @throws \Exception
     */
    public function sign_up()
    {
        $id = $this->request->post('task_id');
        $redEnvelopeId = $this->request->post('red_envelope_id', 0);
        $position = $this->request->post(['lat' => 0, 'lng' => 0, 'sel_lat' => 0, 'sel_lng' => 0], 0, 'floatval');
        $user = $this->request->user;

        try {
            if (empty($user['user_mobile'])) {
                throw new ValidateException('请先登陆！');
            }

            if ($user->status == 1) {
                throw new ValidateException('您无法报名，请联系管理员。');
            }

            $order = invoke([SHtlTask::class, 'signUp'], [$user, $id, $position, getCurrentPlatform(), getCurrentVersion(), $redEnvelopeId]);
        } catch (OrderNumUpLimitException $e) {
            return json(HBKReturn($e->getMessage(), [], 4001));
        } catch (\Exception $e) {
            Log::error('sign_up', $e, '报名失败', $id,  $this->request->param());
            return json(HBKReturn($e->getMessage(), [], 422));
        }
        return json(HBKReturn('success', ['order_id' => $order['id']], 200));
    }
}
