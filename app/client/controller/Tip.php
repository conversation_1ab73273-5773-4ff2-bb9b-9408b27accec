<?php

namespace app\client\controller;


use app\client\model\Config;
use app\client\model\Order as MOrder;
use app\client\service\MsgTip;
use think\response\Json;

class Tip extends Base
{
    /**
     * 是否需要显示提示及引导页
     * @return Json
     */
    public function show_tip(): Json
    {
        $userId = $this->getUserByToken();
        $version = getCurrentVersion();
        $tipResult = invoke([MsgTip::class, 'showTip'], [intval($userId), $version]);
        $guideResult = invoke([MsgTip::class, 'showGuide'], [intval($userId), $version]);
        return json(HBKReturn('success', $tipResult + $guideResult, 200));
    }

    /**
     * @return Json
     */
    public function withdraw_tip(): Json
    {
        $userId = $this->getUserByToken();
        $order = MOrder::alias('o')
            ->field(['o.id'])
            ->join('bwc_order_mt ot', 'o.id = ot.order_id')
            ->where('ot.type', 3)
            ->where('o.user_id', $userId)
            ->where('o.order_status', MOrder::COMPLETED)
            ->where('ot.completion_time', '>=', date("Y-m-d H:i:s", strtotime("-72 hours")))
            ->where('o.create_time', '>=', date("Y-m-d H:i:s", strtotime("-1 month")))
            ->find();
        $tips = empty($order) ? null : [
            'text' => '由于与美团合作升级，您近期有订单返利需前往美团/美团外卖APP钱包中完成提现',
            'images' => json_decode(Config::getValueByKey('cjf_scg_order_image'), true)
        ];
        return json(HBKReturn('success', ['tip' => $tips], 200));
    }

    /**
     * 保存提示消息
     * @return Json
     */
    public function save_tip(): Json
    {
        $userId = $this->getUserByToken();
        $version = getCurrentVersion();
        $platform = getCurrentPlatform();
        $source = $this->request->post('source', 'pop_up_tip', 'trim');
        invoke([MsgTip::class, 'saveTip'], [intval($userId), $version, $platform, $source]);
        return json(HBKReturn('success', [], 200));
    }

    /**
     * 保存引导消息
     * @return Json
     */
    public function save_guide(): Json
    {
        $userId = $this->getUserByToken();
        $version = getCurrentVersion();
        $platform = getCurrentPlatform();
        $source = $this->request->post('source', 'boot_guide', 'trim');
        invoke([MsgTip::class, 'saveGuide'], [intval($userId), $version, $platform, $source]);
        return json(HBKReturn('success', [], 200));
    }

    /**
     * 订单报名前的弹窗提示
     * @return Json
     */
    public function task_pre_sign_up(): Json
    {
        $userId = $this->getUserByToken();
        $pltAbbr = $this->request->post('platform_abbr', '', 'trim');
        $data = invoke([MsgTip::class, 'getTaskPreSignUpPopup'], [intval($userId), $pltAbbr]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 订单报名前的弹窗提示
     * @return Json
     */
    public function task_pre_sign_up_list(): Json
    {
        $userId = $this->getUserByToken();
        $pltAbbr = $this->request->post('platform_abbr', '', 'trim');
        $data = invoke([MsgTip::class, 'getTaskPreSignUpPopupList'], [intval($userId), $pltAbbr]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 保存活动报名前的弹窗提示
     * @return Json
     */
    public function save_task_pre_sign_up()
    {
        $userId = $this->getUserByToken();
        $version = getCurrentVersion();
        $platform = getCurrentPlatform();
        $scene = $this->request->post('scene', '', 'trim');
        invoke([MsgTip::class, 'saveTaskPreSignUpPopup'], [intval($userId), $version, $platform, $scene]);
        return json(HBKReturn('success', [], 200));
    }
}
