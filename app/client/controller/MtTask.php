<?php

namespace app\client\controller;

use app\client\model\Config;
use app\client\exception\OrderNumUpLimitException;
use app\client\model\Platform;
use app\client\service\BusinessPlatform;
use app\client\service\mt\MTGPSignUp;
use app\client\service\mt\MTGPTaskDetail;
use app\client\service\MtTask as SMtTask;
use app\client\service\UserHistory as SUserHistory;
use app\common\exception\ExceptionCode;
use app\common\exception\TokenException;
use app\common\service\TaskView\MtBwc;
use app\common\service\TaskView\TaskViewEscalation;
use app\Log;
use think\Exception;
use think\exception\ValidateException;
use think\response\Json;
use app\client\model\UserPopupSetting;

class MtTask extends Base
{
    protected $middleware = [
        'checkToken' => [
            'only' => ['sign_up', 'mtg_gp_sign_up']
        ]
    ];

    /**
     * 获取美团活动详情
     * @return Json
     * @throws \Exception
     */
    public function task_detail(): Json
    {
        $id = $this->request->get('id');
        $position = $this->request->get(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $params = $this->request->get(['tag' => null, 'rush' => false, 'from' => 0]);

        $userId = $this->getUserByToken();
        if (empty($id)) {
            throw new Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }

        [$task, $clickUrl] = invoke([SMtTask::class, 'getTaskDetail'], [$userId, $id, $position, $params, multi_task_merge_version_compare()]);
        if (!empty($clickUrl)) {
            /** @var MtBwc $escalationClient */
            $escalationClient = app(MtBwc::class);
            $taskViewEscalation = new TaskViewEscalation($escalationClient);
            $taskViewEscalation->publish(TaskViewEscalation::PAGE_VIEWS, $clickUrl);
        }

        // 新增高亮标签
        if (cjf_scg_activity_version_compare()) {
            $tips = Config::getValueByKey('mtg_complete_information_text');
        } else {
            $tips = Config::getValueByKey('mt_task_tips');
        }

        $tips = json_decode($tips, true) ?? [];
        $replaceTag = Config::getValueByKey('high_light_replace_tag');
        $replaceTag = json_decode($replaceTag, true);
        foreach ($tips as &$item) {
            $item['content'] = str_replace('{highlightTextStart}', $replaceTag['highlightTextStart'], $item['content']);
            $item['content'] = str_replace('{highlightTextEnd}', $replaceTag['highlightTextEnd'], $item['content']);
        }

        // 新增提示弹窗，如果用户关闭弹窗提示则不会展示弹窗, 默认是开启的
        $cjfScgPopupSetting = 1;
        if (!empty($userId)) {
            $cjfScgPopupSetting = UserPopupSetting::where('user_id', $userId)
                ->where('type', UserPopupSetting::CJF_SCG_POPUP)
                ->where('status', UserPopupSetting::STATUS_CLOSE)
                ->count() == 0;
        }

        $cjfScgOrderTextEnabled = Config::getValueByKey('cjf_scg_order_text_enabled');
        $popUpTips = ($cjfScgPopupSetting && $cjfScgOrderTextEnabled) ? Config::getValueByKey('cjf_scg_order_text') : '';
        if (!empty($popUpTips)) {
            $popUpTips = str_replace('{highlightTextStart}', $replaceTag['highlightTextStart'], $popUpTips);
            $popUpTips = str_replace('{highlightTextEnd}', $replaceTag['highlightTextEnd'], $popUpTips);
        }

        $cjfScgOrderImage = Config::getValueByKey('cjf_scg_order_image');

        // 领取红包的文案
        $task['red_envelop_text'] = (string)Config::getValueByKey('red_envelop_text');

        $data = ['task' => $task, 'tips' => $tips, 'pop_up_tips' => $popUpTips, 'cjf_scg_order_image' => json_decode($cjfScgOrderImage, true)];
        if (user_client_improve_v_compare() && !empty($userId)) {
            invoke([SUserHistory::class, 'addFootprintList'], [$userId, [
                'task_id' => $id,
                'platform_abbr' => $task['platform_abbr'],
                'store_name' => $task['store_name'],
                'task_cover' => $task['task_cover']
            ]]);
        }

        $returnData = aesEncryption($data);

        return json(HBKReturn('success', $returnData, 200));
    }

    /**
     * 美团官方活动规则
     * @return Json
     */
    public function task_detailed_rules(): Json
    {
        return json(HBKReturn('success', [
            'task_detailed_rules' => invoke([BusinessPlatform::class, 'getTaskDetailedRules'], [Platform::PLT_MTG])
        ], 200));
    }

    /**
     * 美团报名
     * @throws \Exception
     * @throws TokenException
     */
    public function sign_up(): Json
    {
        limitLowVersionSignUp();
        $position = $this->request->post(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $sellerId = $this->request->post('id');
        $mtPhone = $this->request->post('mt_phone');
        $code = $this->request->post('code');
        $platform = $this->request->platform;
        $user = $this->request->user;

        try {
            if (empty($user['user_mobile'])) {
                throw new ValidateException('请先登录！');
            }
            if ($user->status == 1) {
                throw new ValidateException('您无法报名，请联系管理员。');
            }
            $order = invoke([SMtTask::class, 'signUp'], [$mtPhone, $user, $sellerId, $position, $platform, $code]);
        } catch (OrderNumUpLimitException $e) {
            return json(HBKReturn($e->getMessage(), ['verify' => 1], 4001));
        } catch (ValidateException $e) {
            Log::error('sign_up', $e, '报名验证失败', $mtPhone,  $this->request->param());
            return json(HBKReturn($e->getMessage(), ['verify' => 0], 422));
        } catch (\Exception $e) {
            Log::error('sign_up', $e, '报名失败', $mtPhone,  $this->request->param());
            return json(HBKReturn($e->getMessage(), ['verify' => 1], 422));
        }
        return json(HBKReturn('success', ['order_id' => $order['id'], 'verify' => 1], 200));
    }

    /**
     * 获取美团官方团购活动详情
     * @return Json
     * @throws \Exception
     */
    public function mtg_gp_task_detail(): Json
    {
        // 团单标识id
        $productIndexId = $this->request->get('id');
        $position = $this->request->get(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $params = $this->request->get(['rush' => false, 'from' => 0]);
        $agentId = $this->request->get('agent_id', '');

        $userId = (int)$this->getUserByToken();
        if (empty($productIndexId)) {
            throw new Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }

        $mtGp = new MTGPTaskDetail();
        $mtGp->setUserId($userId);
        $mtGp->setProductIndexId($productIndexId);
        $mtGp->setPosition($position);
        $mtGp->setParam($params);
        $mtGp->setAgentId(empty($agentId) ? 0 : $agentId);

        [$task, $clickUrl] = $mtGp->getTaskDetail();

        // 上报点击数据
//        if (!empty($clickUrl)) {
//            /** @var MtBwc $escalationClient */
//            $escalationClient = app(MtBwc::class);
//            $taskViewEscalation = new TaskViewEscalation($escalationClient);
//            $taskViewEscalation->publish(TaskViewEscalation::PAGE_VIEWS, $clickUrl);
//        }

        // 新增提示弹窗，如果用户关闭弹窗提示则不会展示弹窗, 默认是开启的
        $replaceTag = Config::getValueByKey('high_light_replace_tag');
        $replaceTag = json_decode($replaceTag, true);

        $cjfScgOrderImage = Config::getValueByKey('cjf_scg_order_image');

        $popUpTips = '';
        $enabled = Config::getValueByKey('mtg_gp_order_text_enabled');

        if ($enabled) {
            $popupSetting = 1;

            if (!empty($userId)) {
                $popupSetting = UserPopupSetting::where('user_id', $userId)
                        ->where('type', UserPopupSetting::MTG_GP_POPUP)
                        ->where('status', UserPopupSetting::STATUS_CLOSE)
                        ->count() == 0;
            }

            $popUpTips = $popupSetting ? Config::getValueByKey('mtg_gp_order_text') : '';

            if (!empty($popUpTips)) {
                $popUpTips = str_replace('{highlightTextStart}', $replaceTag['highlightTextStart'], $popUpTips);
                $popUpTips = str_replace('{highlightTextEnd}', $replaceTag['highlightTextEnd'], $popUpTips);
            }
        }

        $data = ['task' => $task, 'pop_up_tips' => $popUpTips, 'cjf_scg_order_image' => json_decode($cjfScgOrderImage, true)];
        $returnData = aesEncryption($data);
        return json(HBKReturn('success', $returnData, 200));
    }

    /**
     * 美团官方团购活动报名
     * @throws \Exception
     * @throws TokenException
     */
    public function mtg_gp_sign_up(): Json
    {
        $productIndexId = $this->request->post('id');
        $poiEventId = $this->request->post('poi_event_id');
        $position = $this->request->post(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $mtPhone = $this->request->post('mt_phone');
        $nearestPoiInfo = $this->request->post('nearest_poi_info', []);
        $platform = $this->request->platform;
        $user = $this->request->user;

        try {
            if (empty($user['user_mobile'])) {
                throw new ValidateException('请先登录！');
            }

            if ($user->status == 1) {
                throw new ValidateException('您无法报名，请联系管理员。');
            }

            if (empty($productIndexId) || empty($poiEventId)) {
                throw new ValidateException('活动不存在！');
            }

            // 美团活动报名
            $obj = new MTGPSignUp();
            $mtPhone = $obj->getTaskMtPhone($user['id']);

            if (empty($mtPhone)) {
                throw new ValidateException('您无法报名，请联系管理员。');
            }

            if ($obj->getUserSignUpOrderNum($user['id']) >= 3) {
                throw new OrderNumUpLimitException('当前已报名状态的活动已达上限，请及时提交信息');
            }

            $obj->setMtPhone($mtPhone);
            $obj->setUser($user);
            $obj->setPosition($position);
            $obj->setPlatform($platform);
            $obj->setPoiEventId($poiEventId);
            $obj->setProductIndexId($productIndexId);

            if (!empty($nearestPoiInfo)) {
                $obj->setNearestPoiInfo(!is_array($nearestPoiInfo) ? [] : $nearestPoiInfo);
            }

            $order = $obj->signUp();
        } catch (OrderNumUpLimitException $e) {
            return json(HBKReturn($e->getMessage(), ['verify' => 1], 4001));
        } catch (ValidateException $e) {
            Log::error('sign_up', $e, '报名验证失败', $mtPhone,  $this->request->param());
            return json(HBKReturn($e->getMessage(), ['verify' => 0], 422));
        } catch (\Exception $e) {
            Log::error('sign_up', $e, '报名失败', $mtPhone,  $this->request->param());
            return json(HBKReturn($e->getMessage(), ['verify' => 1], 422));
        }
        return json(HBKReturn('success', ['order_id' => $order['id'], 'verify' => 1], 200));
    }
}
