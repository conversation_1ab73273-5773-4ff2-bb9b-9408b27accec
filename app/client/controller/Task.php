<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/7/12 9:17
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\client\exception\PopUpWindowException;
use app\client\service\TaskSelect;
use app\client\model\{Config, Task as MTask, UserVersion};
use app\client\model\Platform;
use app\client\service\Task as STask;
use app\client\service\User as SUser;
use app\client\service\UserHistory as SUserHistory;
use app\client\service\YunOpen;
use app\client\validate\TaskSignUp;
use app\common\exception\ExceptionCode;
use app\common\service\TaskView\SelfPlatform;
use app\common\service\TaskView\TaskViewEscalation;
use app\Log;
use Rabbit;
use think\Exception;
use think\exception\ValidateException;
use think\response\Json;

class Task extends Base
{
    protected $middleware = [
        'checkToken' => ['only' => ['sign_up', 'sign_up_tip', 'get_cash_back_amount', 'get_cash_back_amount_v2']]
    ];

    /**
     * 获取活动详情
     * @return Json
     * @throws \Exception
     */
    public function get_task(): Json
    {
        $id = $this->request->get('id');
        $storeId = $this->request->get('store_id');
        $statistics = $this->request->get('statistics', 1);
        $position = $this->request->get(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $params = $this->request->get(['tag' => null, 'rush' => false, 'brand_id' => null, 'from' => 0]);
        $userId = $this->getUserByToken();

        // 获取这个店铺的任务
        if (!empty($storeId)) {
            $id = MTask::where('store_id', $storeId)
                ->where('status', MTask::IN_PROGRESS)
                ->where('task_start_time', '>=', date('Y-m-d 00:00:00'))
                ->where('task_end_time', '<=', date('Y-m-d 23:59:59'))
                ->value('id');
        }

        if (empty($id) && empty($storeId)) {
            throw new Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }

        $task = invoke([STask::class, 'getTaskDetail'], [$userId, $id, $position['lat'], $position['lng'], $params]);
        /** @var SelfPlatform $escalationClient */
        $escalationClient = app(SelfPlatform::class);
        $taskViewEscalation = new TaskViewEscalation($escalationClient);
        $taskViewEscalation->publish(TaskViewEscalation::PAGE_VIEWS, $task['id']);
        if (user_client_improve_v_compare() && !empty($userId)) {
            invoke([SUserHistory::class, 'addFootprintList'], [$userId, [
                'task_id' => $id,
                'platform_abbr' => $task['platform_abbr'],
                'store_name' => $task['store_name'],
                'task_cover' => $task['task_cover'],
                'store_id' => $task['store_id']
            ]]);
        }
//        unset($task['store_id']);
        $data = ['task' => $task];
        $returnData = aesEncryption($data);
        return json(HBKReturn('success', $returnData, 200));
    }

    /**
     * 活动规则
     * @return Json
     */
    public function task_detailed_rules(): Json
    {
        $id = $this->request->get('id');
        $platform_id = MTask::get_value('task_type', [
            'id' => $id,
            'data_state' => 0
        ]);
        $task_detailed_rules = Platform::get_value('task_detailed_rules_old', [
            'id' => $platform_id,
            'data_state' => 0
        ]);
        $task_detailed_rules = explode(PHP_EOL, $task_detailed_rules);
        $data = compact('task_detailed_rules');
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 报名
     *
     * @return Json
     * @throws PopUpWindowException
     * @throws \Exception
     */
    public function sign_up(): Json
    {
        limitLowVersionSignUp();
        // 获取参数
        $taskId = $this->request->post('task_id');
        // 是否是大牌专享
        $isBrand = $this->request->post('is_brand', 0);
        // 是否是提前抢单
        $isAdvanced = $this->request->post('is_advance', 0);
        // 手动使用卡券和红包
        $redEnvelopeId = (int)$this->request->post('red_envelope_id', 0);
        $couponId = (int)$this->request->post('coupon_id', 0);
        $position = $this->request->post(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $mark = $this->request->post('mark', '');
        $platform = $this->request->platform;
        $user = $this->request->user;
        $version = getCurrentVersion();
        $storeId = MTask::where('id', '=', $taskId)->value('store_id');

        try {
            $this->validate(['user' => $user, 'task_id' => $taskId, 'store_id' => $storeId], TaskSignUp::class);
            $task = invoke([STask::class, 'validateSingUpByTask'], [
                $user, $taskId, $platform, $isBrand, $isAdvanced, $redEnvelopeId, $couponId,
            ]);
            $order = invoke([STask::class, 'createOrder'], [
                $user, $task, $platform, $task['sign_up_is_brand'], $task['sign_up_is_advanced'], $redEnvelopeId, $couponId
            ]);
        } catch (ValidateException $e) {
            //所有验证类的报错，重新自定义code丢给前端
            throw new PopUpWindowException($e->getMessage());
        }

        //异步更新用户和代理城市的关系
        invoke([SUser::class, 'saveUserAgent'], [$user['id'], $order['agent_id']]);

        $config = [
            'exchange_name' => Rabbit::EXT_ORDER,
            'exchange_type' => Rabbit::DIRECT,#直连模式
            'queue_name' => Rabbit::QUEUE_TASK_REGISTRATION,
            'route_key' => Rabbit::ROUTE_TASK_REGISTRATION
        ];
        $data = json_encode(['order_id' => (int)$order->id], 256);
        Rabbit::pushMessage($data, $config);

        /** @var \app\common\implement\rpc\Task $pay */
        $pay = app(\app\common\implement\rpc\Task::class);
        try {
            $pay->afterSignUp($order['id'], $order['order_status'], $platform,
                $position['lng'], $position['lat'], $mark, $version);
        } catch (\Exception|\Error $e) {
            Log::error('AfterSignUpFailed', $e, '报名后置操作java接口报错', $order['id'], [
                'orderStatus' => $order['order_status'],
                'platform' => $platform,
                'lng' => $position['lng'],
                'lat' => $position['lat'],
                'mark' => $mark
            ]);
        }

        return json(HBKReturn('success', ['order_id' => (int)$order->id], 200));
    }

    public function sign_up_tip()
    {
        $user = $this->request->user;
        $isShow = false;
        $text = '';
        // 1.满足走云账户的条件 2.还未实名认证 3.没有弹出过这个弹窗
        $isYunOpen = invoke([YunOpen::class, 'checkSplitScoreYunOpen'], [getCurrentVersion(), $user['create_time'], $user['id']]);

        if ($isYunOpen && empty($user['identity_card_id'])) {
            // 一个用户只提醒一次，不管这个用户是否切换平台
            $userVersion = UserVersion::where('user_id', '=', $user['id'])
                ->where('scene', '=', UserVersion::SIGN_UP_TIP_SCENE)
                ->find();

            if (empty($userVersion)) {
                UserVersion::insert([
                    'user_id' => $user['id'],
                    'version' => getCurrentVersion(),
                    'platform' => getCurrentPlatform(),
                    'scene' => UserVersion::SIGN_UP_TIP_SCENE
                ]);
                $isShow = true;
                $text = Config::getValueByKey('split_score_yun_sign_up_tip');
                $replaceTag = Config::getValueByKey('high_light_replace_tag');
                $replaceTag = json_decode($replaceTag, true);
                $text = str_replace('{highlightStart}', $replaceTag['highlightStart'], $text);
                $text = str_replace('{highlightEnd}', $replaceTag['highlightEnd'], $text);
            }
        }
        return json(HBKReturn('success', ['is_show' => $isShow, 'text' => $text], 200));
    }

    /**
     * 获取返现金额
     *
     * @return Json
     * @throws \Exception
     */
    public function get_cash_back_amount(): Json
    {
        // 获取参数
        $params = $this->request->get([
            'task_id' => null, 'red_envelope_id' => 0, 'coupon_id' => 0, 'platform_abbr', 'lat' => 0, 'lng' => 0
        ]);
        $user = $this->request->user;
        $data = invoke([STask::class, 'getTaskCashBackAmount'], [$user['id'], $params]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 获取返现金额(选择红包和卡券)
     * @return Json
     */
    public function get_cash_back_amount_v2()
    {
        // 获取参数
        $params = $this->request->get([
            'task_id' => null,
            'red_envelope_id' => 0,
            'coupon_id' => 0,
            'platform_abbr',
            'lat' => 0,
            'lng' => 0,
            'store_id' => null,
            'tag' => null,
            'rush' => false,
            'brand_id' => null,
            'from' => 0
        ]);
        $user = $this->request->user;
        $data = invoke([TaskSelect::class, 'getTaskCashbackText'], [$user['id'], $params]);
        return json(HBKReturn('success', $data, 200));
    }
}
