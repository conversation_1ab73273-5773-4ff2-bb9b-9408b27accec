<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/8/16 10:08
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\client\model\ClientUserVisitRecord;
use app\client\model\User;
use app\client\service\Position;
use app\common\model\ClientVisitLog;

class Visit extends Base
{
    public function count_visits(): \think\response\Json
    {
        $userId = $this->getUserByToken();
        $params = $this->request->post(['platform' => 'web', 'mark' => '', 'lng' => 0, 'lat' => 0, 'uri' => '',
            'user_device_cid' => '', 'ad_code' => 0, 'p_mark' => '', 'page' => '']);
        $url = $this->request->header()['referer'] ?? "";
        if (!empty($url)) {
            $url = $url . '#/' . $params['uri'];
        }
        $ip = get_client_ip();
        // $result = invoke([Position::class, 'geocoder'], [$params['lat'], $params['lng']]);
        $address = '获取地址失败';
        $hour = date('Y-m-d H');
        $day = date('Y-m-d');
        $time = date('Y-m-d H:i:s');
        $agentId = invoke([Position::class, 'getAgentIdByAdCode'], [$params['ad_code']]);
        $data = [
            'agent_id' => (int)$agentId,
            'user_device_cid' => $params['user_device_cid'],
            'mark' => $params['mark'],
            'platform' => $params['platform'],
            'ip' => $ip,
            'url' => $url,
            'longitude' => $params['lng'],
            'latitude' => $params['lat'],
            'address' => $address,
            'visit_hour' => $hour,
            'visit_time' => $time,
            'visit_day' => $day,
        ];
        //invoke(['app\common\service\Dashboard','change_visit_num'],[$ip]);
        $clientUserVisitRecord = ClientUserVisitRecord::create($data);
        $saveClientUserVisitLastData = [
            'agent_id' => (int)$agentId,
            'platform' => $params['platform'],
            'ip' => $ip,
            'longitude' => $params['lng'],
            'latitude' => $params['lat'],
            'user_device_cid' => $params['user_device_cid'],
            'create_date' => date("Y-m-d"),
            'user_id' => $userId ?? 0,
        ];
        if (!empty($params['p_mark'])) {
            $newData = [
                'agent_id' => (int)$agentId,
                'user_device_cid' => $params['user_device_cid'],
                'user_id' => $userId ?? 0,
                'mark' => $params['mark'],
                'p_mark' => $params['p_mark'],
                'page' => $params['page'],
                'platform' => $params['platform'],
                'ip' => $ip,
                'url' => $url,
                'longitude' => $params['lng'],
                'latitude' => $params['lat'],
                'address' => $address,
                'visit_hour' => $hour,
                'visit_time' => $time,
                'visit_day' => $day,
            ];
            ClientVisitLog::create($newData);
        }
        invoke(['\app\client\service\Visit', 'saveClientUserVisitLast'], [$saveClientUserVisitLastData]);
        //用户之前没有保存设备号，或者设备号变更，更新用户表的设备编号
        if (!empty($params['user_device_cid']) && !empty($userId)) {
            User::update(["user_device_cid" => $params['user_device_cid'], "id" => $userId]);
        }
        return json(HBKReturn('success', [], 200));
    }
}
