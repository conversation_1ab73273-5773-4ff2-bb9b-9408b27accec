<?php


namespace app\client\controller;


use app\BaseController;
use app\client\service\wechat\Applet;
use app\client\service\wechat\AppletAir;
use app\common\facade\Redis;
use app\common\service\Position;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelHigh;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\RoundBlockSizeMode\RoundBlockSizeModeEnlarge;
use Endroid\QrCode\Writer\PngWriter;
use app\client\model\Config;

class Qr extends BaseController
{
    protected $middleware = [
        'checkToken' => ['only' => 'get_user_promotion_qr_url'],
    ];

    public function wechat_qr_url(): \think\response\Json
    {
        $lat = (float)$this->request->get('lat', 0);
        $lng = (float)$this->request->get('lng', 0);
        $geofence = invoke([Position::class, 'getGeoResultByDb'], [$lat, $lng, 'getDistrictQrCode']);
        $data = [
            'title' => $geofence['name'],
            'url' => $geofence['qr_url']
        ];
        return json(HBKReturn('success', $data, 200));
    }

    public function mini_wechat_qr(): \think\response\Json
    {
        $lat = (float)$this->request->get('lat', 0);
        $lng = (float)$this->request->get('lng', 0);
        // 返回小程序的配置信息
        return json(HBKReturn('success', [
            'appId' => 'wx70b6bc21adce9cf4',
            'originalId' => 'gh_540b6c0a4b3d',
            'path' => "/package/miniprogram/mp_weixin_welfare_qun?lat={$lat}&lng=$lng",
        ], 200));
    }

    /**
     * 获取用户二维码图片链接
     * @return \think\response\Json
     */
    public function get_user_promotion_qr_url(): \think\response\Json
    {
        $promotion_poster_background_picture = Config::get_column('value', [
            'group' => 'promotion_poster_background_picture'
        ]);

        $user = $this->request->user;

        if (getCurrentPlatform() === 'mp-weixin') {
            $user_promotion_qr_code = $this->request->domain() . '/applet_qr/' . $user->id;
        } elseif (getCurrentPlatform() === 'mp-weixin-air') {
            $user_promotion_qr_code = $this->request->domain() . '/applet_air_qr/' . $user->id;
        } else {
            $user_promotion_qr_code = $this->request->domain() . '/qr/' . $user->id;
        }

        $user_promotion_save_poster_qr_code = $user_promotion_qr_code . '/savePosterImg'; // 保存海报图片
        $user_promotion_friend_circle_qr_code = $user_promotion_qr_code . '/posterFriendCircle'; // 分享朋友圈
        $user_promotion_wechat_friend_qr_code = $user_promotion_qr_code . '/posterWechatFriend'; // 分享微信好友

        $data = [
            'user_promotion_qr_code' => $user_promotion_save_poster_qr_code,
            'user_promotion_friend_circle_qr_code' => $user_promotion_friend_circle_qr_code,
            'user_promotion_wechat_friend_qr_code' => $user_promotion_wechat_friend_qr_code,
            'promotion_poster_background_picture' => $promotion_poster_background_picture,
            'share_title' => Config::getValueByKey('share_title'),
            'share_desc' => Config::getValueByKey('share_desc'),
            'share_logo' => Config::getValueByKey('share_logo'),
            'share_url' => env('WEB_BASE_URL', 'https://xxyx.web.xxyx.ltd/#/') . '?uid=' . $user['id'],
            'source_key' => [
                'save_poster_img' => 'savePosterImg',
                'poster_friend_circle' => 'posterFriendCircle',
                'poster_wechat_friend' => 'posterWechatFriend'
            ],
            'share_base_url' => $user_promotion_qr_code,
        ];
        return json(HBKReturn('success', $data, 200));
    }

    public function promoter_image($uid, $source = 'savePosterImg')
    {
        $host = env('WEB_BASE_URL');
        $writer = new PngWriter();
        $qrCode = QrCode::create($host . '?uid=' . $uid . '&source=' . $source)
            ->setEncoding(new Encoding('UTF-8'))
            ->setErrorCorrectionLevel(new ErrorCorrectionLevelHigh)
            ->setSize(300)
            ->setMargin(20)
            ->setRoundBlockSizeMode(new RoundBlockSizeModeEnlarge())
            ->setForegroundColor(new Color(0, 0, 0))
            ->setBackgroundColor(new Color(255, 255, 255));

        $result = $writer->write($qrCode);
        $water = $result->getImage();
        $mime = $result->getMimeType();
//        $pathName = 'https://image.xiaoxiaoyouxuan.com/admin/qr_backgroup_image.png';
//        $info = @getimagesize($pathName);
//        //设置图像信息
//        $info = [
//            'width' => $info[0],
//            'height' => $info[1],
//            'type' => image_type_to_extension($info[2], false),
//            'mime' => $info['mime'],
//        ];
//        //打开图像
//        $fun = "imagecreatefrom{$info['type']}";
//        $im = @$fun($pathName);
//        $im_cut = ImageCreateTrueColor(750, 970);
//        imagecopyresampled($im_cut, $im, 0, 0, 0, 120, 750, 970, 750, 970);
//        imagecopyresized($im_cut, $water, 181, 430, 0, 0, 390, 390, imagesx($water), imagesy($water));
//        imagedestroy($water); //销毁原图
//        imagedestroy($im); //销毁原图
        header('Content-Type:' . $mime);
        header('Access-Control-Allow-Origin:*');
        header("Cache-Control: public");
//        header("Pragma: cache");
//
//        $offset = 7 * 60 * 60 * 24; // cache 1 week
//        $ExpStr = "Expires: " . gmdate("D, d M Y H:i:s", time() + $offset) . " GMT";
//        header($ExpStr);

        $quality = 100;
        if ($mime == 'image/png') $quality = 9;        //输出质量,JPEG格式(0-100),PNG格式(0-9)
//        $getImgInfo = "imagepng";
        imagepng($water, null, $quality);    //2.将图像输出到浏览器或文件。如: imagepng ( resource $image )
        imagedestroy($water);
        die;
    }

    /**
     * 返回推广人的微信小程序图片
     * @param $uid
     * @return \think\response\Json
     */
    public function applet_promoter_image($uid, $source = 'savePosterImg')
    {
        $token = invoke([Applet::class, 'getToken']);
        invoke([Applet::class, 'getQrCode'], [$token, $uid, $source]);
        return json(HBKReturn('success', [], 200));
    }

    /**
     * 返回推广人的微信小程序图片(air)
     * @param $uid
     * @return \think\response\Json
     */
    public function applet_air_promoter_image($uid, $source = 'savePosterImg')
    {
        $token = invoke([AppletAir::class, 'getToken']);
        invoke([AppletAir::class, 'getQrCode'], [$token, $uid]);
        return json(HBKReturn('success', [], 200));
    }
}
