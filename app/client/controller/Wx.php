<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/7/26 9:25
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\client\service\WechatService;
use app\common\exception\ExceptionCode;
use app\common\service\WxWebConfig;
use think\exception\ValidateException;
use app\client\model\Config;

class Wx extends Base
{
    protected $middleware = [
        'checkToken' => ['only' => 'get_subscription_notification_template_id']
    ];
    /**
     * @return \think\response\Json
     * @throws \app\admin\exception\CurrentException
     */
    public function web_sign(): \think\response\Json
    {
        $url = $this->request->get('url');
        $scene = $this->request->get('scene', 'app');

        if (empty($url)) {
            throw new ValidateException(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }

        /** @var WxWebConfig $wxWebConfig */
        $scene = invoke([WechatService::class, 'choseWechatOffcialAccountApp']);
        $wxWebConfig = app(WxWebConfig::class, [$scene]);
        $data = $wxWebConfig->signCalc($url);
        return json(HBKReturn('success', $data, 200));
    }

    public function get_subscription_notification_template_id(): \think\response\Json
    {
        $platform = $this->request->platform;
        if ($platform !== 'wx-web') {
            throw new ValidateException('登录平台不正确');
        }
        $templates = Config::getValueByKey('wechat_subscription_notification_template_id');
        $data = compact('templates');
        return json(HBKReturn('success', $data, 200));
    }
}