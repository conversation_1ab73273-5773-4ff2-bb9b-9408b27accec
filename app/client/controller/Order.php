<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/7/13 10:15
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\admin\model\ActivityBlackList;
use app\admin\model\OrderCancelReason;
use app\client\facade\SubmitOrderFactory;
use app\client\model\Agent as MAgent;
use app\client\model\Config;
use app\client\model\LiveActivityRecord;
use app\client\model\MiddlemanConfig;
use app\client\model\Order as MOrder;
use app\client\model\OrderAuditRecord;
use app\client\model\OrderCoupon;
use app\client\model\OrderEle;
use app\client\model\OrderInfo;
use app\client\model\OrderInviteBoostTask;
use app\client\model\OrderMt;
use app\client\model\OrderMtCode;
use app\client\model\OrderMtGp;
use app\client\model\OrderSqs;
use app\client\model\OrderTime;
use app\client\model\Platform;
use app\client\model\Rebate;
use app\client\model\Seller;
use app\client\model\SellerBill;
use app\client\model\SellerBillTask;
use app\client\model\StoreReliveSwitch;
use app\client\model\Task;
use app\client\model\UserCoupon;
use app\client\model\UserHelpReturnAmount;
use app\client\service\BusinessPlatform as BusinessPlatformService;
use app\client\service\DelayedPayment;
use app\client\service\Order as SOrder;
use app\client\service\order\order_relive\OrderReliveForList;
use app\client\service\OrderEnterMethod;
use app\client\service\OrderHelpReturnService;
use app\client\service\OrderTip;
use app\common\exception\ExceptionCode;
use app\common\model\OrderGeneral;
use app\common\model\OrderSuperRebateInfo;
use app\common\model\VersionCompatible;
use app\common\service\Cos;
use app\common\service\HttpRpc;
use app\common\service\Order as COrder;
use app\Log;
use think\Exception;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\Event;
use think\response\Json;

class Order extends Base
{
    protected $middleware = [
        'checkToken' => ['except' => [
            'get_in_progress_order_count_by_user',
            'get_in_progress_order_count_by_user_default',
            'get_order_list_status'
        ]]
    ];

    /**
     * 获取订单列表选择状态
     *
     * @request method:GET desc:请求信息
     * @header name:HTTP-TOKEN type:string desc:token
     *
     * @return Json
     */
    public function get_order_list_status(): Json
    {
        $statusList = MOrder::getStatusList();
        $data = ['list' => $statusList];
        return json(HBKReturn('success', $data, 200));
    }

    public function get_order_list_by_user(): Json
    {
        $user = $this->request->user;
        $status = $this->request->get('status', 0, 'intval');
        $version = getCurrentVersion();
        $groupBuyMinVersion = VersionCompatible::getMinVersionByName('group_buy_v');
        $inStoreGpMinVersion = VersionCompatible::getMinVersionByName('in_store_gp_v');
        $jdMsMinVersion = VersionCompatible::getMinVersionByName('jd_ms_v');
        $options = $this->request->get(['page' => 0, 'limit' => 10]);
        $options['field'] = ['id', 'order_no', 'platform_id' => 'platform', 'task_cover', 'store_name', 'meal_price',
            'cash_back_amount', 'order_status', 'cancel_status', 'create_time', 'order_rejection_reasons', 'is_expired',
            'payment_status', 'is_praise', 'praise_demand', 'task_id', 'user_id', 'machine_audit_status', 'agent_id'];
        $options['order'] = ['create_time' => 'desc', 'id' => 'desc'];
        $where = [
            ['data_state', '=', 0],
            ['user_id', '=', $user['id']]
        ];

        $disabledPltIds = [];

        if ($version < $groupBuyMinVersion) {
            $disabledPltIds = Platform::getGroupBuyAbbreviationIdsArray();
        }

        if ($version < $inStoreGpMinVersion) {
            $disabledPltIds[] = Platform::getMtGpId();
        }

        if ($version < $jdMsMinVersion) {
            $disabledPltIds[] = Platform::getJdMsId();
        }

        if (!empty($disabledPltIds)) {
            $where[] = ['platform_id', 'not in', $disabledPltIds];
        }

        switch ($status) {
            case 2:
                $where[] = ['payment_status', '=', 2];
                break;
            case 3:
                $where[] = ['order_status', '=', -1];
                break;
            case 1:
            default:
                $where[] = ['order_status', 'in', [-4, -3, -2, 1, 2, 3, 4, 5, 6]];
                $where[] = ['payment_status', 'in', [0, 1, 3]];
                break;
        }

        $platformList = Platform::where('data_state', 0)
            ->field(['id', 'platform_name', 'display_name', 'platform_abbreviation', 'platform_logo'])
            ->select()
            ->toArray();
        $platformMap = array_columns($platformList, 'id');
        $orderCodeList = array_columns(OrderMtGp::getOrderCodeList(), 'code', ['name', 'color']);
        $orderStatusList = array_columns(MOrder::getOrderStatusList(), 'id', ['name', 'color']);
        $cancelStatusList = array_column(MOrder::getCancelStatusList(), 'name', 'id');
        $paymentStatusList = array_columns(MOrder::getPaymentStatusList(), 'id', ['name', 'color']);
        $orderList = MOrder::where($where)
            ->whereTime('create_time', '>=', '-90 days')
            ->field($options['field'])
            ->page($options['page'])
            ->limit($options['limit'])
            ->order($options['order'])
            ->select();
        $orderReuploadMap = $orderCashbackAmountMap = $orderEleMap = $orderMtMap = $orderMtGpMap = $orderDeductionAmountMap =
        $orderCancelReasonMap = $orderCouponMap = $orderReliveCouponMap = $taskTotalQuotaList =
        $taskApplicantsQuotaList = $orderUserReliveCouponMap = $orderCancelAuditMap = $isPrePayList =
        $sellerBillTaskList = $sellerBillList = $otherOrderList = $orderSuperRebateInfoMap = $orderRedEnvelopeAmountMap = $orderInfoList = [];
        // 店铺和中间人复活开关相关数据
        $taskStoreList = [];
        $taskSellerIdList = [];
        $taskPromoterIdList = [];
        $storeReliveTypeList = [];
        $storeReliveSwitchList = [];
        $middlemanList = [];
        $middleConfigStatusList = [];
        $sellerReliveSwitchList = [];
        $reviveOrderCouponTime = Config::getValueByKey('revive_order_coupon_time');
        $reviveOrderCouponTime = $reviveOrderCouponTime ? json_decode($reviveOrderCouponTime, true) : [];
        $revivedOrderValidTimeMinute = $reviveOrderCouponTime['revivedOrderValidTimeMinute'] ?? '';
        $reviveOrderTimeLimitHour = $reviveOrderCouponTime['reviveOrderTimeLimitHour'] ?? '';
        $selectCouponV = select_coupon_v();

        $taskIds = $orderList->column('task_id');
        $orderIds = in_array($status, [1, 2, 3]) ? $orderList->column('id') : [];
        if (!empty($orderIds)) {
            $orderMtList = OrderMt::whereIn('order_id', $orderIds)
                ->select()
                ->toArray();
            $orderMtGpList = OrderMtGp::whereIn('order_id', $orderIds)
                ->select()
                ->toArray();
            $orderEleList = OrderEle::whereIn('order_id', $orderIds)
                ->select()
                ->toArray();
            $orderInfoList = OrderInfo::whereIn('id', $orderIds)
                ->field(['reupload_num', 'order_deduction_amount', 'id', 'current_cashback_amount', 'red_envelope_amount'])
                ->select()
                ->toArray();

            $orderMtMap = array_column($orderMtList, null, 'order_id');
            $orderMtGpMap = array_column($orderMtGpList, null, 'order_id');
            $orderEleMap = array_column($orderEleList, null, 'order_id');
            $orderCashbackAmountMap = array_column($orderInfoList, 'current_cashback_amount', 'id');

            if ($status == 1) {
                $orderReuploadMap = array_column($orderInfoList, 'reupload_num', 'id');
                $orderDeductionAmountMap = array_column($orderInfoList, 'order_deduction_amount', 'id');
            }

            // 已取消
            if (in_array($status, [2, 3])) {
                $orderCancelReasonMap = OrderCancelReason::whereIn('order_id', $orderIds)
                    ->where('data_state', '=', 0)
                    ->group('order_id')
                    ->order('id desc')
                    ->column('cancel_reason', 'order_id');
            }
        }

        // 获取延迟上传券信息
        // 订单是否已经使用过券
        $memberVersion = false;
        if ($status == 1) {
            $delayedWhere = [
                ['user_id', '=', $user['id']],
                ['coupon_type', '=', UserCoupon::DELAYED_UPLOAD_COUPON],
                ['data_state', '=', 0],
            ];
            $memberVersion = member_version_compare();
            $orderCouponMap = OrderCoupon::whereIn('order_id', $orderIds)
                ->where($delayedWhere)
                ->field(['order_id', 'user_coupon_id'])
                ->column('user_coupon_id', 'order_id');
            $orderReliveCouponMap = OrderCoupon::where('user_id', '=', $user['id'])
                ->whereIn('order_id', $orderIds)
                ->where('coupon_type', '=', UserCoupon::RELIVE_COUPON)
                ->where('data_state', '=', 0)
                ->column('create_time', 'order_id');
            // 取消订单的取消时间
            $orderCancelAuditMap = OrderAuditRecord::whereIn('order_id', $orderIds)
                ->where('admin_id', '<', 0)
                ->where('target_status', '=', -1)
                ->column('create_time', 'order_id');
        }

        if ($status == 3) {
            // 取消的订单能否复活
            $orderReliveCouponMap = OrderCoupon::where('user_id', '=', $user['id'])
                ->whereIn('order_id', $orderIds)
                ->where('coupon_type', '=', UserCoupon::RELIVE_COUPON)
                ->where('data_state', '=', 0)
                ->column('create_time', 'order_id');
            // 取消订单的取消时间
            $orderCancelAuditMap = OrderAuditRecord::whereIn('order_id', $orderIds)
                ->where('admin_id', '<', 0)
                ->where('target_status', '=', -1)
                ->column('create_time', 'order_id');
            $taskList = Task::whereIn('id', $taskIds)
                ->field(['id', 'task_total_quota', 'task_applicants_quota', 'is_pre_pay', 'store_id', 'seller_id',
                    'promoter_id'])
                ->select();
            $taskTotalQuotaList = $taskList->column('task_total_quota', 'id');
            $taskApplicantsQuotaList = $taskList->column('task_applicants_quota', 'id');
            $isPrePayList = $taskList->column('is_pre_pay', 'id');
            $sellerBillTaskList = SellerBillTask::whereIn('task_id', $taskIds)
                ->where('data_state', '=', 0)->column('seller_bill_id', 'task_id');
            $sellerBillIds = array_values($sellerBillTaskList);
            $sellerBillList = SellerBill::whereIn('id', $sellerBillIds)
                ->where('data_state', '=', 0)->column('pay_status', 'id');
            // 移除数组中官方活动任务id是0的数据
            $element = 0;
            $taskIds = array_filter($taskIds, function ($value) use ($element) {
                return $value != $element;
            });
            $otherOrderMap = MOrder::whereIn('task_id', $taskIds)
                ->where('user_id', '=', $user['id'])
                ->where('order_status', '<>', -1)
                ->field(['task_id', 'user_id'])
                ->select();
            $otherOrderMap->each(function ($otherOrder) use (&$otherOrderList) {
                $otherOrderList[$otherOrder['task_id']][] = $otherOrder['user_id'];
            });

            // 店铺和中间人复活开关相关数据
            $taskStoreList = $taskList->column('store_id', 'id');
            $taskSellerIdList = $taskList->column('seller_id', 'id');
            $taskPromoterIdList = $taskList->column('promoter_id', 'id');
            $storeReliveSwitch = StoreReliveSwitch::whereIn('store_id', $taskList->column('store_id'))
                ->where('data_state', '=', 0)
                ->field(['type', 'switch', 'store_id'])
                ->select();
            $storeReliveTypeList = $storeReliveSwitch->column('type', 'store_id');
            $storeReliveSwitchList = $storeReliveSwitch->column('switch', 'store_id');
            $sellerIds = $taskList->column('seller_id');
            $middlemanRebateList = Rebate::whereIn('seller_id', $sellerIds)
                ->whereIn('sale_id', $taskList->column('promoter_id'))
                ->where('data_state', '=', 0)
                ->field(['seller_id', 'sale_id', 'middleman_id'])
                ->select();
            $middlemanRebateList->each(function (&$item) {
                $item['seller_sale'] = $item['seller_id'] . '-' . $item['sale_id'];
            });
            $middlemanList = $middlemanRebateList->column('middleman_id', 'seller_sale');
            $middlemanIds = $middlemanRebateList->column('middleman_id');
            $middleConfigStatusList = MiddlemanConfig::whereIn('middleman_id', $middlemanIds)
                ->where('config_code', '=', 'STORE_REVIVE_SWITCH')
                ->where('is_delete', '=', 0)
                ->column('config_status', 'middleman_id');
            $sellerReliveSwitchList = Seller::whereIn('id', $sellerIds)->where('data_state', '=', 0)->column('relive_switch', 'id');

        }
        // 超级返利券金额
        $orderSuperRebateInfoMap = OrderSuperRebateInfo::whereIn('order_id', $orderIds)->where('data_state', '=', 0)->column('super_rebate_amount', 'order_id');
        $orderRedEnvelopeAmountMap = array_column($orderInfoList, 'red_envelope_amount', 'id');

        // 总
        $isGlobalOrderTimeoutLimitRuleEnable = invoke([COrder::class, 'isGlobalOrderTimeoutLimitRuleEnable'], [$user['id']]);
        // 个人
        $isUserOrderTimeoutLimitRuleEnable = invoke([COrder::class, 'isUserOrderTimeoutLimitRuleEnable'], [$user['id']]);
        $isUserOrderTimeoutLimit = invoke([COrder::class, 'isUserOrderTimeoutLimit'], [$user['id']]);
        $orderTimeoutReleaseTimes = Config::getValueByKey('order_timeout_release_times');
        // 订单对应的代理区域
        $agentIds = $orderList->column('agent_id');
        $agentEnableOrderTimeoutLimitRuleMap = MAgent::whereIn('id', $agentIds)->column('enable_order_timeout_limit_rule', 'id');
        $orderMinutes = Config::getValueByKey('order_timeout_minutes');
        $orderTimoutV = order_timout_v();

        $version = getCurrentVersion();
        $orderTaskRatioMap = invoke([\app\client\service\Task::class, 'getTaskRatioMap'], [$taskIds]);

        $rText = '先领红包再下单，多省3-9元并免订单检测';
        $pltRLinkMap = invoke([BusinessPlatformService::class, 'getPlatformRedirectRedEnvelopeInfo'], [$user['id']]);
        $userActivityBlacklist = ActivityBlacklist::where('user_id', $user['id'])
            ->where('type', ActivityBlacklist::ORDER_TIME_LIMIT)
            ->where('data_state', 0)
            ->find();
        $mtCodeMap = OrderMtCode::whereIn('order_id', $orderIds)
            ->column('code', 'order_id');

        $liveActivityMapping = LiveActivityRecord::where('order_id', 'in', $orderIds)
            ->where('is_delete', '=', 0)
            ->where('status', '=', 0)
            ->column('live_activity_id', 'order_id');

        $sqsExists = OrderSqs::where('order_id', 'in', $orderIds)->column('order_id');
        $isFirstRelive = invoke(["app\client\service\Order", "checkHistoryUserOrderRelive"], [$user]);
        $orderInviteBoostConfig = invoke([OrderHelpReturnService::class, 'getOrderInviteBoostConfig'], []);
        $helpReturnAmountList = OrderInviteBoostTask::whereIn('order_id', $orderList->column('id'))
            ->where('is_delete', '=', 0)
            ->where('point_status', '=', 1)
            ->column('current_amount', 'order_id');
        $userHelpReturnAmountIsLimit = UserHelpReturnAmount::where('user_id', '=', $user['id'])
            ->where('data_state', '=', 0)
            ->value('is_limit');

        $list = $orderList->each(function (&$item) use (
            $platformMap,
            $orderStatusList,
            $cancelStatusList,
            $paymentStatusList,
            $orderReuploadMap,
            $orderDeductionAmountMap,
            $orderCancelReasonMap,
            $orderCouponMap,
            $orderCashbackAmountMap,
            $memberVersion,
            $orderMtMap,
            $orderMtGpMap,
            $orderCodeList,
            $orderEleMap,
            $orderSuperRebateInfoMap,
            $orderRedEnvelopeAmountMap,
            $orderReliveCouponMap,
            $orderCancelAuditMap,
            $orderTaskRatioMap,
            $reviveOrderTimeLimitHour,
            $revivedOrderValidTimeMinute,
            $taskTotalQuotaList,
            $taskApplicantsQuotaList,
            $isPrePayList,
            $sellerBillTaskList,
            $sellerBillList,
            $otherOrderList,
            $version,
            $rText,
            $pltRLinkMap,
            $isUserOrderTimeoutLimitRuleEnable,
            $isGlobalOrderTimeoutLimitRuleEnable,
            $agentEnableOrderTimeoutLimitRuleMap,
            $orderMinutes,
            $orderTimoutV,
            $userActivityBlacklist,
            $selectCouponV,
            $taskStoreList,
            $taskSellerIdList,
            $taskPromoterIdList,
            $storeReliveTypeList,
            $storeReliveSwitchList,
            $middlemanList,
            $middleConfigStatusList,
            $sellerReliveSwitchList,
            $isUserOrderTimeoutLimit,
            $orderTimeoutReleaseTimes,
            $mtCodeMap,
            $liveActivityMapping,
            $sqsExists,
            $user,
            $isFirstRelive,
            $orderInviteBoostConfig,
            $helpReturnAmountList,
            $userHelpReturnAmountIsLimit
        ) {
            $platform = $platformMap[$item['platform']];
            $item['platform_abbr'] = $platform['platform_abbreviation'];
            $item['platform'] = $platform['display_name'];
            $item['display_name'] = $platform['display_name'];
            $item['platform_logo'] = $platform['platform_logo'];
            $item['cancel_reason'] = null;
            // 倒计时
            $item['deadline'] = null;
            $item['status'] = $orderStatusList[$item['order_status']];
            $item['status_code'] = $item['order_status'];
            $orderRejectionReasons = $item['order_rejection_reasons'];
            $item['order_status'] === -2 || $item['order_rejection_reasons'] = null;

            $item['praise_demand_info'] = invoke(['app\client\service\Order', 'getPraiseDemandInfo'], [$item['is_praise'], $item['praise_demand']]);
            $item['praise_demand_color'] = invoke(['app\client\service\Order', 'getPraiseDemandColor'], [$item['is_praise']]);
            $item['praise_demand_tags'] = invoke(['app\client\service\Order', 'getPraiseDemandTags'], [$item['is_praise']]);
            $item['is_praise'] = getCurrentIsPraise($item['is_praise']);
            $orderRedEnvelopeAmount = $orderRedEnvelopeAmountMap[$item['id']] ?? 0;
            // app大改版1.0中需求是：满x返y元， (y = 返现金额 + 红包金额 + 超级返利券金额)
            $item['rule'] = '满' . (float)$item['meal_price'] . '返' . (float)bcadd($item['cash_back_amount'], bcadd(
                    $orderSuperRebateInfoMap[$item['id']] ?? 0,
                    $orderRedEnvelopeAmount, 3
                ), 2);
            $item['live_activity_id'] = $liveActivityMapping[$item['id']] ?? null;
            $item['sqs'] = in_array($item['id'], $sqsExists);
            $isSelfFullReturn = false;

            // 美团官方活动
            if ($item['platform_abbr'] === Platform::PLT_MTG) {
                if (!empty($orderMtMap)) {
                    $orderMt = $orderMtMap[$item['id']];
                    if ($orderMt['type'] === 1) {
                        $item['rule'] = '满' . (float)$item['meal_price'] . '返' . (float)$item['cash_back_amount'];
                    } elseif ($orderMt['type'] === 2) {
                        $userMaxRatio = (float)bcdiv(bcmul($orderMt['user_ratio'], $orderMt['ratio'], 2), '1000000', 2);
                    } else {
                        // 统一返现
                        if ($item['order_status'] == -4) {
                            $praiseText = '由于与美团合作升级，该笔订单评价完成约24小时后返现至美团/美团外卖APP钱包余额';
                            $noPraiseText = '由于与美团合作升级，该笔订单完成约24小时后返现至美团/美团外卖APP钱包余额';
                            $item['warn_tips'] = $item['is_praise'] === 1 ? $praiseText : $noPraiseText;
                        }
                        if ($item['order_status'] == \app\client\model\Order::COMPLETED) {
                            $item['warn_tips'] = '由于与美团合作升级，本单已返现至美团/美团外卖APP钱包，请前往美团提现';
                        }
                        $userMaxRatio = (float)bcdiv($orderMt['user_ratio'], 100, 2);
                        if ($item['order_status'] == -4) {
                            $item['status'] = OrderMt::$wait_cash_back_color;
                        }
                    }
                }
            } elseif ($item['platform_abbr'] === Platform::PLT_ELEG) {
                if (!empty($orderEleMap)) {
                    $orderEle = $orderEleMap[$item['id']];
                    if ($orderEle['type'] === 1) {
                        $item['rule'] = '满' . (float)$item['meal_price'] . '返' . (float)$item['cash_back_amount'];
                    } else {
                        $userMaxRatio = (float)bcdiv(bcmul($orderEle['user_ratio'], $orderEle['ratio'], 2), '100', 2);
                    }
                }
            } elseif ($item['platform_abbr'] === Platform::PLT_MTG_GP) {
                $orderMtGp = $orderMtGpMap[$item['id']];
                if ($item['order_status'] == -4) {
                    $item['warn_tips'] = '由于与美团合作升级，该笔订单完成约24小时后返现至美团/美团外卖APP钱包余额';
                }
                if ($item['order_status'] == \app\client\model\Order::COMPLETED) {
                    $item['warn_tips'] = '由于与美团合作升级，本单已返现至美团/美团外卖APP钱包，请前往美团提现';
                }
                // 要展示总比例(核销返比例 + 反馈返比例)
                $userMaxRatio = (float)bcdiv((float)bcadd($orderMtGp['user_ratio'], $orderMtGp['user_comment_ratio'], 2), 100, 2);
                $item['praise_demand_tags'] = '';
                $item['praise_demand_info'] = '';
                if (!empty($item['praise_demand'])) {
                    $arr = explode(',', $item['praise_demand']);
                    $item['praise_demand_info'] = $arr[0] . '图' . $arr[1] . '字';
                }
                $item['mtg_gp_status_code'] = $orderMtGp['code'];
            } else {
                if (isset($orderTaskRatioMap[$item['task_id']])) {
                    $userMaxRatio = (float)bcdiv($orderTaskRatioMap[$item['task_id']], 100, 2);
                }
            }

            // 不是满返的活动，不是官方的活动=自营满返
            if (!isset($orderTaskRatioMap[$item['task_id']]) && !in_array($item['platform_abbr'], [Platform::PLT_MTG,
                    Platform::PLT_ELEG, Platform::PLT_ELEZ, Platform::PLT_MTG_GP, Platform::PLT_HTL_MT, Platform::PLT_HTL_ELE])) {
                $isSelfFullReturn = true;
            }

            if (isset($userMaxRatio)) {
                $rule = '返实付' . $userMaxRatio . '%';
                $item['rule'] = $rule;

                if ($item['order_status'] === 4 && isset($orderCashbackAmountMap[$item['id']]) && $version < 20350) {
                    $item['rule'] .= ' | 返利' . (float)$orderCashbackAmountMap[$item['id']];
                }
            }

            // 有自己的文案
            if ($item['platform_abbr'] === Platform::PLT_MTG_GP && isset($userMaxRatio)) {
                $rule = '最高返' . $userMaxRatio . '%';
                $item['rule'] = $rule;
            }

            // 后台填写取消原因：用户端展示：客服取消+后台填写的取消原因，里面和外面都这样展示；超时取消和用户主动取消不需要取消原因
            switch ($item['order_status']) {
                case 1:
                    // 可能历史的订单是没有这个数据的
                    $orderTimeObject = OrderTime::where('order_id', $item['id'])
                        ->find();
                    $submitLiveTime = $orderTimeObject['submit_live_time'] ?? 0;
                    if (in_array($item['platform_abbr'], [Platform::PLT_MTG, Platform::PLT_MTG_GP])) {
                        $seconds = $submitLiveTime > 0 ? ($submitLiveTime * 60) : 3600;
                        $finalTime = strtotime(date('Y-m-d 23:59:00', strtotime($item['create_time'])));
                        $deadlineTime = strtotime($item['create_time']) + $seconds;

                        if ($deadlineTime > $finalTime) {
                            $deadlineTime = $finalTime;
                        }

                        $item['deadline'] = date('Y-m-d H:i:s', $deadlineTime);
                        $item['deadline_text'] = '进店下单剩余时间：';
                    } else {
                        if ($selectCouponV && key_exists($item['id'], $orderCouponMap)) {
                            $item['deadline'] = date('Y-m-d 13:00:00', strtotime($item['create_time']) + 86400);
                        } else {
                            $seconds = $submitLiveTime > 0 ? ($submitLiveTime * 60) : 10800;
                            $deadlineTime = strtotime($item['create_time']) + $seconds;
                            $item['deadline'] = date('Y-m-d H:i:s', $deadlineTime);
                        }
                        $item['deadline_text'] = '上传单号剩余时间：';
                    }

                    // 总开关+城市开关+个人的超时取消限制的那个开关也得开着
                    // 若该订单使用了晓晓红包，则提示：“若订单超时未下单而被超时取消，则本单使用的晓晓红包将不会返还”
                    // 若该订单未使用晓晓红包，则提示：“若订单超时未下单而被超时取消，则下一单起需在报名30分钟内提交订单”
                    if (!Platform::isGroupBuy($item['platform_abbr']) && !Platform::isGeneral($item['platform_abbr']) && !Platform::isRepurchaseCard($item['platform_abbr']) && !Platform::isMtOfficial($item['platform_abbr']) && $orderTimoutV && $isGlobalOrderTimeoutLimitRuleEnable && $isUserOrderTimeoutLimitRuleEnable && $agentEnableOrderTimeoutLimitRuleMap[$item['agent_id']] == 1 && empty($userActivityBlacklist)) {
                        if (bccomp($orderRedEnvelopeAmount, 0, 2) === 1) {
                            $item['warn_tips'] = '若订单超时未下单而被超时取消，则本单使用的晓晓红包将不会返还';
                        } else {
                            $item['warn_tips'] = "若订单超时未下单而被超时取消，则下一单起所有订单均需在报名{$orderMinutes}分钟内提交订单";
                        }
                    }

                    // 已报名状态
                    if (!Platform::isGroupBuy($item['platform_abbr']) && !Platform::isGeneral($item['platform_abbr']) && !Platform::isRepurchaseCard($item['platform_abbr']) && !Platform::isMtOfficial($item['platform_abbr']) && $orderTimoutV && $isUserOrderTimeoutLimit) {
                        $item['warn_tips'] = "该订单已被限制下单时间为{$orderMinutes}分钟，请及时前往下单。若未来连续{$orderTimeoutReleaseTimes}单未超时取消，则可解除下单时间限制。";
                    }
                    break;
                case -1:
                    $cancelReason = '';

                    if (!empty($cancelStatusList[$item['cancel_status']])) {
                        $cancelReason = $cancelStatusList[$item['cancel_status']];

                        if (!empty($orderReliveCouponMap[$item['id']])) {
                            $cancelReason = '复活' . $cancelReason;
                        }
                    }

                    // 如果是客服取消，就要显示取消原因
                    if ($item['cancel_status'] == 4) {
                        $orderCancelReason = $orderCancelReasonMap[$item['id']] ?? '';
                        $orderRejectionReasons = !empty($orderCancelReason) ? $orderCancelReason : $orderRejectionReasons;
                        $cancelReason .= !empty($orderRejectionReasons) ? "(" . $orderRejectionReasons . ")" : '';
                    }

                    $item['cancel_reason'] = $cancelReason;

                    break;
                case 2:
                    // 需要判断时间
                    if (key_exists($item['id'], $orderCouponMap) && $memberVersion) {
                        $item['deadline'] = date('Y-m-d 13:00:00', strtotime($item['create_time']) + 86400);
                    } else {
                        if (date('H', strtotime($item['create_time'])) >= 21) {
                            $item['deadline'] = date('Y-m-d 03:00:00', strtotime($item['create_time']) + 86400);
                        } else {
                            $item['deadline'] = date('Y-m-d 23:59:59', strtotime($item['create_time']));
                        }
                    }

                    if ($item['is_expired'] === 1) {
                        $item['deadline'] = date('Y-m-d 13:00:00', strtotime($item['create_time']) + 86400);
                    }

                    if (in_array($item['platform_abbr'], [Platform::PLT_MTG, Platform::PLT_ELEZ])) {
                        $item['deadline_text'] = '完成评价剩余时间：';
                    } else {
                        if ($item['platform_abbr'] != Platform::PLT_ELEG) {
                            $item['deadline_text'] = '提交截图剩余时间：';
                        }
                    }
                    break;
                case 3:
                    if ($item['platform_abbr'] === Platform::PLT_MTG_GP) {
                        $item['warn_tips'] = '';
                        $delayCashBackTimeText = '';
                        $orderMtGp = $orderMtGpMap[$item['id']];
                        if ($orderMtGp['is_hx_cash_back'] === 1) {
                            $item['cash_back_text'] = '已返金额: ' . floatval(bcdiv($orderMtGp['hx_cash_back'], '100', 2));
                        }
                        if ($orderMtGp['delay_cash_back_time'] > 0) {
                            $delayCashBackTimeText = '约' . $orderMtGp['delay_cash_back_time'] . '小时';
                        }
                        if ($orderMtGp['code'] === 7) {
                            if ($orderMtGp['is_hx_cash_back'] === 1) {
                                $item['warn_tips'] = '核销奖励已返现至美团/大众点评钱包-余额,请前往提现。(反馈不达标原因：' . $orderMtGp['fail_reason_msg'] . '，您可前往美团修改)';
                            } else {
                                $item['warn_tips'] = '官方审核中，核销奖励在完成后' . $delayCashBackTimeText . '返现至美团/大众点评钱包-余额。(反馈不达标原因：' . $orderMtGp['fail_reason_msg'] . '，您可前往美团修改)';
                            }
                            if ($orderMtGp['hx_time'] > 0) {
                                $item['deadline_text'] = '反馈剩余时间：';
                                $item['deadline'] = date('Y-m-d 23:59:59', $orderMtGp['hx_time'] + $orderMtGp['comment_time'] * 86400);
                            }
                        } elseif ($orderMtGp['code'] === 8) {
                            $item['warn_tips'] = '官方审核中，核销奖励在完成后' . $delayCashBackTimeText . '返现至美团/大众点评钱包-余额。(反馈失败原因：您已删除评价)';
                        }
                        $item['status'] = $orderCodeList[$orderMtGp['code']];
                    } else {
                        $status = $item->getAttr('status');
                        if ($item['machine_audit_status'] === 0) {
                            $status['name'] = '审核中';
                            $item->setAttr('status', $status);
                            break;
                        }
                        $status['name'] = '人工复审中';
                        $item->setAttr('status', $status);
                    }
                    break;
                case 4:
                    $item['status'] = $paymentStatusList[$item['payment_status']];
                    $item['current_cashback_amount'] = formatNumber($orderCashbackAmountMap[$item['id']] ?? null);
                    $item['super_rebate_amount'] = formatNumber(
                        bcadd(
                            $orderSuperRebateInfoMap[$item['id']] ?? null,
                            $orderRedEnvelopeAmount,
                            2
                        )
                    );

                    if ($item['platform_abbr'] === Platform::PLT_MTG_GP) {
                        $orderMtGp = $orderMtGpMap[$item['id']];
                        if ($orderMtGp['is_fk_cash_back'] === 1) {
                            $hxCashBack = floatval(bcdiv($orderMtGp['hx_cash_back'], '100', 2));
                            $fkCashBack = floatval(bcdiv($orderMtGp['fk_cash_back'], '100', 2));
                            $item['cash_back_text'] = '实返金额: ' . $hxCashBack . '+' . $fkCashBack . '=' . (float)bcadd($hxCashBack, $fkCashBack, 2);
                        } else {
                            $item['cash_back_text'] = '实返金额: ' . floatval(bcdiv($orderMtGp['hx_cash_back'], '100', 2));
                        }
                        $item['warn_tips'] = '本单已返现至美团/大众点评钱包-余额，请前往提现';

                        // 超时/过期完成的，文案还不一样
                        if ($orderMtGp['is_timeout_complete'] === 1) {
                            if (empty($orderMtGp['fk_code']) || $orderMtGp['fk_code'] === 8) {// 没有反馈
                                $item['warn_tips'] .= '(您未参与反馈活动，暂无反馈奖励)';
                            } else if ($orderMtGp['fk_code'] === 9) {
                                $item['warn_tips'] .= '(您反馈未达标，暂无反馈奖励)';
                            }
                        }

                        // 反馈任务失败，文案不一样
                        if ($orderMtGp['fk_code'] === 6) {
                            $item['warn_tips'] .= '(反馈失败原因：您已删除评价)';
                        }
                    }
                    break;
                case 5:
                    $item['warn_tips'] = '';
                    if ($item['platform_abbr'] === Platform::PLT_ELEZ) {
                        if ($item['is_praise'] != 2) {
                            $item['warn_tips'] = '下单后需在次日24点前完成评价，否则会返现失败哦';
                            $item['deadline_text'] = '完成评价剩余时间：';
                            $item['deadline'] = date('Y-m-d 00:00:00', strtotime($item['create_time']) + 172800);
                        }
                    } elseif ($item['platform_abbr'] === Platform::PLT_MTG) {
                        if ($item['is_praise'] != 2) {
                            $item['warn_tips'] = '下单后需在次日23点前完成评价，否则会返现失败哦';
                            $item['deadline_text'] = '完成评价剩余时间：';
                            $item['deadline'] = date('Y-m-d 23:00:00', strtotime($item['create_time']) + 86400);
                        }
                        // 已下单状态,并且美团最后的状态是6(任务失败)
                        if (!empty($mtCodeMap[$item['id']]) && $mtCodeMap[$item['id']] === 6) {
                            if (isset($item['status']['name'])) {
                                $status = $item['status'];
                                $status['name'] = '检测中';
                                $item['status'] = $status;
                            }
                            $item['warn_tips'] = '若美团订单存在退款或取消情况，请勿重复下单，否则无法生效，有问题请联系客服，请耐心等待';
                        }
                    } elseif ($item['platform_abbr'] === Platform::PLT_MTG_GP) {
                        if (!empty($orderMtGpMap[$item['id']])) {
                            $orderMtGp = $orderMtGpMap[$item['id']];
                            $item['status'] = $orderCodeList[$orderMtGp['code']];
                            switch ($orderMtGp['code']) {
                                case OrderMtGp::STS_ORDERED:
                                    $item['warn_tips'] = '下单后需在' . $orderMtGp['consume_time'] . '天内到店核销团购，否则会返现失败哦';
                                    $item['deadline_text'] = '团单核销剩余时间：';
                                    $item['deadline'] = date('Y-m-d 23:59:59', strtotime($orderMtGp['create_time']) + $orderMtGp['consume_time'] * 86400);
                                    break;
                                case OrderMtGp::STS_CONSUMED:
                                    if ($orderMtGp['delay_cash_back_time'] <= 0) {
                                        $item['warn_tips'] = '官方审核中，核销奖励在完成后返现至美团/大众点评钱包-余额。(核销完成后' . $orderMtGp['comment_time'] . '天内您可自由选择是否反馈，至团购平台参与反馈可获取额外返现)';
                                    } else {
                                        $item['warn_tips'] = '官方审核中，核销奖励在完成后约' . $orderMtGp['delay_cash_back_time'] . '小时返现至美团/大众点评钱包-余额。(核销完成后' . $orderMtGp['comment_time'] . '天内您可自由选择是否反馈，至团购平台参与反馈可获取额外返现)';
                                    }
                                    if ($orderMtGp['hx_time'] > 0) {
                                        $item['deadline_text'] = '反馈剩余时间：';
                                        $item['deadline'] = date('Y-m-d 23:59:59', $orderMtGp['hx_time'] + $orderMtGp['comment_time'] * 86400);
                                    }
                                    break;
                                case OrderMtGp::STS_RETURNED:
                                    $item['cash_back_text'] = '已返金额: ' . floatval(bcdiv($orderMtGp['hx_cash_back'], '100', 2));
                                    $item['warn_tips'] = '核销奖励已返现至美团/大众点评钱包-余额,请前往提现。(核销完成后' . $orderMtGp['comment_time'] . '天内您可自由选择是否反馈，至团购平台参与反馈可获取额外返现)';
                                    if ($orderMtGp['hx_time'] > 0) {
                                        $item['deadline_text'] = '反馈剩余时间：';
                                        $item['deadline'] = date('Y-m-d 23:59:59', $orderMtGp['hx_time'] + $orderMtGp['comment_time'] * 86400);
                                    }
                                    break;
                                case OrderMtGp::STS_FEEDBACK:
                                    if ($orderMtGp['is_hx_cash_back'] === 1) {
                                        $item['cash_back_text'] = '已返金额: ' . floatval(bcdiv($orderMtGp['hx_cash_back'], '100', 2));
                                    }
                                    if ($orderMtGp['delay_cash_back_time'] <= 0) {
                                        if ($orderMtGp['is_hx_cash_back'] === 1) {
                                            $item['warn_tips'] = '核销奖励已返现至美团/大众点评钱包-余额，请前往提现；反馈奖励官方审核中，在完成后返现至美团/大众点评钱包-余额';
                                        } else if ($orderMtGp['is_fk_cash_back'] === 1) {
                                            $item['warn_tips'] = '核销奖励官方审核中，在完成后返现至美团/大众点评钱包-余额；反馈奖励已返现至美团/大众点评钱包-余额，请前往提现';
                                        } else {
                                            $item['warn_tips'] = '官方审核中，核销奖励在完成后返现至美团/大众点评钱包-余额；反馈奖励在完成后返现至美团/大众点评钱包-余额';
                                        }
                                    } else {
                                        if ($orderMtGp['is_hx_cash_back'] === 1) {
                                            $item['warn_tips'] = '核销奖励已返现至美团/大众点评钱包-余额，请前往提现；反馈奖励官方审核中，在完成后约' . $orderMtGp['delay_cash_back_time'] . '小时返现至美团/大众点评钱包-余额';
                                        } else if ($orderMtGp['is_fk_cash_back'] === 1) {
                                            $item['warn_tips'] = '核销奖励官方审核中，在完成后约' . $orderMtGp['delay_cash_back_time'] . '小时返现至美团/大众点评钱包-余额；反馈奖励已返现至美团/大众点评钱包-余额，请前往提现';
                                        } else {
                                            $item['warn_tips'] = '官方审核中，核销奖励在完成后约' . $orderMtGp['delay_cash_back_time'] . '小时返现至美团/大众点评钱包-余额；反馈奖励在完成后约' . $orderMtGp['delay_cash_back_time'] . '小时返现至美团/大众点评钱包-余额';
                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                    break;
                case -3:
                    if ($item['platform_abbr'] === 'mtg' || $item['platform_abbr'] === 'eleg') {
                        $deductionAmount = $orderDeductionAmountMap[$item['id']] ?? 0;
                        $item['warn_tips'] = '因实付未满' . (float)$item['meal_price'] . '元，需要从返现中扣除' . (float)($deductionAmount) . '元，请您确认';
                    }
            }

            // 增加是否需要映射的字段
            if ($item['platform_abbr'] === Platform::PLT_MTG_GP) {
                if (in_array($item['order_status'], [-1,])) {
                    $item['need_map'] = false;
                } else {
                    $item['need_map'] = true;
                }
            }

            // 显示视频上传按钮
            // 通用活动的订单，待审核和已驳回状态下，订单列表不给上传视频按钮
            if (in_array($item['order_status'], [-2, 3]) && !Platform::isGeneral($item['platform_abbr']) && !Platform::isRepurchaseCard($item['platform_abbr'])) {
                $item['show_video_upload_btn'] = 1;
            } else {
                $item['show_video_upload_btn'] = 2;
            }

            // 领红包组件, 已报名状态
            if ($item['order_status'] === 1 && isset($pltRLinkMap[$item['platform_abbr']])) {
                $item['red_envelop_widget'] = array_merge([
                    'text' => $rText
                ], $pltRLinkMap[$item['platform_abbr']]);
            }

            $item['reupload_num'] = $orderReuploadMap[$item['id']] ?? 0;
            $orderRelive = new OrderReliveForList($item);
            $orderRelive->setReviveOrderTimeLimitHour($reviveOrderTimeLimitHour);
            $orderRelive->setRevivedOrderValidTimeMinute($revivedOrderValidTimeMinute);
            $orderRelive->checkReviveOrderTimeLimitHour();
            $orderRelive->setOrderReliveCoupon($orderReliveCouponMap);
            $orderRelive->setTaskTotalQuotaAndTaskApplicantsQuota($taskTotalQuotaList, $taskApplicantsQuotaList);
            $orderRelive->setIsPrePay($isPrePayList, $orderCancelAuditMap);
            $orderRelive->setSellerBill($sellerBillTaskList, $sellerBillList);
            $orderRelive->storeOrMiddlemanReliveSwitch($taskStoreList, $taskSellerIdList, $taskPromoterIdList,
                $storeReliveTypeList, $storeReliveSwitchList, $middlemanList, $middleConfigStatusList, $sellerReliveSwitchList);
            $orderRelive->checkFirstOrder($isFirstRelive);
            $orderRelive->hasOtherOrder($otherOrderList);
            $orderRelive->initCheck();
            $item['can_relive'] = $orderRelive->canReliveOrder();
            if ($item['can_relive']) {
                if ($item['order_status'] == MOrder::REGISTERED) {
                    $item['deadline_text'] = '上传单号剩余时间：';
                }
                if ($item['order_status'] == MOrder::SUBMITTED) {
                    $item['deadline_text'] = '提交截图剩余时间：';
                }
            }
            $item['relive_remain_time'] = $orderRelive->getReliveRemainTime();
            $item['is_relive'] = $orderRelive->isReliveOrder();
            $item['relive_upload_remain_time'] = $orderRelive->getReliveUploadRemainTime();
            $item['is_open_help_return'] = invoke([OrderHelpReturnService::class, 'getIsOpenHelpReturn'],
                [-1, $orderInviteBoostConfig, $item['agent_id'], $item['order_status'], $isSelfFullReturn, $item['task_id'],
                    $userHelpReturnAmountIsLimit, $item['is_relive']]);
            $item['help_return_amount'] = invoke([OrderHelpReturnService::class, 'getHelpReturnAmountForList'],
                [-1, $orderInviteBoostConfig, $item['agent_id'], $item['order_status'], $isSelfFullReturn,
                    $helpReturnAmountList, $item['id'], $item['task_id'], $userHelpReturnAmountIsLimit]);
            $item['current_cashback_amount'] = !empty($item['help_return_amount']) ?
                bcadd($item['current_cashback_amount'], $item['help_return_amount'], 2) . '' : $item['current_cashback_amount'];
            unset($item['meal_price'], $item['is_expired'], $item['cash_back_amount'], $item['order_status'],
                $item['cancel_status']);
        });
        $count = MOrder::where($where)
            ->whereTime('create_time', '>=', '-90 days')
            ->field(['id'])
            ->count();
        $totalPages = ceil(bcdiv($count, $options['limit'], 1));
        $data = compact('list', 'count', 'totalPages');
        return json(HBKReturn('success', $data, 200));
    }

    public function get_in_progress_order_count_by_user_default()
    {
        return json(HBKReturn('success', ['corner' => 0], 200));
    }

    public function get_in_progress_order_count_by_user(): Json
    {
        $userId = $this->getUserByToken();
        $version = getCurrentVersion();
        $groupBuyMinVersion = VersionCompatible::getMinVersionByName('group_buy_v');
        $inStoreGpMinVersion = VersionCompatible::getMinVersionByName('in_store_gp_v');
        $jdMsMinVersion = VersionCompatible::getMinVersionByName('jd_ms_v');

        if (empty($userId)) {
            $corner = 0;
        } else {
            $where = [
                ['data_state', '=', 0],
                ['user_id', '=', $userId],
                ['order_status', 'in', [-4, -3, -2, 1, 2, 3, 4, 5, 6]],
                ['payment_status', 'in', [0, 1, 3]]
            ];

            $disabledPltIds = [];

            if ($version < $groupBuyMinVersion) {
                $disabledPltIds = Platform::getGroupBuyAbbreviationIdsArray();
            }

            if ($version < $inStoreGpMinVersion) {
                $disabledPltIds[] = Platform::getMtGpId();
            }

            if ($version < $jdMsMinVersion) {
                $disabledPltIds[] = Platform::getJdMsId();
            }

            if (!empty($disabledPltIds)) {
                $where[] = ['platform_id', 'not in', $disabledPltIds];
            }

            $corner = MOrder::get_count($where, ['field' => 'id']);
        }

        $data = compact('corner');
        return json(HBKReturn('success', $data, 200));
    }

    public function get_order_by_id(): Json
    {
        $id = $this->request->get('id');
        $position = $this->request->get(['lng' => 0, 'lat' => 0], 0, 'floatval');
        $user = $this->request->user;
        if (empty($id)) {
            throw new Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }
        $order = invoke([SOrder::class, 'getOrderById'], [$user, $id, $position['lat'], $position['lng']]);
        $data = compact('order');
        return json(HBKReturn('success', $data, 200));
    }

    public function submit(): Json
    {
        throw new \Exception('请使用最新版本提交');
    }

    public function cancel(): Json
    {
        $id = $this->request->post('id', 0, 'intval');
        $order = $this->validateMethod($id);
        if (in_array($order['order_status'], [-1, 4, 6])) {
            throw new ValidateException('订单状态不正确');
        }

        if ($order['order_status'] === 3) {
            //[#744]人工复审状态直接返回，不允许取消订单
            throw new ValidateException("您的订单正在等待审核，不可取消哦~");
        }

        $data = [$id, '', '', 0, 'clientCancelMtOrder'];
        HttpRpc::callHyperfService('Order', 'cancel', $data);
        Event::trigger('CancelOrder', $id);
        return json(HBKReturn('success', [], 200));
    }

    public function create_sts(): Json
    {
        /** @var Cos $cos */
        $cos = app('\app\common\service\Cos');
        $tempKey = $cos->create_sts();
        return json(HBKReturn('success', $tempKey, 200));
    }

    public function cos_sign(): Json
    {
        $ext = $this->request->get('ext', '');
        $key = 'order/image_' . date('YmdHis') . substr(md5(rand(10000, 99999)), 5, 10) . '.' . $ext;
        $cos = app('\app\common\service\Cos');
        $data = $cos->post_object_sign($ext, $key);
        return json(HBKReturn('success', $data, 200));
    }

    private function validateMethod($id)
    {
        if (empty($id)) {
            throw new Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }
        $where = [
            ['data_state', '=', 0],
            ['user_id', '=', $this->request->user['id']],
            ['id', '=', $id]
        ];
        $order = MOrder::where($where)->lock(true)->find();
        if (empty($order)) {
            throw new Exception('不存在的订单');
        }
        return $order;
    }

    /**
     * 订单详情滚动条
     * @return Json
     */
    public function get_order_scroll_bar()
    {
        $id = $this->request->get('id', 0, 'intval');
        $data = invoke([OrderTip::class, 'get_order_scroll_bar'], [$id]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 订单详情提示
     * @return Json
     */
    public function get_order_tip_message()
    {
        $id = $this->request->get('id', 0, 'intval');
        $data = invoke([OrderTip::class, 'get_order_tip_message'], [$id]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 记录进店方式
     * @return Json
     */
    public function choose_enter_store_method()
    {
        $orderId = $this->request->post('order_id');
        $method = $this->request->post('method');
        $data = invoke([OrderEnterMethod::class, 'choose_enter_store_method'], [$orderId, $method]);
        return json(HBKReturn('success', $data, 200));
    }
}
