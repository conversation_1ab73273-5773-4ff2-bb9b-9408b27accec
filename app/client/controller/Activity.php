<?php

namespace app\client\controller;

use app\client\service\Activity as SActivity;
use app\client\validate\Activity as VActivity;
use think\response\Json;

class Activity extends Base
{
    protected $middleware = [
        'checkToken' => [
            'except' => ['index', 'detail', 'enter_group_config', 'get_enter_link', 'rule']
        ]
    ];

    /**
     * 首页活动引导
     * @return Json
     */
    public function index(): Json
    {
        $params = $this->request->get(['lat' => 0, 'lng' => 0]);
        $data = invoke([SActivity::class, 'index'], [$params]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 用户活动详情
     * @return Json
     */
    public function detail(): Json
    {
        $userId = $this->getUserByToken();
        $params = $this->request->get(['id', 'type' => 1]);
        $data = invoke([SActivity::class, 'detail'], [$params, intval($userId)]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 活动规则
     * @return Json
     */
    public function rule(): Json
    {
        $params = $this->request->get(['id']);
        $data = invoke([SActivity::class, 'rule'], [$params]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 活动跳转小程序配置
     * @return Json
     */
    public function enter_group_config(): Json
    {
        $params = $this->request->get(['type' => 1]);
        $data = invoke([SActivity::class, 'enterGroupConfig'], [$params]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 进群地址
     * @return Json
     */
    public function get_enter_link(): Json
    {
        $params = $this->request->get([
            'id',
            'type' => 1
        ]);
        $data = invoke([SActivity::class, 'getEnterLink'], [$params]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 生成分享邀请图片
     * @return Json
     */
    public function invite(): Json
    {
        $user = $this->request->user;
        $params = $this->request->get(['id']);
        $data = invoke([SActivity::class, 'invite'], [$user['id'], $params]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 用户报名活动
     * @return Json
     */
    public function sign_up(): Json
    {
        $user = $this->request->user;
        $params = $this->request->post(['id', 'type']);
        $this->validate($params, VActivity::class . '.sign_up');
        $data = invoke([SActivity::class, 'signUp'], [$user['id'], $params]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 用户收益明细
     * @return Json
     */
    public function revenue(): Json
    {
        $user = $this->request->user;
        $params = $this->request->get(['id']);
        $data = invoke([SActivity::class, 'revenue'], [$user['id'], $params]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 明细详情
     * @return Json
     */
    public function revenue_detail(): Json
    {
        $user = $this->request->user;
        $params = $this->request->get([
            'id',
            'level' => -1,
            'completed' => 0,
            'page' => 1,
            'limit' => 10
        ]);
        $data = invoke([SActivity::class, 'revenueDetail'], [$user['id'], $params]);
        return json(HBKReturn('success', $data, 200));
    }
}