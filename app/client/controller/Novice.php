<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/7/11 18:06
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\client\service\Novice as SNovice;
use think\response\Json;

class Novice extends Base
{
    /**
     * 新手教程文字
     * @return Json
     */
    public function index(): Json
    {
        $data = invoke([SNovice::class, 'getNovice'], [
            getCurrentVersion()
        ]);
        return json(HBKReturn('success', $data, 200));
    }
}