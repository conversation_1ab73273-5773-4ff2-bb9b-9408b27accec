<?php

namespace app\client\controller;


use app\common\facade\Redis;
use think\response\Json;

class Sms extends Base
{
    protected $middleware = [
        'checkToken'
    ];

    /**
     * 发送短信验证码
     * @return Json
     */
    public function send_code(): Json
    {
        $mobile = $this->request->post('mobile', '', 'trim');
        $position = $this->request->post(['lat' => 0, 'lng' => 0], 0, 'strval');

        // 验证
        $this->validate(compact('mobile'), [
            'mobile' => ['require', 'mobile']
        ]);

        $path = '/sms/send_code';

        //同一ip一天最多50条
        invoke(['app\common\service\SMSVerificationCode', 'allowOne'],
            [$path, get_client_ip(0, true), 50]);

        // 同一个手机号一天最多20条
        invoke(['app\common\service\SMSVerificationCode', 'allowOne'], [$path, $mobile, 20]);

        // 发送短信
        $data = invoke(['app\common\service\SMSVerificationCode', 'sendCode'],
            [$mobile, Redis::$order_phone_verify, $position]);

        // 返回
        return json(HBKReturn('短信发送成功！', $data, 200));
    }
}