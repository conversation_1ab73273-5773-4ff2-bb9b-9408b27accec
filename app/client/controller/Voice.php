<?php

namespace app\client\controller;

use app\client\service\Voice as SVoice;

class Voice extends Base
{
    //腾讯云的ai语音回调，todo 等腾讯云回调地址切换到api目录下的域名的时候，这里的这个回调入口失效
    public function callback()
    {
        $params = $this->request->post();
        //请求参数返回
        if (isset($params['voiceprompt_callback'])) {
            invoke([SVoice::class, 'voicePromptCallback'], [$params['voiceprompt_callback']]);
        }
        return json(['result' => 0, 'errmsg' => 'OK']);

    }
}