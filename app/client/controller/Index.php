<?php
declare (strict_types=1);

namespace app\client\controller;

use app\client\implement\task\TaskList;
use app\client\model\Config;
use app\client\model\Platform;
use app\client\model\Task;
use app\client\model\User;
use app\client\service\AgentAd as SAgentAd;
use app\client\service\Store;
use app\common\exception\ExceptionCode;
use app\common\exception\TokenException;
use app\common\model\StoreCategory;
use app\common\model\VersionCompatible;
use think\Exception;
use think\response\Json;
use app\client\service\Advertisement as SAdvertisement;
use app\Log;


class Index extends Base
{
    /**
     * 获取banner列表
     *
     * @request method:GET desc:请求信息
     * @params name:platform type:string desc:访问平台
     *
     * @return Json
     * @throws \RedisException
     */
    public function get_banner_list(): Json
    {
        $platform = $this->request->get('platform', 'web');
        $version = getCurrentVersion();
        $lat = $this->request->get('lat', 0);
        $lng = $this->request->get('lng', 0);
        $userId = 0;
        // 新的banner图，老版本banner图
        $brandCompare = brand_version_compare();
        // 大牌专享已上线
        $count = 0;
        $brandBannerList = $bannerList = [];
        if ($brandCompare) {
            try {
                $userId = $this->getUserByToken();
            } catch (\Exception $e) {
                if ($e instanceof TokenException) {
                    $userId = 0;
                }
            }
            // 附近大牌数量
            try {
                $count = invoke([SAdvertisement::class, 'getBrandBanner'], [$userId, $version, $lat, $lng]);
            } catch (\Exception $e) {
                Log::error('Index', $e, '获取大牌banner失败', 'getBrandBanner', [$userId, $version, $lat, $lng]);
            }

            // 大牌的版本号
//            $minBrandVersion = VersionCompatible::where('version_name', '=', VersionCompatible::BRAND_ONLINE)
//                ->where('data_state', '=', 0)
//                ->value('min_version');
            // 都是取的大牌的版本号，之前这么写是为了获取所有大牌版本之后的banner，现在是需要版本控制的
            // 没有大牌，则不显示大牌banner版本之后添加的banner
            if ($count <= 0) {
                $bannerList = invoke([SAdvertisement::class, 'getNewAdvertisementList'], ['banner', $version, $platform]);
            } else {
                $brandBannerImage = Config::getValueByKey('brand_banner_image');
                // 大牌banner显示开关
                $brandBannerEnable = Config::getValueByKey('brand_banner_enable');
                $brandBannerList = $brandBannerEnable ? [$brandBannerImage] : [];
                // 有大牌则隐藏banner开关
                $brandBannerHiddenEnable = Config::getValueByKey('brand_banner_hidden_enable');
                // 展示最低版本号的banner图
                if (empty($brandBannerHiddenEnable)) {
                    $bannerList = invoke([SAdvertisement::class, 'getNewAdvertisementList'], ['banner', $version, $platform]);
                }
            }
        } else {
            // 实时客户端的version， max ， min 空
            $bannerList = invoke([SAdvertisement::class, 'getAdvertisementList'], ['banner', $version, '', $platform]);
        }

        $bannerList = !empty($bannerList) ? $bannerList->toArray() : $bannerList;
        // 过滤大牌的banner
        if ($count <= 0) {
            $bannerList = array_values(
                array_filter($bannerList, function ($item) {
                    $path = $item['skip']['path'] ?? '';
                    return strpos($path, '/pages/bigBrand/index') === false && $path != 'big_brand';
                })
            );
        }

        $data = ['brand_banner' => $brandBannerList, 'banner' => $bannerList];
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 获取get_bottom_banner_list列表
     *
     * @return Json
     */
    public function get_bottom_banner_list(): Json
    {
        $userId = $this->getUserByToken();
        $bottomBannerList = invoke([SAdvertisement::class, 'getBottomBannerList'], [$userId]);
        return json(HBKReturn('success', $bottomBannerList, 200));
    }

    /**
     * 获取金刚区列表
     *
     * @request method:GET desc:请求信息
     * @params name:platform type:string desc:访问平台
     *
     * @return Json
     */
    public function get_vajra_list(): Json
    {
        $cateList = invoke(['app\client\service\Vajra', 'vajraCateList']);
        array_unshift($cateList, ['id' => 'hot', 'cate_name' => '热门']);
        $data = array_columns($cateList, 'id', ['cate_name']);
        $platform = $this->request->get('platform', 'web');
        $list = invoke(['app\client\service\Vajra', 'vajraList'], [$platform]);
        foreach ($list as $item) {
            $child = $item;
            $child['skip'] = $item['vajra_skip_config'];
            unset($child['hot'], $child['cate_id'], $child['vajra_skip_config']);
            if ($item['hot'] === 1) {
                $data['hot']['child'][] = $child;
            }
            if (empty($item['cate_id']) || empty($data[$item['cate_id']])) {
                continue;
            }
            if (empty($data[$item['cate_id']])) {
                $data[$item['cate_id']] = [
                    'cate_name' => $cateList[$item['cate_id']],
                    'child' => []
                ];
            }
            $data[$item['cate_id']]['child'][] = $child;
        }
        foreach ($data as $key => $item) {
            if (empty($item['child'])) {
                unset($data[$key]);
            }
        }
        $data = array_values($data);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 推广区数据
     * @return Json
     */
    public function get_popularize_data(): Json
    {
        $lat = floatval($this->request->get('lat', 0));
        $lng = floatval($this->request->get('lng', 0));
        $userId = $this->request->get('user_id', 0);
        $wechat_group_qr = invoke([Store::class, 'getWechatQrCode'], [$lat, $lng]);
        $data = Config::getValueByKey('index_extension_data');
        $data = json_decode($data, true);

        // 团员奖励文案变更
        if (isset($data['v0'])) {
            $data = getCurrentVersion() < 20116 ? $data['v0'] : $data['v20120'];
        }

        $data['wechat_group_qr'] = $wechat_group_qr;
        $share_title = Config::getValueByKey('share_title');
        $share_desc = Config::getValueByKey('share_desc');
        $share_logo = Config::getValueByKey('share_logo');
        $share_url = env('WEB_BASE_URL', 'https://xxyx.web.xxyx.ltd/#/');
        if (!empty($userId)) {
            $share_url .= '?uid=' . $userId;
        }
        $data['share_title'] = $share_title;
        $data['share_desc'] = $share_desc;
        $data['share_logo'] = $share_logo;
        $data['share_url'] = $share_url;
        $data['redirect_url'] = 'https://team.saveat.wang/#/';
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 首页代理广告
     * @return Json
     */
    public function agent_ad(): Json
    {
        $userId = $this->getUserByToken();
        $params = $this->request->get(['lat' => 0, 'lng' => 0]);
        $data = invoke([SAgentAd::class, 'getAdBy'], [$params, $userId, 'home']);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 新版本活动分类列表
     * @return Json
     * @throws \RedisException
     */
    public function get_new_task_cate_list(): Json
    {
        $cate = $this->request->get('cate', 'wm', 'trim');
        $hasDistanceOpt = false;

        if ($cate === 'wm') {
            $indexTaskCateList = Task::getIndexNewTaskCateList();
            $brandCompare = brand_version_compare();
            $mergeCompare = multi_task_merge_version_compare();
            if (!$brandCompare) {
                $tag = $indexTaskCateList['tag'];
                $indexTaskCateList['tag'] = array_values(array_filter($tag, function ($item) {
                    return $item['id'] != 'brand';
                }));
            }

            if ($mergeCompare) {
                $indexTaskCateList['tag'] = array_map(function ($tag) {
                    if ($tag['id'] === 'noEvaluation') {
                        $tag['name'] = '无需反馈';
                    }
                    return $tag;
                }, $indexTaskCateList['tag']);
            }

            // 晓晓霸王餐、美团试吃官
            $indexTaskCateList['type'] = [
                ['id' => Task::TYPE_XXT, 'name' => '晓晓霸王餐'],
                ['id' => Task::TYPE_MTT, 'name' => '美团试吃官'],
                ['id' => Task::TYPE_ELET, 'name' => '饿了么美食探'],
            ];

            // 增加品类
            $storeCategoryList = StoreCategory::where('data_state', 0)
                ->field('id, name')
                ->select();
            $indexTaskCateList['category'] = $storeCategoryList;

            // 排序逻辑处理
            $isRebateSort = rebate_sort_version_compare();
            if ($isRebateSort) {
                $hasDistanceOpt = true;
            } else {
                $indexTaskCateList['sort'] = array_values(array_filter($indexTaskCateList['sort'], function ($item) {
                    return $item['id'] !== 'rebate_ratio';
                }));
            }
        } else {
            $indexTaskCateList = Task::getIndexNew2TaskCateList();
            $hasDistanceOpt = true;
        }

        if ($hasDistanceOpt) {
            // 增加距离排序选项
            $distanceOptions = Config::getValueByKey('index_select_distance_options');

            if (!empty($distanceOptions)) {
                $indexTaskCateList['distance'] = json_decode($distanceOptions, true);
            } else {
                $indexTaskCateList['distance'] = [];
            }

            $userId = $this->getUserByToken();

            if ($userId > 0) {
                $distanceWhitelist = (int)User::where('id', $userId)
                    ->where('status', 0)
                    ->where('data_state', 0)
                    ->value('distance_whitelist');

                // 限制查看距离，默认显示距离优先
                if ($distanceWhitelist === 1 && isset($indexTaskCateList['sort'])) {
                    $indexTaskCateList['sort'] = array_map(function ($item) {
                        $item['selected'] = $item['id'] === 'distance_asc';
                        return $item;
                    }, $indexTaskCateList['sort']);
                }
            }
        }

        // 如果是团购版本，并且是外卖， 团购不在进入筛选
        if (in_store_gp_version() && $cate === 'wm') {
            $gpAbbr = [
                'mtgp',
                'dygp',
                'publicgp'
            ];
            $indexTaskCateList['platform'] = array_values(array_filter($indexTaskCateList['platform'], function ($item) use ($gpAbbr) {
                return !in_array($item['id'], $gpAbbr);
            }));
        }

        // 如果不是京东秒送版本，`京东`不在进入筛选
        if (!jd_ms_version()) {
            $indexTaskCateList['platform'] = array_values(array_filter($indexTaskCateList['platform'], function ($item) {
                return $item['id'] !== Platform::PLT_JD_MS;
            }));
        }

        return json(HBKReturn('success', ['taskCateList' => $indexTaskCateList], 200));
    }

    /**
     * 新版活动列表
     * @return Json
     * @throws \Exception
     */
    public function get_new_task_list(): Json
    {
        $params = $this->request->get([
            'keyword' => null,
            'tag' => null,
            'session_id' => '',
            'fd_session_id' => '',
            'request_id' => '',
            'platform' => null,
            'rush' => false,
            'lat' => 0,
            'lng' => 0,
            'statistics' => 1,
            'sort' => 'default',
            'page_pv_id' => null,
            'mt_pv_id' => null,
            'version' => null,
            // 品类可以多选
            'category_id' => null,
            'only_self' => 0,
            'distance' => '',
            'from' => '',
            'type' => null,
            // 从这个版本开始，美团/点评/抖音 团购，移到团购分类下
            'cate' => 'wm', // 选项卡中，可选值[wm:外卖上门, gp:到店团购]
        ]);

        $params['options'] = $this->request->get(['page' => 0]);
        $params['user_id'] = $this->getUserByToken();
        $params['version'] = getCurrentVersion();

        // 移除关键词两边的空格
        if (!empty($params['keyword'])) {
            $params['keyword'] = trim($params['keyword']);
        }

        /** @var TaskList $task */
        $task = app(TaskList::class, [$params], true);
        $data = $task->getTaskList();
        $returnData = aesEncryption($data);
        return json(HBKReturn('success', $returnData, 200));
    }

    /**
     * @return Json
     * @throws \Exception
     */
    public function geocoder(): Json
    {
        $lat = floatval($this->request->get('lat', 0));
        $lng = floatval($this->request->get('lng', 0));

        if (empty($lat) || empty($lng)) {
            throw new Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }

        //$address = invoke([\app\common\service\Position::class, 'getGeoResultByStatic'],
        //    [$lat, $lng, 'getProvinceCityDistrictToString', '定位失败，请手动选择']);
        $geocoderData = invoke([\app\common\service\Position::class, 'getGeoResultByStatic'],
            [$lat, $lng, 'getProvinceCityDistrictToStringAndAgentId', '定位失败，请手动选择']);
        $data = ['address' => $geocoderData['address'], 'agent_id' => $geocoderData['agent_id'],
            'switchCity' => false];
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * app下载链接
     * @return Json
     */
    public function download_app(): Json
    {
        $data = Config::getValueByKey('index_app_data');
        $data = json_decode($data, true);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 获取客服配置链接
     *
     * @return Json
     */
    public function customer_service_link(): Json
    {
        $customerServiceLink = Config::get_value('value', ['key' => 'customer_service_link']);
        return json(HBKReturn('success', ['url' => $customerServiceLink], 200));
    }

    /**
     * 查看当前用户是否有切换城市的权限
     * @return Json
     */
    public function checkChangeCity()
    {
        $switchCity = false;
        $userId = $this->getUserByToken();
        if (!empty($userId)) {
            //用户登录状态下，判断当前用户是否可以切换不限制距离的城市
            $distanceWhitelist = User::where('id', $userId)->value('distance_whitelist');
            $switchCity = $distanceWhitelist === 2;
        }
        $data = ['switchCity' => $switchCity];
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 查看用户端引导的图片
     *
     * @return Json
     */
    public function get_newcomer_orientation(): Json
    {
        $tab = $this->request->get('tab', 1);
        if ($tab == 1) {
            $content = Config::getValueByKey('newcomer_orientation_how');
        } elseif($tab == 2) {
            $content = Config::getValueByKey('newcomer_orientation_question');
        } else {
            $content = !order_timout_v() ? Config::getValueByKey('newcomer_orientation_rule') : Config::getValueByKey('newcomer_orientation_rule_order_limit');
        }

        return json(HBKReturn('success', ['list' => json_decode($content, true)], 200));
    }

    /**
     * 获取banner列表 (新)
     *
     * @request method:GET desc:请求信息
     * @params name:platform type:string desc:访问平台
     *
     * @return Json
     */
    public function get_new_banner_list(): Json
    {
        $platform = $this->request->get('platform', 'web');
        $version = getCurrentVersion();
        $lat = $this->request->get('lat', 0);
        $lng = $this->request->get('lng', 0);
        $userId = 0;
        // 新接口一定是大牌banner版本
        try {
            $userId = $this->getUserByToken();
        } catch (\Exception $e) {

        }
        $data = invoke([SAdvertisement::class, 'getNewBannerList'], [$userId, $version, $lat, $lng, $platform]);
        return json(HBKReturn('success', $data, 200));
    }
}
