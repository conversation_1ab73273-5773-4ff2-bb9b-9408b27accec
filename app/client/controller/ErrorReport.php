<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/7/13 10:15
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\client\service\StoreErrorReport as SStoreErrorReport;
use app\client\validate\StoreErrorReport as VStoreErrorReport;
use app\common\model\StoreErrorReportType;
use think\response\Json;

class ErrorReport extends Base
{
    protected $middleware = [
        'checkToken'
    ];

    /**
     * 获取类型
     *
     * @return Json
     */
    public function get_report_type_list(): Json
    {
        $list = StoreErrorReportType::where('data_state', 0)->field([
            'id', 'name'
        ])->select();

        return json(HBKReturn('success', ['list' => $list], 200));
    }

    /**
     * 上报
     *
     * @return Json
     */
    public function report(): Json
    {
        $user = $this->request->user;
        $params = $this->request->post(['type_id', 'store_id']);
        $this->validate($params, VStoreErrorReport::class . '.report');
        $data = invoke([SStoreErrorReport::class, 'submit'], [$user['id'], $params]);
        return json(HBKReturn('success', $data, 200));
    }
}
