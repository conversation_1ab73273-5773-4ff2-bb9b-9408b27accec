<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/7/25 16:15
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\client\exception\CancellationException;
use app\client\model\{Config, IdentityCard, Order as MOrder, OrderRebateBill as MOrderRebateBill, UserMember};
use app\client\model\Promotion as MPromotion;
use app\client\service\OrderCashBackBill;
use app\client\service\OrderRebateBill;
use app\client\service\UserService;
use app\client\service\YunOpen;
use app\client\validate\My as VMy;
use app\client\validate\Suggestion as VSuggestion;
use app\client\validate\User as VUser;
use app\common\exception\ExceptionCode;
use app\common\facade\Redis;
use app\common\model\Suggestion as MSuggestion;
use app\common\service\Order;
use app\common\implement\task_center\User as IUser;
use think\Exception;
use think\exception\ValidateException;
use think\facade\Db;
use think\response\Json;

class My extends Base
{
    protected $middleware = [
        'checkToken'
    ];

    /**
     * 个人中心数据
     * @return Json
     */
    public function index(): Json
    {
        $user = $this->request->user;
        $mobile = substr_replace($user['user_mobile'], '****', 3, 4);
        $redEnvelopeBanner = Config::getValueByKey('red_envelope_banner');

        $userMemberInfo = UserMember::where('user_id', $user['id'])
            ->where('is_delete', '=', 0)
            ->field(['member_count', 'member_order_count'])
            ->find();
        $data = [
            'nick' => $user['wechat_nick'] ?: $user['user_nick'],
            'avatar' => $user['wechat_avatar'] ?: $user['user_avatar'],
            'mobile' => $mobile,
            'totalCashBackAmount' => $user['total_cash_back_amount'],
            'totalRebateAmount' => $user['total_rebate_amount'],
            'availableScore' => $user['user_score'],
            'numberOfMembers' => !empty($userMemberInfo['member_count']) ? $userMemberInfo['member_count'] : 0,
            'numberOfMembersOrder' => !empty($userMemberInfo['member_order_count']) ? $userMemberInfo['member_order_count'] : 0,
            'redEnvelopeBanner' => $redEnvelopeBanner,
            'is_promoter' => (int)!empty(MPromotion::where('mobile', $user['user_mobile'])->where('data_state', 0)->find()),
            'identityId' => encryptionUserId($user['id']),
            'orderNum' => $user['order_num']
        ];
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 我的订单收益
     * @return Json
     */
    public function cash_back_earnings(): Json
    {
        $status = $this->request->get('status', 0);
        $user = $this->request->user;
        $options = $this->request->get(['page' => 0, 'limit' => 10]);
        $cumulativeCashBackAmount = $user['total_cash_back_amount'];//累计提现金额
        $where = [
            ['data_state', '=', 0],
            ['user_id', '=', $user['id']]
        ];
        if ($status == 1) {
            $where[] = ['payment_status', '=', 2];
        } elseif ($status == 2) {
            $where[] = ['payment_status', '<>', 2];
        }
        [$list, $count, $totalPages] = invoke([OrderCashBackBill::class, 'getMyEarnings'], [$where, $options]);
        $data = compact('cumulativeCashBackAmount', 'list', 'count', 'totalPages');
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 我的推广收益
     * @return Json
     */
    public function rebate_earnings(): Json
    {
        $status = $this->request->get('status', 0);
        $user = $this->request->user;
        $options = $this->request->get(['page' => 0, 'limit' => 10]);

        $where = [
            'data_state' => 0,
            'user_id' => $user['id'],
            'rebate_status' => $status
        ];
        [$list, $count, $totalPages] = invoke([OrderRebateBill::class, 'getMyRebate'], [$where, $options]);
        $data = compact('list', 'count', 'totalPages');
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 重新打款推广奖励
     * @return Json
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function rebate_repay(): Json
    {
        $id = $this->request->post('id');
        $user = $this->request->user;
        if (empty($id)) {
            throw new Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }

        $orderRebateBill = MOrderRebateBill::get_one([
            'id' => $id,
            'user_id' => $user['id'],
            'data_state' => 0
        ], ['lock' => true]);

        if (empty($orderRebateBill)) {
            throw new Exception("此订单无推广奖励！");
        }
        if ($orderRebateBill['rebate_status'] !== 3) {
            throw new Exception("结算状态不正确！");
        }

        MOrderRebateBill::where(['id' => $id])->update([
            'rebate_status' => 1,
            'rebate_payment_fail_reason' => ''
        ]);

        invoke([Order::class, 'payRebateAmount'], [$orderRebateBill]);
        return json(HBKReturn('success', [], 200));
    }

    /**
     * 提现账号
     * @return Json
     */
    public function get_alipay(): Json
    {
        $user = $this->request->user;
        $data = [
            'alipay_account' => $user['user_alipay_account'],
            'alipay_name' => $user['user_alipay_name'],
            'user_sign' => $user['identity_card_id'] > 0
        ];
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 设置/修改支付宝修改人
     * @return Json
     */
    public function bind_alipay(): Json
    {
        $user = $this->request->user;
        $params = [$user];
        $params['alipay_account'] = $this->request->post('alipay_account', null, 'trim');
        $validateScene = 'alipay_account';

        if (empty($user['identity_card_id'])) {
            $params['alipay_name'] = $this->request->post('alipay_name', null, 'trim');
            $validateScene = 'alipay';
        }

        $this->validate($params, VUser::class . '.' . $validateScene);

        invoke([UserService::class, 'saveAlipayInfo'], $params);
        return json(HBKReturn('success', [], 200));
    }

    public function get_user_sign(): Json
    {
        $user = $this->request->user;
        $userSign = null;
        // 是否签约
        if ($user['identity_card_id'] > 0) {
            $userSign = IdentityCard::get_by_id($user['identity_card_id'], [], ['field' => 'real_name, id_card']);
        }
        $text = Config::getValueByKey('yun_user_sign_notice');
        $replaceTag = Config::getValueByKey('high_light_replace_tag');
        $replaceTag = json_decode($replaceTag, true);
        $text = str_replace('{highlightStart}', $replaceTag['highlightTextStart'], $text);
        $text = str_replace('{highlightEnd}', $replaceTag['highlightTextEnd'], $text);
        $text = explode(PHP_EOL, $text);
        $data = [
            'user_sign' => $userSign,
            'need_user_sign' => true, // 兼容老版本安卓
            'text' => $text,
            'alipay_name' => $user['user_alipay_name'],
        ];

        return json(HBKReturn('success', $data, 200));
    }

    public function user_sign_first_notice(): Json
    {
        $notice = Config::getValueByKey('yun_first_notice');
        $notice = json_decode($notice, true);
        $replaceTag = Config::getValueByKey('high_light_replace_tag');
        $replaceTag = json_decode($replaceTag, true);
        $notice['content'] = array_map(function ($item) use ($replaceTag) {
            $item = str_replace('{highlightStart}', $replaceTag['highlightTextStart'], $item);
            return str_replace('{highlightEnd}', $replaceTag['highlightTextEnd'], $item);
        }, $notice['content']);
        return json(HBKReturn('success', $notice, 200));
    }

    public function user_sign(): Json
    {
        $user = $this->request->user;
        $version = $this->request->version;
        $yunOpen = invoke([YunOpen::class, 'getYunOpen'], [$version, $user['create_time'], $user['id']]);

        if (!$yunOpen) {
            throw new ValidateException('当前无需签约');
        }

        if ($user['identity_card_id'] > 0) {
            throw new ValidateException('该用户已经认证');
        }

        $idCardNo = $this->request->post('id_card', null, 'trim');
        $idCardName = $this->request->post('real_name', null, 'trim');

        $this->validate(['id_card' => $idCardNo, 'real_name' => $idCardName], VUser::class . '.user_sign');
        invoke([UserService::class, 'userSign'], [$idCardNo, $idCardName]);
        invoke([UserService::class, 'setIdCard'], [$user, $idCardNo, $idCardName]);
        return json(HBKReturn('success', [], 200));
    }

    //获取灵活就业合作伙伴协议
    public function cooperation_agreement(): Json
    {
        $data = invoke([UserService::class, 'getCooperationAgreement']);
        return json(HBKReturn('success', $data, 200));
    }

    public function my_invitation(): Json
    {
        $user = $this->request->user;
        $options = $this->request->get(['page' => 1, 'limit' => 10]);
        $data = invoke([OrderRebateBill::class, 'myInvitationByJava'], [$user, $options]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 发送短信验证码
     *
     * @request method:POST desc:请求信息
     * @body name:mobile type:string desc:手机号
     *
     * @return Json
     */
    public function send_cancellation_sms_code(): Json
    {
        $mobile = $this->request->post('mobile', '', 'trim');
        $position = $this->request->post(['lat' => 0, 'lng' => 0], 0, 'floatval');
        // 验证
        if ($mobile !== $this->request->user['user_mobile']) {
            throw new ValidateException('请输入正确的注册手机号。');
        }
        // 发送短信
        $data = invoke(['app\common\service\SMSVerificationCode', 'sendCode'], [$mobile, Redis::$user_cancellation_sms_code, $position]);
        // 返回
        return json(HBKReturn('短信发送成功！', $data, 200));
    }

    public function check_cancellation_sms_code(): Json
    {
        $mobile = $this->request->user['user_mobile'];
        $code = $this->request->post('code');
        // 测试账号验证码检查
        $mobile_whitelist = ['18969137622', '18768177608', '13205710602'];
        if (!in_array($mobile, $mobile_whitelist) || $code !== '1234') {
            invoke(['app\common\service\SMSVerificationCode', 'checkCode'], [$mobile, $code,
                Redis::$user_cancellation_sms_code, Redis::$user_login_sms_code_error]);
        }
        $codeKey = Redis::getFullKeyName($mobile, Redis::$user_cancellation_confirm);
        Redis::set($codeKey, '1', 600);
        return json(HBKReturn('短信发送成功！', [], 200));
    }

    public function cancellation(): Json
    {
        $user = $this->request->user;
        $mobile = $user['user_mobile'];
        $codeKey = Redis::getFullKeyName($mobile, Redis::$user_cancellation_confirm);
        if (Redis::get($codeKey)) {
            if ($user['user_score'] < 0 || $user['user_gold'] < 0) {
                throw new CancellationException('您的积分/金币为负数，不可注销，如有疑问，请联系客服', 4002);
            }

            if ($user['user_score'] > 0 || $user['user_gold'] > 1) {
                throw new CancellationException('您的积分/金币存在剩余，请提现后操作', 4003);
            }

            $order = MOrder::get_any([
                'user_id' => $user['id'],
                ['order_status', 'in', [-3, -2, 1, 2, 3]]
            ]);
            if ($order) {
                throw new Exception('您有未完成的订单存在，请先处理。');
            }
            $user->data_state = 1;
            $user->save();
            /** @var IUser $taskCenterUser */
            $taskCenterUser = app(IUser::class);
            $taskCenterUser->logOffUser($user['id']);
            return json(HBKReturn('success', [], 200));
        } else {
            throw new ValidateException('请重新进行短信验证！');
        }
    }

    public function suggestion_index()
    {
        $typeList = MSuggestion::getTypeList();
        return json(HBKReturn('success', compact('typeList'), 200));
    }

    /**
     * 新增投诉建议
     * @return Json
     */
    public function suggestion_add()
    {
        $user = $this->request->user;
        $params = $this->request->post(['type', 'suggestion', 'images' => '', 'mobile', 'app_version']);
        $this->validate($params, VSuggestion::class);
        Db::startTrans();
        try {
            invoke(['app\client\service\Suggestion', 'add'], [$params, $user]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            throw new Exception($e->getMessage());
        }
        return json(HBKReturn('success', [], 200));

    }

    /**
     * 投诉建议列表
     * @return Json
     */
    public function suggestion_list()
    {
        $user = $this->request->user;
        $options = $this->request->post(['page' => 1, 'limit' => 10]);
        $data = invoke(['app\client\service\Suggestion', 'getList'], [['user_id' => $user['id']], $options]);
        return json(HBKReturn('success', $data, 200));

    }


    /**
     * 卡券列表
     * @return Json
     */
    public function coupon_list(): Json
    {
        $user = $this->request->user;
        $params = $this->request->get(['task_id' => null, 'platform_abbr' => null, 'type' => 1]);
        $this->validate($params, VMy::class . '.coupon_list');
        $data = invoke(['app\client\service\My', 'getCouponList'], [$user, $params]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 团长邀请
     * @return Json
     */
    public function group_rebate_rules()
    {
        $invitationRewardCfgEffectTime = (string)Config::getValueByKey('team_invitation_reward_effect_time');
        $invitationRewardCfgEffectTimestamp = strtotime($invitationRewardCfgEffectTime);
        $data = Config::getValueByKey('new_group_rebate_rules');
        $data = str_replace('{$effectDateTime}', date('Y年m月d日', $invitationRewardCfgEffectTimestamp), $data);
        $rules = !empty($data) ? json_decode($data, true) : [];
        return json(HBKReturn('success', ['rules' => $rules], 200));
    }
}
