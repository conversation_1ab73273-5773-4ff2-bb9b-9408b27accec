<?php
declare (strict_types=1);

namespace app\client\controller;

use app\admin\model\AgentUrbanActivityConfig;
use app\client\model\Config;
use app\client\service\Advertisement as SAdvertisement;
use app\common\service\Position;
use Rabbit;
use think\response\Json;


class Ad extends Base
{
    /**
     * 首页-金刚区广告
     * @return Json
     * @throws \RedisException
     */
    public function get_vajra_ad(): Json
    {
        // 获取地理位置
        $position = $this->request->get(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $version = getCurrentVersion();
        $limit = $version < 30090 ? null : 10;

        // 获取广告列表
        $list = invoke([SAdvertisement::class, 'getNewAdvertisementList'], ['vajra', $version, getCurrentPlatform(), $limit]);
        $result = $list->toArray();

        // 初始化隐藏的 path
        $hidePaths = [];

        // 客服隐藏（低版本）
        if (message_push_version_compare() && $version < 30090) {
            $hidePaths[] = 'customer_service';
        }

        // 城市挑战和助力逻辑（高版本）
        if ($version >= 30090) {
            $agentId = invoke([Position::class, 'getGeoResultByDb'], [$position['lat'], $position['lng'], 'getAgentId', 0]);

            if (empty($agentId)) {
                $hidePaths[] = 'city_challenge';
                $hidePaths[] = 'friend_help_activity';
            } else {
                $status = AgentUrbanActivityConfig::where('agent_id', $agentId)->where('data_state', 0)->value('status');
                if ($status != 1) {
                    $hidePaths[] = 'city_challenge';
                }

                $friendHelpActivityConfig = json_decode(Config::getValueByKey('friend_help_activity_config'), true);
                $cityList = $friendHelpActivityConfig['openCityConfig']['cityList'] ?? [];
                $isAllOpen = $friendHelpActivityConfig['openCityConfig']['isAllOpen'] ?? false;

                if (!$isAllOpen && !in_array($agentId, $cityList)) {
                    $hidePaths[] = 'friend_help_activity';
                }
            }
        }

        // 过滤掉 skip 中包含隐藏 path 的广告项
        $result = array_values(array_filter($result, function ($item) use ($hidePaths) {
            if (!isset($item['skip']) || !is_array($item['skip'])) {
                return true;
            }
            foreach ($item['skip'] as $path) {
                if (in_array($path, $hidePaths)) {
                    return false; // 跳过此广告
                }
            }
            return true;
        }));

        return json(HBKReturn('success', $result, 200));
    }

    /**
     * 首页金刚区广告位的点击，记录下操作信息
     * @return Json
     */
    public function click_vajra_ad()
    {
        $userId = $this->getUserByToken();
        $params = $this->request->post(['ad_id', 'user_device_cid']);

        if (empty($params['user_device_cid']) || empty($params['ad_id'])) {
            return json(HBKReturn('success', [], 200));
        }

        //数据丢到rabbitmq
        $config = [
            'exchange_name' => Rabbit::EXT_AD_CLICK,
            'exchange_type' => Rabbit::DIRECT,#直连模式
            'queue_name' => Rabbit::QUEUE_AD_CLICK,
            'route_key' => Rabbit::ROUTE_AD_CLICK
        ];
        $data = json_encode([
            'data' => [
                'platform' => getCurrentPlatform(),
                'version' => getCurrentVersion(),
                'user_id' => $userId ?: 0,
                'user_device_cid' => $params['user_device_cid'],
                'ad_id' => $params['ad_id'],
                'create_time' => date("Y-m-d H:i:s")
            ]
        ], 256);
        Rabbit::pushMessage($data, $config);
        return json(HBKReturn('success', [], 200));
    }

    /**
     * 首页-新人礼广告
     * @return Json
     */
    public function get_newcomer_ad(): Json
    {
        $bg = '';
        $list = invoke([SAdvertisement::class, 'getAdvertisementList'], ['newcomer', '', '', getCurrentPlatform()]);
        $bgList = invoke([SAdvertisement::class, 'getAdvertisementList'], ['newcomer_bg', '', '', getCurrentPlatform()]);

        if (!$bgList->isEmpty()) {
            $bg = $bgList[0];
        }

        return json(HBKReturn('success', [
            'bg' => $bg,
            'list' => $list
        ], 200));
    }

    public function getNewcomerTutorial(): Json
    {
        $list = invoke([SAdvertisement::class, 'getAdvertisementList'], ['newcomer_tutorial', '', '', getCurrentPlatform()]);
        return json(HBKReturn("success", compact("list"), 200));
    }

}
