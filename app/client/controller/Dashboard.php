<?php

namespace app\client\controller;

use app\client\service\Activity as SActivity;
use app\client\service\User as SUser;
use app\client\validate\Activity as VActivity;
use think\response\Json;

class Dashboard extends Base
{
    protected $middleware = [
        'checkToken'
    ];

    /**
     * 用户收益看板
     * （此接口已废弃）
     * @return Json
     */
    public function reward(): Json
    {
        $user = $this->request->user;
        // 1：累计 2：月度
        $params = $this->request->get(['tab' => 1, 'month' => date('Y-m')]);
        $data = invoke([SUser::class, 'getUserRewardBill'], [$user, $params]);
        return json(HBKReturn('success', $data, 200));
    }
}
