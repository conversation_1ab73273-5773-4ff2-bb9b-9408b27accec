<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2023/7/14 10:47
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\client\factory\task_factory\TaskV20180;
use app\client\model\Config;
use app\client\model\UserPopupSetting;
use app\client\model\UserScore;
use app\client\service\User as SUser;
use app\client\service\YunOpen;
use app\client\validate\User as VUser;
use think\response\Json;

class User extends Base
{
    protected $middleware = [
        'checkToken'
    ];

    /**
     * 校验用户支付宝是否为空
     * @return Json
     */
    public function check_alipay(): Json
    {
        $has = invoke([SUser::class, 'checkAlipay']);
        return json(HBKReturn('success', ['has' => $has], 200));
    }

    /**
     * 关闭弹窗
     * @return Json
     */
    public function close_pop_up(): Json
    {
        $type = $this->request->post('type', UserPopupSetting::CJF_SCG_POPUP);
        $data = invoke([SUser::class, 'closePopUp'], [$type]);
        return json(HBKReturn('success', $data, 200));
    }

    public function check_score()
    {
        $data = [
            'is_show' => false,
            'tip_title' => '',
            'quantity_yun_text' => '',
            'is_show_customer_qrcode' => false,
            'work_wx_customer_qrcode' => null,
        ];

        $quantityYun = 0;
        $quantityNotYun = 0;
        $user = $this->request->user;
        $quantity = $this->request->post('quantity');
        $isYunOpen = invoke([YunOpen::class, 'checkSplitScoreYunOpen'], [getCurrentVersion(), $user['create_time'], $user['id']]);

        $userScore = UserScore::where('user_id', '=', $user['id'])
            ->where('data_state', '=', 0)
            ->field(['not_yun_score', 'yun_score'])
            ->find();

        // 提现金额小于非云账户积分账户金额
        if (!empty($userScore) && $quantity <= $userScore['not_yun_score']) {
            $quantityNotYun = $quantity;
        }

        // 提现金额大于非云账户积分账户金额，且非云账户金额不为0
        if (!empty($userScore) && $quantity > $userScore['not_yun_score'] && $userScore['not_yun_score'] > 0) {
            $quantityNotYun = (float)$userScore['not_yun_score'];
            $quantityYun = bcsub($quantity, $userScore['not_yun_score'], 2);

            // 如果云账户提现的金额小于1了，就从非云账户直接扣钱
            $yunMinWithdrawalAmount = Config::getValueByKey('yun_min_withdrawal_amount');
            if ($quantityYun < $yunMinWithdrawalAmount && $quantityYun > 0) {
                $quantityYun = 0;
                $quantityNotYun = $quantity;
            }

            // 如果非云账户打款小于0.1了，就从云账户直接扣钱
            $minWithdrawalAmount = Config::getValueByKey('min_withdrawal_amount');
            if ($quantityNotYun < $minWithdrawalAmount && $quantityNotYun > 0) {
                $quantityNotYun = 0;
                $quantityYun = $quantity;
            }
        }

        // 提现金额大于非云账户积分账户金额，且非云账户金额==0
        if (!empty($userScore) && $quantity > $userScore['not_yun_score'] && $userScore['not_yun_score'] <= 0) {
            $quantityYun = $quantity;
        }

        $quantityYunCommission = Config::getValueByKey('yun_transfer_score_commission');
        $quantityYunCommission = $quantityYunCommission > 1 ? 1 : $quantityYunCommission;
        // 需要扣掉的钱
        $quantityYunCommission = ceil(bcmul($quantityYun, $quantityYunCommission, 3) * 100) / 100;
        // 第一笔提现+第二笔提现（扣掉手续费） = 本次申请最后的钱
        $lastYunQuantity = bcadd(bcsub($quantityYun, $quantityYunCommission, 2), $quantityNotYun, 2);
        $data['quantity_not_yun'] = $quantityNotYun ?: 0;
        $data['quantity_yun'] = $quantityYun;
        $data['quantity_yun_commission'] = $quantityYunCommission;

        if ($quantityYun && $isYunOpen) {

            if (empty($quantityNotYun) && $quantityYunCommission <= 0) {
                return json(HBKReturn('success', $data, 200));
            }

            if (empty($data['quantity_not_yun'])) {
                $text = '本次申请提现共' . $lastYunQuantity . '元，请注意查收';
            } else {
                $text = '本次申请提现' . $lastYunQuantity . '元将{highlightStart}分两笔金额进行转账{highlightEnd}，请注意查收。';
                $replaceTag = Config::getValueByKey('high_light_replace_tag');
                $replaceTag = json_decode($replaceTag, true);
                $text = str_replace('{highlightStart}', $replaceTag['highlightStart'], $text);
                $text = str_replace('{highlightEnd}', $replaceTag['highlightEnd'], $text);
            }

            $data['is_show'] = true;
            $data['tip_title'] = $text;
            $data['quantity_yun_text'] = '预计到账金额' . $lastYunQuantity . '元';
        }

        $isAllow = invoke(['\app\client\service\Withdrawal', 'checkRepurchaseCardActivityUser'], [$user]);
        if (!$isAllow) {
            $data['is_show_customer_qrcode'] = true;
            $data['work_wx_customer_qrcode'] = Config::getValueByKey('work_wx_customer_qrcode');
        }

        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 校验用户支付宝是否为空
     * @return Json
     */
    public function save_user_phone(): Json
    {
        $user = $this->request->user;
        $params = $this->request->post(['phone' => null, 'code' => null, 'platform_abbr' => null]);
        $this->validate($params, VUser::class . '.save_user_phone');
        $data = invoke([SUser::class, 'saveUserPhone'], [$user, $params]);
        return json(HBKReturn('success', $data, 200));
    }

    public function get_recom_list()
    {
        $user = $this->request->user;
        $params = $this->request->get([
            'lat' => 0,
            'lng' => 0,
        ]);
        $this->validate($params, VUser::class . '.get_recom_list');
        // 用户报名最多的店铺品类标签
        // 首先获取所有的店铺
        $task = new TaskV20180();
        $task->setVersion(getCurrentVersion());
        $list = $task->getRecomList($user['id'], $params['lat'], $params['lng']);
        return json(HBKReturn('success', ['list' => $list], 200));
    }

    /**
     * 屏蔽成人用品
     * @return Json
     */
    public function mute(): Json
    {
        $user = $this->request->user;
        $data = invoke([SUser::class, 'muteStoreCategoryTag'], [$user]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 取消屏蔽成人用品
     * @return Json
     */
    public function unmute(): Json
    {
        $user = $this->request->user;
        $data = invoke([SUser::class, 'unmuteStoreCategoryTag'], [$user]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 设置
     * @return Json
     */
    public function setting(): Json
    {
        $user = $this->request->user;
        $data = invoke([SUser::class, 'userTagDisable'], [$user['id']]);
        return json(HBKReturn('success', [
            'adult_goods_open' => (int)!empty($data)
        ], 200));
    }
}
