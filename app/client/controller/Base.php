<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/2/9 11:40
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\BaseController;
use app\client\model\User;
use app\common\exception\ExceptionCode;
use app\common\exception\TokenException;
use app\common\facade\Redis;
use think\exception\ValidateException;

abstract class Base extends BaseController
{
    public function getUserByToken()
    {
        // 获取请求头信息
        $token = $this->request->header('xx-token') ?: $this->request->param('xx_token');
        try {
            // 验证token
            $userId = invoke(['app\common\service\Token', 'checkToken'], [$token, Redis::$user_login_token]);
            if (empty($userId)) {
                throw new TokenException(ExceptionCode::TOKEN_VALIDATION_FAILED);
            }
            $user = User::get_by_id($userId, [
                'status' => 0,
                'data_state' => 0,
            ]);
            if (empty($user)) {
                throw new TokenException(ExceptionCode::TOKEN_VALIDATION_FAILED . " " . $userId);
            }
            return $userId;
        } catch (\Exception $e) {
            return null;
        }
    }
}
