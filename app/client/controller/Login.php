<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/7/7 10:53
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\client\service\login\AppletQuickLogin;
use app\client\service\login\MobileLogin;
use app\client\service\login\SMSLogin;
use app\client\service\login\WechatLogin;
use app\client\service\User;
use app\common\facade\Redis;
use think\response\Json;

class Login extends Base
{
    /**
     * 发送短信验证码
     *
     * @request method:POST desc:请求信息
     * @body name:mobile type:string desc:手机号
     *
     * @return Json
     */
    public function send_sms_code(): Json
    {
        $mobile = $this->request->post('mobile', '', 'trim');
        $position = $this->request->post(['lat' => 0, 'lng' => 0], 0, 'floatval');
        // 验证
        $this->validate(compact('mobile'), [
            'mobile' => ['require', 'mobile']
        ]);
        //同一ip一天最多50条
        invoke(['app\common\service\SMSVerificationCode', 'allowOne'],
            ['/login/index', get_client_ip(0, true), 50]);
        // 同一个手机号一天最多20条
        invoke(['app\common\service\SMSVerificationCode', 'allowOne'], ['/login/index', $mobile, 20]);
        // 发送短信
        $data = invoke(['app\common\service\SMSVerificationCode', 'sendCode'],
            [$mobile, Redis::$user_login_sms_code, $position]);
        // 返回
        return json(HBKReturn('短信发送成功！', $data, 200));
    }

    public function mobileLogin(): Json
    {
        /** @var MobileLogin $loginService */
        $loginService = app(MobileLogin::class, [], true);
        $data = $loginService->login();
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 短信登录
     * @return Json
     */
    public function sms_login(): Json
    {
        $mobile = $this->request->post('mobile', '', 'trim');
        $code = $this->request->post('code');
        // 验证
        $this->validate(compact('mobile', 'code'), [
            'mobile' => ['require', 'mobile'],
            'code' => 'require'
        ]);
        // web端禁止注册
        // 测试账号验证码检查
        $mobile_whitelist = ['18969137622', '18768177608', '13205710602'];
        if (!in_array($mobile, $mobile_whitelist) || $code !== '1234') {
            invoke(['app\common\service\SMSVerificationCode', 'checkCode'], [$mobile, $code,
                Redis::$user_login_sms_code, Redis::$user_login_sms_code_error]);
        }
        $loginService = new SMSLogin($mobile);
        $data = $loginService->login();
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 小程序,微信授权一键登录
     * @return Json
     */
    public function applet_quick_login()
    {
        /** @var AppletQuickLogin $loginService */
        $loginService = app(AppletQuickLogin::class, [], true);
        $data = $loginService->login();
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 微信登录
     * @param string $from_app
     * @return Json
     */
    public function wechat_login(string $from_app = ""): Json
    {
        $version = $this->request->post('version', 20041, 'intval');
        $loginService = new WechatLogin($from_app);
        $data = $loginService->login();
        return json(HBKReturn('success', $data, 200));
    }

    public function logout(): Json
    {
        $userId = $this->getUserByToken();

        if (empty($userId)) {
            return json(HBKReturn('success', [], 200));
        }

        $token = $this->request->header('xx-token') ?: $this->request->param('xx-token');

        //必须是app的退出，才可以清楚极光推送RegistrationId
        if (in_array($this->request->platform, ['ios', 'android'])) {
            invoke([User::class, 'clearRegistrationId'], [$userId]);
        }

        invoke(['app\common\service\Token', 'clearToken'], [$token, Redis::$user_login_token]);
        return json(HBKReturn('success', [], 200));
    }
}
