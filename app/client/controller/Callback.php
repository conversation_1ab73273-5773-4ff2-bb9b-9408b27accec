<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/7/18 15:21
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\api\exception\IdempotentException;
use app\api\implement\middleman\MiddlemanFactory;
use app\api\validate\Middleman as Validate;
use app\common\model\OrderRepurchaseCard;
use app\common\model\RepurchaseCardTask as MRepurchaseCardTask;
use app\client\model\{AppPush,
    Order,
    OrderCashBackBill,
    OrderDelayedPayment,
    OrderInfo,
    OrderThird,
    OrderVerify,
    OrderVideo as OrderVideoModel,
    Platform,
    Task};
use app\client\service\Agent;
use app\client\service\Order as SOrder;
use app\common\model\OrderGeneral;
use app\common\model\OrderRebateBill;
use app\common\model\TaskCashbackType;
use app\common\service\OrderHtl;
use app\common\service\RatioTask;
use app\Log;
use think\Exception;
use think\exception\ValidateException;
use think\facade\Db;
use think\response\Json;

class Callback extends Base
{
    protected static array $pay_error_code = [
        'PAYEE_CARD_INFO_ERROR',
        'CARD_BIN_ERROR',
        'PAYEE_CARD_INFO_ERROR',
        'PAYEE_NOT_EXIST',
        'PAYEE_ACCOUNT_STATUS_ERROR',
        'PERMIT_NON_BANK_LIMIT_PAYEE',
        'PAYEE_TRUSTEESHIP_ACC_OVER_LIMIT',
        'PAYEE_ACCOUNT_NOT_EXSIT',
        'PAYEE_ACCOUNT_NOT_EXIST',
        'PAYEE_USERINFO_STATUS_ERROR',
        'TRUSTEESHIP_RECIEVE_QUOTA_LIMIT',
        'EXCEED_LIMIT_DC_RECEIVED',
        'PAYEE_ACC_OCUPIED',
        'EXCEED_LIMIT_UNRN_DM_AMOUNT',
        'RELEASE_USER_FORBBIDEN_RECIEVE',
    ];

    public function pay_to_cash_back(): Json
    {
        $params = $this->request->post();
        Log::info('clientCallback', 'pay_to_cash_back', '订单返现打款', $params['id'], $params);
        $order = Order::get_one([
            'id' => $params['id'],
            'data_state' => 0
        ]);
        if (empty($order)) {
            throw new Exception('无此订单');
        }
        if ($order['order_status'] !== 4 || $order['payment_status'] !== 1) {
            throw new Exception('订单状态不正确');
        }

        $res = $params['alipayResult'];
        if (!empty($res['code']) && $res['code'] == 10000) {//成功
            $order->payment_status = 2;
            $order->save();
            OrderCashBackBill::where('id', '=', $params['id'])
                ->update([
                    'cash_back_status' => 2,
                    'cash_back_payment_trade_no' => $res['pay_fund_order_id'],
                    'cash_back_payment_transfer_response' => json_encode($res, 256),
                    'cash_back_trans_date' => $res['trans_date']//打款成功时间
                ]);
            $price = OrderCashBackBill::get_value('cash_back_amount', ['id' => $params['id']]);
            $recordData = [
                'amount' => $price,
                'agent_id' => $order['agent_id'],
                'type' => 4,
                'order_id' => $order['id'],
                'task_id' => $order['task_id'],
                'user_id' => $order['user_id'],
                'trans_date' => $res['trans_date'],
                'out_biz_no' => $res['out_biz_no'],
                'data' => json_encode($res, 256)
            ];
            invoke([Agent::class, 'changeAgentAmountByTrans'], [$price, $order['agent_id'], $recordData]);
        } else {
            if (!empty($res) && in_array($res['sub_code'], self::$pay_error_code)) {
                $order->payment_status = 3;
                $order->save();
                OrderCashBackBill::where('id', '=', $params['id'])
                    ->update([
                        'cash_back_status' => 3,
                        'cash_back_payment_fail_reason' => $res['sub_msg'],
                        'cash_back_payment_transfer_response' => json_encode($res, 256)
                    ]);
                OrderInfo::where('id', '=', $params['id'])
                    ->update(['alipay_error_tips' => 1]);
            } else {
                $order->payment_status = 3;
                $order->save();
                OrderCashBackBill::where('id', '=', $params['id'])
                    ->update([
                        'cash_back_status' => 3,
                        'cash_back_payment_fail_reason' => '返现失败，请联系客服。',
                        'cash_back_payment_transfer_response' => json_encode($res, 256)
                    ]);
            }
        }
        return json(HBKReturn('success', [], 200));
    }

    // TODO 金币提现后的下个版本删除
    public function pay_to_rebate(): Json
    {
        $params = $this->request->post();
        Log::info('clientCallback', 'pay_to_rebate', '订单推广返现打款', $params['id'], $params);
        $order = Order::get_one([
            'id' => $params['id'],
            'data_state' => 0
        ]);
        if (empty($order)) {
            throw new Exception('无此订单');
        }
        if ($order['order_status'] !== 4) {
            throw new Exception('订单状态不正确');
        }

        $res = $params['alipayResult'];
        if (!empty($res['code']) && $res['code'] == 10000) {//成功
            OrderRebateBill::where('id', '=', $params['id'])
                ->update([
                    'rebate_status' => 2,
                    'rebate_payment_trade_no' => $res['pay_fund_order_id'],
                    'rebate_payment_transfer_response' => json_encode($res, 256),
                    'rebate_payment_time' => $res['trans_date']//打款成功时间
                ]);
            $price = OrderRebateBill::get_value('rebate_amount', ['id' => $params['id']]);
            $recordData = [
                'amount' => $price,
                'agent_id' => $order['agent_id'],
                'type' => 5,
                'order_id' => $order['id'],
                'task_id' => $order['task_id'],
                'user_id' => $order['user_id'],
                'trans_date' => $res['trans_date'],
                'out_biz_no' => $res['out_biz_no'],
                'data' => json_encode($res, 256)
            ];
            invoke([Agent::class, 'changeAgentAmountByTrans'], [$price, $order['agent_id'], $recordData]);
        } else {
            if (!empty($res) && in_array($res['sub_code'], self::$pay_error_code)) {
                OrderRebateBill::where('id', '=', $params['id'])
                    ->update([
                        'rebate_status' => 3,
                        'rebate_payment_fail_reason' => $res['sub_msg'],
                        'rebate_payment_transfer_response' => json_encode($res, 256)
                    ]);
            } else {
                OrderRebateBill::where('id', '=', $params['id'])
                    ->update([
                        'rebate_status' => 3,
                        'rebate_payment_fail_reason' => '返现失败，请联系客服。',
                        'rebate_payment_transfer_response' => json_encode($res, 256)
                    ]);
            }
        }
        return json(HBKReturn('success', [], 200));
    }

    /**
     * 机审回调
     *
     * @return \think\response\Json
     * @throws \think\Exception
     */
    public function black_audit_callback(): Json
    {
        $params = $this->request->post();
        Log::info('clientCallback', 'black_audit_callback', '订单机审开始', $params['orderId'], $params);
        Db::startTrans();
        try {
            /** @var Order $order */
            $order = Order::get_by_id($params['orderId'], [], ['lock' => true]);
            /** @var Task $task */
            $task = Task::get_by_id($order['task_id']);
            if ($order['order_status'] !== 3 || $order['machine_audit_status'] !== 0) {
                throw new Exception('订单状态不对，无需机审');
            }

            $platformAbbr = Platform::where('id', $order['platform_id'])
                ->value('platform_abbreviation');
            $orderThird = OrderThird::where('order_id', $order['id'])->find();
            $orderGeneral = OrderGeneral::where('order_id', $order['id'])->find();
            $orderRepurchaseCard = OrderRepurchaseCard::where('order_id', $order['id'])->find();

            if (empty($task) && in_array($platformAbbr, [Platform::PLT_HTL_MT, Platform::PLT_HTL_ELE,])) {
                if (!empty($orderThird)) {
                    $task = new Task();
                    $task->task_start_time = $orderThird['task_start_time'];
                    $task->task_end_time = $orderThird['task_end_time'];
                    $task->store_longitude = $orderThird['longitude'];
                    $task->store_latitude = $orderThird['latitude'];
                    $task->is_machine_audit = 1;
                }
            }

            if (empty($task) && Platform::isGeneral($platformAbbr)) {
                if (!empty($orderGeneral)) {
                    $timestamp = strtotime($orderGeneral['create_time']);
                    $task = new Task();
                    $task->task_start_time = date('Y-m-d 00:00:00', $timestamp);
                    $task->task_end_time = date('Y-m-d 23:59:59', $timestamp);
                    $task->is_machine_audit = 1;
                }
            }
            if (empty($task) && Platform::isRepurchaseCard($platformAbbr)) {
                if (!empty($orderRepurchaseCard)) {
                    $repurchaseCardTask = MRepurchaseCardTask::where('id', '=', $orderRepurchaseCard['repurchase_card_task_id'])->find();
                    $timestamp = strtotime($orderRepurchaseCard['create_time']);
                    $task = new Task();
                    $task->task_start_time = date('Y-m-d 00:00:00', $timestamp);
                    $task->task_end_time = date('Y-m-d 23:59:59', $timestamp);
                    $task->is_machine_audit = 1;
                    $task->store_latitude = $repurchaseCardTask['store_latitude'];
                    $task->store_longitude = $repurchaseCardTask['store_longitude'];
                }
            }

            /** @var SOrder $orderService */
            $orderService = app(SOrder::class);
            $error = $orderService->machineAuditProcessing($order, $task, $params, $sms, $platformAbbr);
            $error_str = '';
            $orderStatus = null;

            // 对于返比例的自营活动,获取到实付金额后，重新计算
            if (!empty($order['task_id'])) {
                $taskExt = TaskCashbackType::where('task_id', $order['task_id'])
                    ->where('type', 1)
                    ->find();

                $orderStatus = invoke([\app\client\service\order\Order::class, 'checkIsIndependentAuditOrRedEnvelopAudit'], [$order]);

                if (!empty($error)) {
                    $orderIsCompleted = false;
                } else {
                    if (empty($orderThird) && $orderStatus) {
                        $orderIsCompleted = true;
                    } elseif ($task['is_machine_audit'] === 1 || ($task['bill_status'] === 2 && $task['is_pre_pay'] === 1)) {
                        $orderIsCompleted = true;
                    } else {
                        $orderIsCompleted = false;
                    }
                }

                !empty($taskExt) && invoke([RatioTask::class, 'recalculateOrderCashback'], [
                    !empty($task['id']) ? $task['id'] : 0,
                    $order['id'],
                    $order['take_out_paid_amount'],
                    $orderIsCompleted
                ]);
            }

            // 机审不通过
            if (!empty($error)) {
                $order->machine_audit_status = -1;
                foreach ($error as $key => $value) {
                    $error_str .= ($key + 1) . '.' . $value . ' ';
                }
                $order->machine_audit_remark = $error_str;
                $order->save();
                // 机审不通过，如果是灰太狼，
                if (in_array($platformAbbr, [Platform::PLT_HTL_MT, Platform::PLT_HTL_ELE,])) {
                    invoke([OrderHtl::class, 'machineAuditFailed'], [$order['id'], $params]);
                }
                Db::commit();
                return json(HBKReturn('success', [], 200));
            }

            // #18527 上传视频后订单不延迟审核，直接开始机审，但是机审不能通过
            $orderDelayed = OrderDelayedPayment::where('order_id', $order['id'])
                ->find();

            if (!empty($orderDelayed)) {// 没有被延迟，即使上传了视频，也不卡机审
                $orderVideo = OrderVideoModel::where('order_id', $order['id'])
                    ->find();

                if (!empty($orderVideo)) {
                    $order->machine_audit_status = -1;
                    $order->machine_audit_remark = '1. 订单上传视频，请人工核对后通过';
                    $order->save();
                    // 机审不通过，如果是灰太狼，
                    if (in_array($platformAbbr, [Platform::PLT_HTL_MT, Platform::PLT_HTL_ELE,])) {
                        invoke([OrderHtl::class, 'machineAuditFailed'], [$order['id'], $params]);
                    }
                    Db::commit();
                    return json(HBKReturn('success', [], 200));
                }
            }

            // 默认为true
            $isCompleted = true;

            // 这里判断如果是灰太狼订单，要特殊处理
            if (in_array($platformAbbr, [Platform::PLT_HTL_MT, Platform::PLT_HTL_ELE,])) {
                $isCompleted = invoke([OrderHtl::class, 'machineAuditPass'], [$order['id'], $params]);
            }
            // 通用活动不直接打款（后台开启了截图上传）
            if (Platform::isGeneral($platformAbbr) && OrderVerify::where('order_id', $order['id'])->value('order_switch_way_ctrl') == 2) {
                $order->machine_audit_status = -1;
                $order->machine_audit_remark = '1. 订单上传视频，请人工核对后通过';
                $order->save();
                $isCompleted = false;
            }

            if ($isCompleted) {
                // 机审通过，并打款
                $order->machine_audit_status = 1;
                $orderPassClient = $orderService->machineAuditForOrderPass($task, $order, $orderThird, $orderStatus);
            } else {
                // 机审通过(灰太狼回调没有通过)，不打款，(目前针对灰太狼,通用活动)
                $order->machine_audit_status = 1;
                $order->save();
            }

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('clientCallbackBlackAuditCallback', $e, '订单机审回调，机审失败', $order['id'], $params);
            throw new Exception('机审失败。');
        }

        if (!empty($orderPassClient) && !empty($isCompleted)) {
            $orderPassClient->afterTransHandle();
        }

        return json(HBKReturn('success', [], 200));
    }

    /**
     * 给中间人打款回调
     *
     * @return Json
     * @throws \Exception
     */
    public function pay_to_middleman(): Json
    {
        $params = $this->request->post();
        $this->validate($params, Validate::class);

        Log::info('middlemanCallback', '中间人打款回调日志', '中间人打款回调日志',
            $params['agent_middle_bill_id'], $params);

        /** @var MiddlemanFactory $factory */
        $factory = app(MiddlemanFactory::class);
        $payment = $factory->createMiddlemanObject($params);

        Db::startTrans();
        try {
            $payment->getAgentMiddlemanBill();
            $payment->changeStatus();
            Db::commit();
        } catch (IdempotentException $e) {
            //密等类异常，这类异常不抛错
            Db::rollback();
            Log::error('middlemanCallbackFailed', $e, '中间人打款回调失败',
                $params['agent_middle_bill_id']);
        } catch (ValidateException $e) {
            Db::rollback();
            Log::error('middlemanCallbackFailed', $e, '中间人打款回调失败',
                $params['agent_middle_bill_id']);
            throw $e;
        } catch (\Exception|\Error $e) {
            Db::rollback();
            Log::error('middlemanCallbackFailed', $e, '中间人打款回调失败',
                $params['agent_middle_bill_id']);
            throw new \Exception("Error");
        }
        return json(HBKReturn('success', [], 200));
    }

    public function notice_jpush_app_push(): Json
    {
        $params = $this->request->post();
        $msgId = $params[0]['msgid'] ?? null;
        Log::info('clientCallback', 'notice_jpush_app_push', '极光回调', $msgId, $params);
        //处理送达回调
        if (!empty($msgId)) {
            //送达成功
            if ($params[0]['callback_type'] === '0') {
                AppPush::where(['identification_id' => $msgId])->update([
                    'status' => 2
                ]);
            }
        }
        return json(HBKReturn('success', [], 200));
    }
}
