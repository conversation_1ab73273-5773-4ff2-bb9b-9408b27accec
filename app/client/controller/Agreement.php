<?php

namespace app\client\controller;


use app\client\service\Agreement as SAgreement;
use think\response\Json;

class Agreement extends Base
{
    protected $middleware = [
        'checkToken'
    ];

    /**
     * 订单跳转审核-协议
     * @return Json
     */
    public function order_redirect_audit(): Json
    {
        $user = $this->request->user;
        $data = invoke([SAgreement::class, 'order_redirect_audit'], [$user['id']]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * @return Json
     */
    public function save_order_redirect_audit()
    {
        $user = $this->request->user;
        $data = invoke([SAgreement::class, 'save_order_redirect_audit'], [
            $user['id'],
            getCurrentVersion(),
            getCurrentPlatform()
        ]);
        return json(HBKReturn('success', $data, 200));
    }
}