<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/7/14 11:32
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\client\model\Config as MConfig;
use app\client\model\Order as OrderModel;
use think\response\Json;

class Config extends Base
{
    protected $middleware = [
        'checkToken' => [
            'except' => ['get_public_config']
        ]
    ];

    public function get_customer_service_qr_code(): Json
    {
        $customerServiceQrCode = MConfig::getValueByKey('customer_service_qr_code');
        return json(HBKReturn('success', ['customer_service_qr_code' => $customerServiceQrCode], 200));
    }

    public function get_manual_review_msg(): Json
    {
        $manualReviewMsg = [];
        $manualReview = MConfig::get_one(['key' => 'manual_review_msg'], ['field' => ['type', 'value']]);
        if (!empty($manualReview) && $manualReview['type'] === 'text') {
            $manualReviewMsg = json_decode($manualReview['value'], true);
        }
        return json(HBKReturn('success', ['manual_review_msg' => $manualReviewMsg], 200));
    }

    public function get_order_review_msg(): Json
    {
        $user = $this->request->user;
        $orderNum = OrderModel::where('user_id', $user['id'])
            ->where('data_state',  0)
            ->where('order_status',  1)
            ->whereTime('create_time', '>=', date('Y-m-d'))
            ->count();
        $review = MConfig::getValueByKey('order_review_msg');
        $review = str_replace('{$numText}', $orderNum, $review);
        $reviewMsg = json_decode($review, true);
        return json(HBKReturn('success', $reviewMsg, 200));
    }

    /**
     * 通用配置
     * @return Json
     */
    public function get_public_config()
    {
        $platform = getCurrentPlatform();

        if (!empty($platform) && $platform === 'android') {
            // 开屏广告优先推的广告商
            $priority = MConfig::getValueByKeys([
                'screen_ad_priority_for_android',
                'order_list_ad_for_android',
                'order_detail_ad_for_android',
                'activity_detail_ad_for_android',
                'energy_site_ad_for_android',
                'my_page_ad_for_android',
                'withdraw_ad_for_android',
            ]);
            $advPriorityList = MConfig::getValueByKey('screen_ad_list_for_android');
            $advPriorityList = $advPriorityList ? explode(",", $advPriorityList) : [];
            $data = [
                'priority' => $priority['screen_ad_priority_for_android'],
                'adv_priority_list' => $advPriorityList,
                'feed_ad' => [
                    'order_list_ad' => $priority['order_list_ad_for_android'] ?? 0,
                    'order_detail_ad' => $priority['order_detail_ad_for_android'] ?? 0,
                    'activity_detail_ad' => $priority['activity_detail_ad_for_android'] ?? 0,
                    'energy_site_ad' => $priority['energy_site_ad_for_android'] ?? 0,
                    'my_page_ad' => $priority['my_page_ad_for_android'] ?? 0,
                    'withdraw_ad' => $priority['withdraw_ad_for_android'] ?? 0,
                ],
            ];
        } else {
            // 开屏广告优先推的广告商
            $priority = MConfig::getValueByKeys([
                'screen_ad_priority',
                'order_list_ad_for_ios',
                'order_detail_ad_for_ios',
                'activity_detail_ad_for_ios',
                'energy_site_ad_for_ios',
                'my_page_ad_for_ios',
                'withdraw_ad_for_ios',
            ]);
            $advPriorityList = MConfig::getValueByKey('screen_ad_list_for_ios');
            $advPriorityList = $advPriorityList ? explode(",", $advPriorityList) : [];
            $data = [
                'priority' => $priority['screen_ad_priority'],
                'adv_priority_list' => $advPriorityList,
                'feed_ad' => [
                    'order_list_ad' => $priority['order_list_ad_for_ios'] ?? 0,
                    'order_detail_ad' => $priority['order_detail_ad_for_ios'] ?? 0,
                    'activity_detail_ad' => $priority['activity_detail_ad_for_ios'] ?? 0,
                    'energy_site_ad' => $priority['energy_site_ad_for_ios'] ?? 0,
                    'my_page_ad' => $priority['my_page_ad_for_ios'] ?? 0,
                    'withdraw_ad' => $priority['withdraw_ad_for_ios'] ?? 0,
                ],
            ];
        }

        return json(HBKReturn('success', $data, 200));
    }
}
