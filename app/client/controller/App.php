<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/8/25 10:48
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\admin\exception\TransactionException;
use app\client\model\{AppVersion, Config, UserVersion};
use app\client\service\User;
use app\client\service\YunOpen;
use app\common\model\UserCompetitorApp;
use app\Log;
use think\exception\ValidateException;
use think\facade\Db;
use think\helper\Str;
use think\response\Json;

class App extends Base
{
    protected $middleware = [
        'checkToken' => [
            'only' => ['set_registration_id', 'update_notice_by_version', 'score_yun_open', 'gold_yun_open',
                'setting_yun_open', 'truncate']
        ]
    ];

    public function update_notice_by_version(): Json
    {
        $scene = $this->request->get('scene');

        if (empty($scene)) {
            throw new ValidateException('缺少必要参数');
        }

        $user = $this->request->user;
        $version = $this->request->version;// 20042
        $platform = $this->request->platform;// ios
        $userVersion = UserVersion::where('user_id', '=', $user['id'])
            ->where('scene', '=', $scene)
            ->find();

        $first = empty($userVersion);

        if ($first) {
            UserVersion::insert([
                'user_id' => $user['id'],
                'version' => $version,
                'platform' => $platform,
                'scene' => $scene
            ]);
        }

        return json(HBKReturn('success', ['first' => $first], 200));
    }

    /**
     * 获取更新版本
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function get_update_version(): Json
    {
        $params = $this->request->get(['platform', 'version']);
        // 更新类型（1：整包更新，2：热更新）
        // 是否强制更新(1:是，2：否) 老30 新31 最低30 29不更新
        $data = AppVersion::get_one([
            'app_update_type' => 1,
            ['app_version_code', '>', $params['version']],
            ['app_lowest_version', '<=', $params['version']],
            ['platform', '=', strtolower($params['platform'])],
            ['is_enabled', '=', 1],
            ['data_state', '=', 0],
            ['update_enabled_time', '<=', date('Y-m-d H:i:s')]
        ], [
            'field' => ['id', 'app_name', 'app_version_code', 'app_version_name', 'app_update_type',
                'app_resource_url', 'is_force', 'platform_ext_info', 'text'],
            'order' => ['app_version_code' => 'desc']
        ]);
        if (empty($data)) {
            $data = AppVersion::get_one([
                'app_update_type' => 2,
                ['app_version_code', '>', $params['version']],
                ['app_lowest_version', '<=', $params['version']],
                ['platform', '=', strtolower($params['platform'])],
                ['is_enabled', '=', 1],
                ['data_state', '=', 0],
                ['update_enabled_time', '<=', date('Y-m-d H:i:s')]
            ], [
                'field' => ['id', 'app_name', 'app_version_code', 'app_version_name', 'app_update_type',
                    'app_resource_url', 'is_force', 'platform_ext_info', 'text'],
                'order' => ['app_version_code' => 'desc']
            ]);
        } else {
            switch (strtolower($params['platform'])) {
                case 'android':
                    $key = 'android.update.url';
                    $data['app_resource_url'] = Config::get_value('value', ['key' => $key]);
                    break;
                case 'ios':
                    $key = 'ios.update.url';
                    $data['app_resource_url'] = Config::get_value('value', ['key' => $key]);
                    break;
                default:
                    break;
            }
        }
        !empty($data) && $data['platform_ext_info'] = json_decode($data['platform_ext_info'], true) ?? [];
        if (!empty($data) && !empty($data['text'])) {
            $textList = json_decode($data['text'], true) ?? [];
            foreach ($textList as $key => $value) {
                $textList[$key] = ($key + 1) . '、' . $value;
            }
            $data['text'] = $textList;
        }
        return json(HBKReturn('success', $data, 200));
    }

    public function set_registration_id(): Json
    {
        $user = $this->request->user;
        $platform = $this->request->platform;
        $registrationId = $this->request->post('registration_id');
        $gtRegistrationId = $this->request->post('gt_registration_id');
        if (empty($registrationId)) {
            throw new ValidateException('registration_id不允许为空');
        }
        if (!in_array(Str::lower($platform), ['ios', 'android'])) {
            throw new ValidateException('登录平台不正确');
        }
        Db::startTrans();
        try {
            invoke([User::class, 'setRegistrationId'], [$user['id'], $registrationId, $gtRegistrationId]);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            throw new TransactionException($e->getMessage());
        }
        return json(HBKReturn('success', [], 200));
    }

    public function gold_yun_open(): Json
    {
        $user = $this->request->user;
        $yunOpen = invoke([YunOpen::class, 'checkGoldYunOpen'], [$user['id']]);
        return json(HBKReturn('success', ['yun_open' => $yunOpen], 200));
    }

    public function score_yun_open(): Json
    {
        $version = $this->request->version;
        $user = $this->request->user;
        $yunOpen = invoke([YunOpen::class, 'checkScoreYunOpen'], [$version, $user['create_time'], $user['id']]);
        return json(HBKReturn('success', ['yun_open' => $yunOpen], 200));
    }

    public function setting_yun_open(): Json
    {
        $version = $this->request->version;
        $user = $this->request->user;
        $yunOpen = invoke([YunOpen::class, 'getYunOpen'], [$version, $user['create_time'], $user['id']]);
        return json(HBKReturn('success', ['yun_open' => $yunOpen], 200));
    }

    public function main_control(): Json
    {
        $platform = Str::lower($this->request->get('platform', 'web'));
        $openRegimentalCommanderPage = invoke(['app\client\service\App', 'getOpenRegimentalCommanderPage'], [$platform]);

        return json(HBKReturn('success', [
            'open_regimental_commander_page' => $openRegimentalCommanderPage
        ], 200));
    }

    /**
     * app端添加日志
     * 需要记录的信息：类型，内容，端，版本，用户，设备号
     * 类型：1图片上传
     * @return Json
     */
    public function add_error_log()
    {
        $params = $this->request->post(['type', 'msg' => '', 'device_id' => '', 'os_version' => '']);

        if (empty($params['type'])) {
            Log::error('SaveAppLogError', $params['msg'], '前端没传type', $params['type'], $params);
            return json(HBKReturn('success', [], 200));
        }

        $params['version'] = $this->request->header('xx-version') ?: $this->request->param('xx_version');
        $params['platform'] = $this->request->header('xx-platform') ?: $this->request->param('xx_platform');
        $params['user_id'] = $this->getUserByToken();

        if (empty($params['user_id'])) {
            //当用户id不存在，就是记录的公共日志
            Log::error('SaveAppLogPublic', $params['msg'], '记录app公共日志', $params['type'], $params);
            return json(HBKReturn('success', [], 200));
        }

        Log::error('SaveAppLog', $params['msg'], '记录app日志', $params['type'], $params);
        return json(HBKReturn('success', [], 200));
    }

    /**
     * 追踪竞争对手app
     * @return Json
     */
    public function truncate()
    {
        $app = $this->request->post('a', []);
        $user = $this->request->user;
        $appList = [UserCompetitorApp::XIAO_CAN, UserCompetitorApp::WAI_MAI, UserCompetitorApp::CDD, UserCompetitorApp::HTL];
        $insertData = [];
        if (is_array($app)) {
            $duplicateApps = array_intersect($app, $appList);
            foreach ($duplicateApps as $item) {
                // 已上报过的APP
                $lastApp = UserCompetitorApp::where('user_id', $user['id'])
                    ->where('competitor_app', $item)
                    ->find();
                if (empty($lastApp)) {
                    $insertData[] = [
                        'user_id' => $user['id'],
                        'competitor_app' => $item
                    ];
                } else {
                    $lastApp->data_state = 0;
                    $lastApp->save();
                }
            }
        }
        !empty($insertData) && UserCompetitorApp::insertAll($insertData);
        UserCompetitorApp::whereNotIn('competitor_app', is_array($app) ? $app : [])
            ->where('user_id', $user['id'])
            ->update(['data_state' => 1]);
        return json(HBKReturn('success', [], 200));
    }
}
