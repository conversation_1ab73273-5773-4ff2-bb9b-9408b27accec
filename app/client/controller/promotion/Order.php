<?php

namespace app\client\controller\promotion;

use app\client\facade\SubmitOrderFactory;
use app\client\model\Order as MOrder;
use app\client\model\OrderDelayedPayment;
use app\client\model\Platform;
use app\client\service\Order as SOrder;
use app\common\exception\ExceptionCode;
use app\common\service\HttpRpc;
use think\Exception;
use think\exception\ValidateException;
use think\facade\Db;
use app\Log;

class Order extends Base
{
    protected $middleware = [
        'CheckPromotionToken'
    ];

    public function get_order_list_status(): \think\response\Json
    {
        $statusList = MOrder::getStatusList();
        $data = ['list' => $statusList];
        return json(HBKReturn('success', $data, 200));
    }

    public function get_order_list_by_user(): \think\response\Json
    {
        $user = $this->request->user;
        $status = $this->request->get('status', 0, 'intval');
        $options = $this->request->get(['page' => 0, 'limit' => 10]);
        $options['field'] = ['id', 'order_no', 'platform_id' => 'platform', 'task_cover', 'store_name', 'meal_price',
            'cash_back_amount', 'order_status', 'cancel_status', 'create_time', 'order_rejection_reasons', 'is_expired',
            'payment_status', 'is_praise', 'praise_demand'];
        $options['order'] = ['create_time' => 'desc', 'id' => 'desc'];
        $where = [
            ['data_state', '=', 0],
            ['user_id', '=', $user['id']]
        ];
        switch ($status) {
            case 2:
                $where[] = ['payment_status', '=', 2];
                break;
            case 3:
                $where[] = ['order_status', '=', -1];
                break;
            case 1:
            default:
                $where[] = ['order_status', 'in', [-3, -2, 1, 2, 3, 4, 6]];
                $where[] = ['payment_status', 'in', [0, 1, 3]];
                break;
        }

        $platformList = Platform::get_all(['data_state' => 0], ['field' => 'id,platform_name'])->toArray();
        $platformList = array_column($platformList, 'platform_name', 'id');
        $orderStatusList = array_columns(MOrder::getOrderStatusList(), 'id', ['name', 'color']);
        $cancelStatusList = array_column(MOrder::getCancelStatusList(), 'name', 'id');
        $paymentStatusList = array_columns(MOrder::getPaymentStatusList(), 'id', ['name', 'color']);
        $list = MOrder::get_all($where, $options)->each(function (&$item) use (
            $platformList, $orderStatusList, $cancelStatusList, $paymentStatusList
        ) {
            $item['platform'] = $platformList[$item['platform']];
            $item['rule'] = "满" . (float)$item['meal_price'] . "返" . (float)$item['cash_back_amount'];
            $item['cancel_reason'] = null;
            $item['deadline'] = null;
            $item['status'] = $orderStatusList[$item['order_status']];
            $item['status_code'] = $item['order_status'];
            $item['order_status'] === -2 || $item['order_rejection_reasons'] = null;
            $item['praise_demand_info'] = invoke(['app\client\service\Order', 'getPraiseDemandInfo'], [$item['is_praise'], $item['praise_demand']]);
            $item['praise_demand_color'] = invoke(['app\client\service\Order', 'getPraiseDemandColor'], [$item['is_praise']]);
            $item['praise_demand_tags'] = invoke(['app\client\service\Order', 'getPraiseDemandTags'], [$item['is_praise']]);
            $item['is_praise'] = getCurrentIsPraise($item['is_praise']);
            switch ($item['order_status']) {
                case 1:
                    $item['deadline'] = date('Y-m-d H:i:s', strtotime($item['create_time']) + 3600);
                    break;
                case -1:
                    $item['cancel_reason'] = $cancelStatusList[$item['cancel_status']] ?? '';
                    break;
                case 2:
                    if (date('H', strtotime($item['create_time'])) >= 21) {
                        $item['deadline'] = date('Y-m-d 03:00:00', strtotime($item['create_time']) + 86400);
                    } else {
                        $item['deadline'] = date('Y-m-d 23:59:59', strtotime($item['create_time']));
                    }
                    if ($item['is_expired'] === 1) {
                        $item['deadline'] = date('Y-m-d 13:00:00', strtotime($item['create_time']) + 86400);
                    }
                    break;
                case 3:
                    $status = $item->getAttr('status');
                    if ($item['machine_audit_status'] === 0) {
                        $status['name'] = '审核中';
                        $item->setAttr('status', $status);
                        break;
                    }
                    $status['name'] = '人工复审中';
                    $item->setAttr('status', $status);
                    break;
                case 4:
                    $item['status'] = $paymentStatusList[$item['payment_status']];
                    break;
            }
            unset($item['meal_price'], $item['is_expired'], $item['cash_back_amount'], $item['order_status'],
                $item['cancel_status']);
        });
        $count = MOrder::get_count($where, ['field' => 'id']);
        $totalPages = ceil(bcdiv($count, $options['limit'], 1));
        $data = compact('list', 'count', 'totalPages');
        return json(HBKReturn('success', $data, 200));
    }

    public function get_order_by_id(): \think\response\Json
    {
        $id = $this->request->get('id');
        $position = $this->request->get(['lng' => 0, 'lat' => 0], 0, 'floatval');
        $user = $this->request->user;
        if (empty($id)) {
            throw new Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }
        $order = invoke([SOrder::class, 'getOrderById'], [$user, $id, $position['lat'], $position['lng']]);
        $data = compact('order');
        return json(HBKReturn('success', $data, 200));
    }

    public function submit(): \think\response\Json
    {
        $id = $this->request->post('id');
        $params = $this->request->post(['take_out_order_no', 'task_out_order_images', 'task_out_evaluate_images']);
        if (!empty($params['task_out_order_images'])) {
            $params['task_out_order_images'] = str_replace(config('Params.cos_host'),
                config('Params.cdn_image_host'), $params['task_out_order_images']);
        }
        if (!empty($params['task_out_evaluate_images'])) {
            $params['task_out_evaluate_images'] = str_replace(config('Params.cos_host'),
                config('Params.cdn_image_host'), $params['task_out_evaluate_images']);
        }
        $position = $this->request->post(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $promotion = $this->request->promotion;
        $mark = $promotion['mark'];
        $platform = 'promotionApi';
        $order = $this->validateMethod($id);
        Db::startTrans();
        try {
            $orderStatusBeforeChange = $order['order_status'];
            // 验证 + 提交 流程
            $submitOrderFactory = SubmitOrderFactory::createSubmitOrderStatusObject($order);
            $submitOrderFactory->validate($params)->submitOrder($params);
            // 添加提交记录
            invoke(['app\client\service\Order', 'insertOrderSubmitRecordByOrder'],
                [$order, $mark, $platform, $position, [], $orderStatusBeforeChange]);
            Db::commit();
        } catch (ValidateException $e) {
            throw $e;
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('clientPromotionOrderSubmit', $e, '提交异常', $id, compact('params', 'mark', 'position'));
            throw new Exception('提交失败，请联系管理员。');
        }

        try {
            switch ($order['order_status']) {
                case 3:
                    // 机审
                    try {
                        $isMachineAuditPlatform = Platform::get_value('machine_audit', ['id' => $order['platform_id']]);
                        if ($isMachineAuditPlatform === 1) {
                            invoke(['app\client\service\Order', 'recognitionImages'], [$order['id']]);
                        }
                    } catch (\Exception $e) {
                        $log = "添加机审队列异常。\r\nmsg：" . $e->getMessage();
                        throw $e;
                    }
                    break;
            }
        } catch (\Exception $error) {
            Log::error('clientPromotionOrderSubmit', $error, "scene：订单扣款确认" . $log, $order['id'], ['id' => $order['platform_id']]);
        }

        return json(HBKReturn('success', [], 200));
    }

    public function cancel(): \think\response\Json
    {
        $id = $this->request->post('id', 0, 'intval');
        $order = $this->validateMethod($id);

        if (in_array($order['order_status'], [-1, 4, 6])) {
            throw new ValidateException('订单状态不正确');
        }

        $data = [$id, '', '', 0, 'promotionClientCancelMtOrder'];
        HttpRpc::callHyperfService('Order', 'cancel', $data);
        return json(HBKReturn('success', [], 200));
    }

    private function validateMethod($id)
    {
        if (empty($id)) {
            throw new Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }
        $where = [
            ['data_state', '=', 0],
            ['user_id', '=', $this->request->userId],
            ['id', '=', $id]
        ];
        $order = MOrder::where($where)->lock(true)->find();
        if (empty($order)) {
            throw new Exception('不存在的订单');
        }
        return $order;
    }
}
