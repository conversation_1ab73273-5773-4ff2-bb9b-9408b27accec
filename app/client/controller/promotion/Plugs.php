<?php

namespace app\client\controller\promotion;

use app\client\validate\Upload;

class Plugs extends Base
{
    public function upload(): \think\response\Json
    {
        $files = $this->request->file('file');
        $param = $this->request->post('param', 'admin');

        $this->validate(['file' => $files], Upload::class);

        $file = fopen($files->getPathname(), 'rb');
        $key = $param . '/image_' . date('YmdHis') . substr(md5(time()), 5, 10) . '.' . $files->getOriginalExtension();
        $result = invoke(['app\common\service\Cos', 'upload'], [$file, $key]);
        $location = str_replace('xxyx-1310097496.cos.ap-shanghai.myqcloud.com', 'image.xiaoxiaoyouxuan.com', $result['Location']);
        return json(HBKReturn('success', ['path' => 'https://' . $location], 200));
    }
}