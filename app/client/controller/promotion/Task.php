<?php

namespace app\client\controller\promotion;

use app\client\service\promotion\User as SUser;
use app\common\exception\ExceptionCode;
use app\common\exception\TokenException;
use app\common\facade\Redis;
use think\Exception;
use think\facade\Db;
use think\facade\Event;
use think\response\Json;
use app\client\service\Task as STask;

class Task extends Base
{
    protected $middleware = [
        'CheckPromotionToken'
    ];
    /**
     * 活动分类列表
     * @return Json
     */
    public function get_task_cate_list(): Json
    {
        $indexTaskCateList = \app\client\model\Task::getIndexTaskCateList();
        return json(HBKReturn('success', ['taskCateList' => $indexTaskCateList], 200));
    }

    public function get_task_list(): Json
    {
        $userId = $this->request->user_id;
        $params = $this->request->get(['keyword' => null, 'tag' => null, 'rush' => false, 'lat' => 0, 'lng' => 0]);
        $options = $this->request->get(['page' => 0, 'limit' => 10]);
        [$list, $count, $totalPages] = invoke([STask::class, 'getIndexTasKList'], [$params['keyword'], $options['page'],
            $options['limit'], $params['tag'], $params['rush'], $params['lat'], $params['lng'], $userId]);
        $data = compact('list', 'count', 'totalPages');
        /** @var \ApiAes $apiAes */
        $key = \CodeGen::gen('2048', \CodeGen::TYPE_MIX_LOWER);
        $iv = substr($key, 32, 16);
        $apiAes = app(\ApiAes::class);
        $ciphertext = json_encode($data, 256);
        $ciphertext = $apiAes->encrypt($ciphertext, $key, $iv);
        $data['a'] = $ciphertext;
        $data['b'] = $key;
        return json(HBKReturn('success', $data, 200));
    }

    public function get_task(): Json
    {
        $id = $this->request->get('id');
        $position = $this->request->get(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $userId = $this->request->user_id;
        if (empty($id)) {
            throw new \Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }
        $task = invoke([STask::class, 'getTaskDetail'], [$userId, $id, $position['lat'], $position['lng']]);
        $data = ['task' => $task];
        /** @var \ApiAes $apiAes */
        $key = \CodeGen::gen('2048', \CodeGen::TYPE_MIX_LOWER);
        $iv = substr($key, 32, 16);
        $apiAes = app(\ApiAes::class);
        $ciphertext = json_encode($data, 256);
        $ciphertext = $apiAes->encrypt($ciphertext, $key, $iv);
        $data['a'] = $ciphertext;
        $data['b'] = $key;
        return json(HBKReturn('success', $data, 200));
    }

    public function sign_up(): Json
    {
        // 获取参数
        $task_id = $this->request->post('task_id');
        $position = $this->request->post(['lat' => 0, 'lng' => 0], 0, 'floatval');
        $promotion = $this->request->promotion;
        $mark = $promotion['mark'];
        $platform = 'promotionApi';
        $user = $this->request->user;

        if ($user->status == 1) {
            throw new Exception('您无法报名，请联系管理员。');
        }
        if (empty($user['user_mobile'])) {
            throw new TokenException('请先登录！');
        }
        if (empty($task_id)) {
            throw new Exception(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }

        $key = Redis::getFullKeyName($task_id . ":" . $user->id, Redis::$order_creating_user_id);
        $is_creating = Redis::set($key, 1, ['NX', 'EX' => 5]);
        if (empty($is_creating)) {
            throw new Exception('你已经在报名中，请稍后查看结果或稍后再试。');
        }
        Db::startTrans();
        try {
            $order = invoke(['app\client\service\Task', 'SignUp'], [$user, $task_id, $platform]);
            invoke(['app\client\service\Task', 'createOrderRecord'], [$order, $platform, $position, $mark]);
            invoke(['app\client\service\Task', 'createPromotionApiOrder'], [$order, $promotion]);
            Event::trigger('CreateOrder', $order);
            Redis::del($key);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Redis::del($key);
            throw new Exception($e->getMessage());
        }
        return json(HBKReturn('success', [], 200));
    }
}