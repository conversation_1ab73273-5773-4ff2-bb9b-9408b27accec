<?php
/**
 * User: zq
 * For: xx
 * Date: 2024/10/23
 * Time: 14:47
 */

namespace app\client\controller;

use app\client\service\Identity as SIdentity;
use app\client\validate\User as VUser;

class Identity extends Base
{
    protected $middleware = [
        'checkToken'
    ];

    /**
     * 身份验证详情
     * @return \think\response\Json
     */
    public function detail()
    {
        $user = $this->request->user;
        $data = invoke([SIdentity::class, 'getDetail'], [$user]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 验证是否需要支付宝三要素授权
     * @return \think\response\Json
     */
    public function check()
    {
        $params = $this->request->post(['type', 'alipay_name', 'alipay_account', 'identity_card_no']);
        $user = $this->request->user;
        // 验证
        $this->validate($params, [
            'type' => ['require', 'in' => '0,1,2'],
            'alipay_name' => ['require'],
            'alipay_account' => ['require'],
        ]);
        $data = invoke([SIdentity::class, 'checkAlipayOauth'], [$params, $user]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 提交信息
     * @return \think\response\Json
     */
    public function config()
    {
        $params = $this->request->post(['code', 'type', 'alipay_name', 'alipay_account', 'identity_card_no']);
        $user = $this->request->user;
        // 验证
        validate(VUser::class)->scene('identity_config')->check([
            'type' => $params['type'],
            'alipay_account' => $params['alipay_account'],
            'alipay_name' => $params['alipay_name']
        ]);
        $data = invoke([SIdentity::class, 'submitAlipayOauth'], [$params, $user]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 用户端清空实名信息
     * @return \think\response\Json
     */
    public function clear()
    {
        $user = $this->request->user;
        $data = invoke([SIdentity::class, 'clearIdentityInfo'], [$user]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 判断用户是否在云账户实名
     * @return \think\response\Json
     */
    public function identity_check_by_yun()
    {
        $user = $this->request->user;
        $data = invoke([SIdentity::class, 'identityCheckByYun'], [$user]);
        return json(HBKReturn('success', $data, 200));
    }
}