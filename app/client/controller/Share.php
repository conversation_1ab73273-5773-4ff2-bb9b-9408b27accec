<?php
declare (strict_types=1);

namespace app\client\controller;


use think\response\Json;
use app\client\service\Share as SShare;


class Share extends Base
{
    /**
     * 店铺活动分享
     * @return Json
     */
    public function share_store_task(): Json
    {
        $userId = $this->getUserByToken();
        $taskId = $this->request->post('task_id', '');
        $platform = $this->request->post('platform_abbr', '');
        $data = invoke([SShare::class, 'shareStoreTask'], [$userId, $taskId, $platform]);
        return json(HBKReturn('success', $data, 200));
    }

    /**
     * 店铺活动分享规则
     * @return Json
     */
    public function share_store_rule(): Json
    {
        $data = invoke([SShare::class, 'shareStoreRule']);
        return json(HBKReturn('success', $data, 200));
    }
}
