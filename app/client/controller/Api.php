<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/8/30 16:50
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\client\controller;

use app\client\model\Platform;
use app\client\model\SettlementOrder;
use app\common\exception\ExceptionCode;
use app\common\facade\Redis;
use think\exception\ValidateException;
use app\client\model\Task;
use think\response\Json;
use app\client\model\Order;
use app\client\model\Store;

class Api extends Base
{
    public function task_settlement_pay_details(): Json
    {
        // 请求参数
        $id = $this->request->get('id');
        $order_code = $this->request->get('order_code');
        // 验证
        if (empty($id) || empty($order_code)) {
            throw new ValidateException(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }
        // 验证
        $key = Redis::getFullKeyName($id, Redis::$task_settlement_page_code);
        $orderCode = Redis::get($key);
        if ($order_code !== $orderCode) {
            throw new \Exception('not find page', 404);
        }
        // 获取settlement和task
        $settlement = SettlementOrder::get_one([
            'id' => $id
        ], [
            'field' => 'id,order_code,task_id,order_amount,order_status'
        ], false);
        if (empty($settlement)) {
            throw new \Exception('not find page', 404);
        }
        $list = array_column(SettlementOrder::getStatusList(), 'name', 'id');
        $settlement['status'] = $list[$settlement['order_status']];
        $task = Task::get_one([
            'id' => $settlement['task_id']
        ], [
            'field' => 'id,store_name,task_cover cover,task_start_time,task_type'
        ]);
        if (empty($task)) {
            throw new \Exception('not find page', 404);
        }

        $jssdkData = false;

        // 页面信息

        $platformList = Platform::get_all(['data_state' => 0], ['field' => 'id,platform_name name'])->toArray();
        $list = array_column($platformList, 'name', 'id');
        $task['type'] = $list[$task['type']] ?? "未知";
        $task['time'] = date('Y-m-d', strtotime($task['task_start_time']));
        $where = [
            'task_id' => $task['id'],
            'data_state' => 0,
            'satisfied' => 1,
            ['order_status', 'in', [-3, -2, 3, 4, 6]]
        ];
        // 结算订单
        $orders = Order::get_all($where, [
            'field' => 'id,take_out_order_no,user_nick,create_time,take_out_paid_amount,order_phone,order_phone_tail,task_out_order_images,task_out_evaluate_images'
        ])->each(function ($item) {
            $item['task_out_order_images'] = $item['task_out_order_images'] ? explode(',', $item['task_out_order_images']) : [];
            $item['task_out_evaluate_images'] = $item['task_out_evaluate_images'] ? explode(',', $item['task_out_evaluate_images']) : [];
            $item['images'] = array_merge($item['task_out_order_images'], $item['task_out_evaluate_images']);
            unset($item['task_out_order_images'], $item['task_out_evaluate_images']);
        });
        $count = Order::get_count($where);
        $data = compact('settlement', 'task', 'orders', 'count', 'jssdkData');
        return json(HBKReturn('success', $data, 200));
    }

    public function store_settlement_pay_details(): \think\response\Json
    {
        // 请求参数
        $id = $this->request->get('id');
        $order_code = $this->request->get('order_code');
        // 验证
        if (empty($order_code)) {
            throw new ValidateException(ExceptionCode::MISSING_REQUIRED_PARAMETERS);
        }
        $key = Redis::getFullKeyName($id, Redis::$store_settlement_page_code);
        $storeData = Redis::get($key);
        if (empty($storeData)) {
            throw new \Exception('not find page', 404);
        }
        $storeData = json_decode($storeData, true);
        if ($order_code !== $storeData['code']) {
            throw new \Exception('not find page', 404);
        }

        $platformList = Platform::get_all(['data_state' => 0], ['field' => 'id,platform_name'])->toArray();
        $list = array_column($platformList, 'platform_name', 'id');
        $store = Store::get_one([
            'id' => $storeData['store_id']
        ], [
            'field' => 'id,store_name,store_cover,store_type'
        ]);
        $store['type'] = $list[$store['store_type']] ?? "未知";
        if (empty($storeData['order_ids'])) {
            $data = [
                'store' => $store,
                'orders' => [],
                'count' => 0
            ];
            $data['date'] = implode(' - ', $storeData['date']);
            return json(HBKReturn('success', $data, 200));
        }
        $where = [['id', 'in', $storeData['order_ids']]];
        // 结算订单
        $orders = Order::get_all($where, [
            'field' => 'id,take_out_order_no,user_nick,create_time,take_out_paid_amount,order_phone_tail,task_out_order_images,task_out_evaluate_images'
        ])->each(function ($item) {
            $item['task_out_order_images'] = $item['task_out_order_images'] ? explode(',', $item['task_out_order_images']) : [];
            $item['task_out_evaluate_images'] = $item['task_out_evaluate_images'] ? explode(',', $item['task_out_evaluate_images']) : [];
            $item['images'] = array_merge($item['task_out_order_images'], $item['task_out_evaluate_images']);
            unset($item['task_out_order_images'], $item['task_out_evaluate_images']);
        });
        $count = Order::get_count($where);
        $data = compact('store', 'orders', 'count');
        $data['date'] = implode(' - ', $storeData['date']);
        return json(HBKReturn('success', $data, 200));
    }
}