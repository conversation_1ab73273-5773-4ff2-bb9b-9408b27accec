<?php


namespace app\client\service\mt;


use app\client\model\Agent as AgentModel;
use app\client\model\Config as ConfigModel;
use app\client\model\Order as OrderModel;
use app\client\model\OrderInfo as OrderInfoModel;
use app\client\model\OrderMtGp as OrderMtGpModel;
use app\client\model\OrderSubmitRecord as OrderSubmitRecordModel;
use app\client\model\OrderTime as OrderTimeModel;
use app\client\model\Platform as PlatformModel;
use app\client\service\User as UserService;
use app\common\facade\Redis;
use app\common\implement\rpc\Task;
use app\common\service\Position as PositionService;
use app\Log;
use Rabbit;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;

class MTGPSignUp extends Base
{
    /**
     * 报名
     * @throws \Exception
     */
    public function signUp()
    {
        $productIndexId = $this->getProductIndexId();
        $key = Redis::getFullKeyName($productIndexId . ':' . $this->mtPhone, Redis::$order_creating_user_id);
        $isCreating = Redis::set($key, 1, ['NX', 'EX' => 5]);

        if (empty($isCreating)) {
            throw new \Exception('你已经在报名中，请稍后查看结果或稍后再试。');
        }

        try {
            if ($this->checkUserAlreadySignUp($this->getMtPhone(), rawurldecode($this->productIndexId))) {
                throw new \Exception('用户或当前手机号今日已经参加过活动了');
            }

            // 不是超级白名单用户，需要检查下单次数
            if ($this->user['whitelist'] !== 2) {
                $this->checkCancelNum();
            }

            // 从redis中获取团购活动数据
            $infoKey = Redis::getFullKeyName($productIndexId, self::MT_SELLER_KEY);
            $sellInfo = Redis::get($infoKey);

            if (empty($sellInfo)) {
                throw new \Exception('没有此活动！');
            }

            $sellInfo = json_decode($sellInfo, true);
            $poiInfo = $this->getPoiInfo($sellInfo);
            $sellInfo['agentId'] = $poiInfo['agentId'];
            $sellInfo['nearestPoiInfo'] = $poiInfo;

            try {
                // 尝试获取门店信息
                if (!empty($this->getHashKey())) {
                    $uniqueKey = $this->getHashKey();
                    $hNearestPoiInfoKey = Redis::getFullKeyName($productIndexId, self::MT_SELLER_NEAREST_POI_INFO_KEY);
                    $nearestPoiInfo = Redis::hGet($hNearestPoiInfoKey, $uniqueKey);
                    if (!empty($nearestPoiInfo)) {
                        $sellInfo['nearestPoiInfo'] = json_decode($nearestPoiInfo, true);
                        $sellInfo['agentId'] = $sellInfo['nearestPoiInfo']['agentId'];
                    }
                }
            } catch (\Exception $e) {
            }

            $result = $this->requestMtGpSignUp($this->mtPhone, $sellInfo);

            if (empty($result)) {
                throw new \Exception('报名失败');
            }
        } catch (\Exception $e) {
            Redis::del($key);
            throw $e;
        }

        Db::startTrans();
        try {
            $orderInfo = $this->createOrder($sellInfo);
            $order = $orderInfo['order'];
            $this->createMtGpOrder($order, $sellInfo, $result);
            $this->createOrderInfo($order);
            $this->createOrderRecord($order, $this->getPosition(), $this->getPlatform());
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Redis::del($key);
            throw $e;
        }

        try {
            if (!empty($order)) {
                $config = [
                    'exchange_name' => Rabbit::EXT_ORDER,
                    'exchange_type' => Rabbit::DIRECT,#直连模式
                    'queue_name' => Rabbit::QUEUE_TASK_REGISTRATION,
                    'route_key' => Rabbit::ROUTE_TASK_REGISTRATION
                ];
                $data = json_encode([
                    'order_id' => (int)$order->id,
                ], 256);
                Rabbit::pushMessage($data, $config);
            }
        } catch (\Exception $e) {
            Log::error('pushRabbitmqFailed', $e, '订单id放入Rabbitmq失败', 'pushRabbitmqFailed', ['order_id' => $order->id ?? 0]);
        }

        // 更新用户和代理城市的关系
        invoke([UserService::class, 'saveUserAgent'], [$this->user['id'], $sellInfo['agentId']]);
        // 异步通知java报名
        $this->asyncNotifySignUp($order, $this->getPlatform(), $this->getPosition());
        // 异步更新报名的状态
        $this->asyncUpdateSignUpStatus($order, $result);

        return $order;
    }

    private function asyncNotifySignUp($order, $platform, $position)
    {
        /** @var Task $pay */
        $pay = app(Task::class);
        try {
            $pay->afterSignUp(
                $order['id'],
                $order['order_status'],
                $platform,
                $position['lng'],
                $position['lat'],
                '',
                getCurrentVersion());
        } catch (\Exception|\Error $e) {
            Log::error('AfterMtSignUpFailed', $e, '美团官方活动报名后置操作java接口报错', $order['id'], [
                'orderStatus' => $order['order_status'],
                'platform' => $platform,
                'lng' => $position['lng'],
                'lat' => $position['lat']
            ]);
        }
    }

    /**
     * 异步更新报名的状态
     * @param $order
     * @param array $result
     */
    private function asyncUpdateSignUpStatus($order, array $result)
    {
        try {
            /** @var Task $task */
            $task = app(Task::class);
            if (!empty($result['userEventId'])) {
                $task->afterMtSignUp($order->id, $result['userEventId'], $this->mtPhone);
            }
        } catch (\Exception $e) {
            Log::error('asyncUpdateSignUpStatusFailed', $e, '异步更新报名的状态后置操作java接口报错', $order->id, ['order_id' => $order->id ?? 0]);
        }
    }

    /**
     * @param $order
     * @param $sellInfo
     * @param $result
     * @throws DataNotFoundException
     * @throws DbException
     */
    private function createMtGpOrder($order, $sellInfo, $result)
    {
        $agentInfo = AgentModel::where('id', $sellInfo['agentId'])
            ->find();
        $subsidyRatio = $agentInfo['mtg_gp_subsidy_ratio'] ?? 0;

        if (isset($sellInfo['platForm'])) {
            $platform = intval($sellInfo['platForm']) === 0 ? 1 : 2;
        } elseif (isset($sellInfo['platform'])) {
            $platform = intval($sellInfo['platform']) === 0 ? 1 : 2;
        } else {
            $platform = 1; // 默认美团
        }

        $poiEventId = $this->getPoiEventId();
        $poiEventInfo = $this->getPoiEventInfo($sellInfo['planActivityInfoList'], $poiEventId);

        $mtData = [
            'user_id' => $this->user['id'],
            'order_id' => $order['id'],
            'type' => $platform,
            'cash_back_type' => 1,
            'product_index_id' => rawurldecode($this->productIndexId),
            'mt_phone' => $this->getMtPhone(),
            'poi_event_id' => $poiEventId,
            'user_event_id' => $result['userEventId'],
            'subsidy_ratio' => $subsidyRatio,
            'unified_cash_back_headquarter_ratio' => $agentInfo['mtg_gp_unified_cash_back_headquarter_ratio'] ?? 0,
            'unified_cash_back_agent_ratio' => $agentInfo['mtg_gp_unified_cash_back_agent_ratio'] ?? 0,
            'longitude' => $this->position['lng'],
            'latitude' => $this->position['lat'],
            'distance' => (float)bcdiv($sellInfo['nearestPoiInfo']['distance'], 1000, 2),
            'action_url' => json_encode([
                'detail_url' => $sellInfo['productDetailJumpUrl'],
                'buy_url' => $sellInfo['productBuyUrl'],
                'poi_url' => $sellInfo['nearestPoiInfo']['poiJumpUrl'],
            ], true),
            'cancel_time' => $result['rule']['cancelTime'],
            'consume_time' => $result['rule']['consumeTime'],
            'delay_cash_back_time' => $result['rule']['delayCashBackTime'],
            'comment_time' => $result['rule']['commentTime'],
            'comment_text_length' => $result['rule']['commentTextLength'],
            'comment_picture_count' => $result['rule']['commentPictureCount'],
            'order_limit_exact_time' => $result['orderLimitExactTime'],
            'user_comment_ratio' => $poiEventInfo['toUserCommentRatio'],
            'org_user_ratio' => $poiEventInfo['toUserRatio'],
            'org_media_ratio' => $poiEventInfo['toMediaRatio'],
            'user_ratio' => $result['userRatio'],
            'media_ratio' => $result['mediaRatio'],
            'user_max_commission' => $result['userMaxCommission'] ?? -1, // -1表示无上限
            'client_ip' => get_client_ip(),
            'seller_name' => $sellInfo['nearestPoiInfo']['name'],
            'cover' => $sellInfo['nearestPoiInfo']['frontImg'],
            'address' => $sellInfo['nearestPoiInfo']['address'],
            'meal_name' => $sellInfo['title'],
            'meal_cover' => $sellInfo['headImage'],
            'pay_price' => $sellInfo['payPrice'],
            'market_price' => $sellInfo['marketPrice'],
            'category_name' => $sellInfo['nearestPoiInfo']['categoryName'],
            'mt_score' => $sellInfo['nearestPoiInfo']['mtScore'],
            'code' => 1
        ];

        if (isset($result['mediaMaxCommission'])) {
            $mtData['media_max_commission'] = intval($result['mediaMaxCommission']);
        }

        OrderMtGpModel::create($mtData);
    }

    /**
     * @param $order
     */
    private function createOrderInfo($order)
    {
        $orderInfo = [
            'id' => $order['id'],
            'order_no' => $order['order_no'],
            'cancel_num' => 0,
            'red_envelope_id' => 0,
            'red_envelope_amount' => 0,
            'current_debit' => 0,
            'sign_up_client' => $this->getPlatform(),
            'store_enter_qr' => ''
        ];

        OrderInfoModel::create($orderInfo);
    }

    /**
     * 增加订单记录
     * @param $order
     * @param $position
     * @param $platform
     */
    private function createOrderRecord($order, $position, $platform)
    {
        $latitude = empty($position['lat']) ? '' : $position['lat'];
        $longitude = empty($position['lng']) ? '' : $position['lng'];
        $address = invoke([PositionService::class, 'getGeoResultByDb'],
            [$latitude, $longitude, 'getAddress', '获取地址失败']);

        OrderSubmitRecordModel::create([
            'order_id' => $order->id,
            'status' => $order['order_status'],
            'order_no' => $order['order_no'],
            'platform' => $platform,
            'longitude' => $longitude,
            'latitude' => $latitude,
            'address' => $address,
            'mark' => ''
        ]);
    }

    /**
     * @param $sellInfo
     * @return array
     * @throws \Exception
     */
    private function createOrder($sellInfo): array
    {
        $platform = PlatformModel::where('platform_abbreviation', PlatformModel::PLT_MTG_GP)
            ->field(true)
            ->find();

        if (!empty($platform)) {
            $platform = $platform->toArray();
        } else {
            throw new \Exception('平台信息不存在');
        }

        $data = [
            'user_id' => $this->user['id'],
            'user_nick' => $this->user['user_nick'],
            'promoter_user_id' => $this->user['user_promoter_id'],
            'order_phone' => $this->user['user_mobile'],
            'order_phone_tail' => substr($this->user['user_mobile'], -4),
            'store_id' => 0,
            'task_id' => 0,
            'store_name' => $sellInfo['nearestPoiInfo']['name'],
            'task_cover' => $sellInfo['nearestPoiInfo']['frontImg'],
            'agent_id' => $sellInfo['agentId'],
            'platform_id' => $platform['id'],
            'is_praise' => 1, // 团购订单，暂定都是需要反馈
            'praise_demand' => $sellInfo['rule']['commentPictureCount'] . ',' . $sellInfo['rule']['commentTextLength'],
            'order_status' => 1,
            'create_date' => date('Y-m-d')
        ];

        $order = OrderModel::create($data);
        $order->order_no = createOrderNo($order['id'], $order['create_time'], $order['user_id']);
        $order->save();

        // 获取提交订单的时间倒计时
        $isOrderLimit = 0;
        $submitLiveTime = $sellInfo['rule']['cancelTime'];

        OrderTimeModel::create([
            'order_id' => $order['id'],
            'submit_live_time' => $submitLiveTime,
            'is_order_limit' => $isOrderLimit,
        ]);

        return [
            'order' => $order,
            'data' => $data
        ];
    }

    /**
     * 检查用户进入是否已经参加活动
     * @param string $mtPhone
     * @param string $productIndexId
     * @return bool
     */
    private function checkUserAlreadySignUp(string $mtPhone, string $productIndexId): bool
    {
        $count = OrderMtGpModel::alias('omg')
            ->where('omg.mt_phone', $mtPhone)
            ->where('omg.product_index_id', $productIndexId)
            ->where('omg.data_state', 0)
            ->where('o.order_status', '<>', -1)
            ->whereDay('o.create_time')
            ->join('bwc_order o', 'omg.order_id=o.id')
            ->count('omg.id');
        return $count > 0;
    }

    /**
     * @param $mtPhone
     * @param $sellInfo
     * @return array
     * @throws \RedisException
     * @throws \Exception
     */
    private function requestMtGpSignUp($mtPhone, $sellInfo): array
    {
        $position = $this->getPosition();
        $post = [
            'mediaUserId' => $this->encodeUserPhone($mtPhone),
            'poiEventId' => $this->getPoiEventId(),
            'p' => $this->getMtGpP(),
            'meituanPvId' => $sellInfo['mtPvId'],
            'actualLongitude' => $position['lng'],
            'actualLatitude' => $position['lat'],
            'clientIp' => get_client_ip(),
            'cashBackMode' => 1,
        ];

        $subsidyRatio = (int)AgentModel::where('id', $sellInfo['agentId'])
            ->value('mtg_gp_subsidy_ratio');

        if ($subsidyRatio > 0) {
            $post['subsidyRatio'] = $subsidyRatio;
        }

        // todo::记得修改
//        $env = 'test';
//        if ($env === 'test') {
//            $this->mtCreateOrder = 'https://ldw-admin-api-test.xiaoxiaoyouxuan.com/mock.mtg.sign_up/index';
//            $post['seller_info'] = $sellInfo;
//        }

        $this->mtCreateOrder = $this->config['mtg_gp_create_order'] ?? '';
        $result = $this->request($this->mtCreateOrder, json_encode($post));

        try {
            // 这里记录报名日志，包含请求和响应数据
            Log::info('mt_gp_sign_up_res', '美团接口报名数据', '请求美团到店接口报名', $mtPhone, [
                'res' => $result,
                'param' => $post,
                'url' => $this->mtCreateOrder
            ]);

            if (!isset($result['code'])) {
                throw new \Exception('报名不成功，请稍后重试');
            }

            $code = intval($result['code']);

            if ($code === 0) {
                return $result;
            }

            $msg = $result['msg'] ?? $code . '错误';

            if ($msg === '已领取商家任务，请前往对应领取平台完成任务') {
                $msg = '您已参与过该店铺活动，请于对应活动平台查看';
            }

            throw new \Exception($msg);
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 活动信息
     * @param $actList
     * @param $poiEventId
     * @return mixed
     * @throws \Exception
     */
    protected function getPoiEventInfo($actList, $poiEventId)
    {
        foreach ($actList as $item) {
            if ($item['poiEventId'] === $poiEventId) {
                return $item;
            }
        }

        throw new \Exception('活动不存在');
    }

    /**
     * @param $sellInfo
     * @return array
     */
    protected function getPoiInfo($sellInfo)
    {
        $nearestPoiInfo = $this->getNearestPoiInfo();

        if (!empty($nearestPoiInfo)) {
            return [
                'name' => $nearestPoiInfo['name'], // ⻔店名称，包含分店,
                'address' => $nearestPoiInfo['address'],
                'frontImg' => $nearestPoiInfo['front_img'],
                'avgPrice' => $nearestPoiInfo['avg_price'], // ⻔店人均消费价格，单位元
                'mtScore' => (int)bcmul($nearestPoiInfo['mt_score'], '10'), // 美团评分
                'bareaName' => $nearestPoiInfo['barea_name'] ?? '', // 所属商圈名称
                'categoryName' => $nearestPoiInfo['category_name'],
                'distance' => (int)bcmul($nearestPoiInfo['distance'], '1000'),
                'poiJumpUrl' => $nearestPoiInfo['poi_jump_url'], // ⻔店详情页链接
                'agentId' => $nearestPoiInfo['agent_id'], // 代理商id
            ];
        }

        $poiInfo = $sellInfo['nearestPoiInfo'];

        return array_merge($poiInfo, [
            'agentId' => $sellInfo['agentId'], // 代理商id
        ]);
    }

    /**
     * 加密手机号
     * @param $userPhone
     * @return string
     * @throws \Exception
     */
    protected function encodeUserPhone($userPhone): string
    {
        return \MTAes::encrypt($userPhone, $this->getMtGpP());
    }

    /**
     * p参数
     * @return string
     * @throws \Exception
     */
    protected function getMtGpP()
    {
        if (isset($this->config['mtg_gp_p'])) {
            return $this->config['mtg_gp_p'];
        }

        throw new \Exception('p参数未配置，请联系管理员');
    }
}
