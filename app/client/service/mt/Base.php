<?php

namespace app\client\service\mt;


use app\client\exception\ShareStoreTaskException;
use app\client\model\Agent;
use app\client\model\Agent as MAgent;
use app\client\model\Config as MConfig;
use app\client\model\MtConfig as MtConfig;
use app\client\model\Order;
use app\client\model\RedEnvelopes;
use app\client\model\Task;
use app\client\model\UserMtPhone;
use app\client\service\BaseService;
use app\common\facade\Redis;
use Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;


abstract class Base extends BaseService
{
    /**
     * 美团配置
     * @var array
     */
    protected array $config = [];

    /**
     * 推广链接 id，赏金联盟媒体平台取链生成的 p 参数
     * @var string
     */
    protected $p;

    /**
     * 美团商家列表接口
     * @var string
     */
    protected $mtSellerUrl;

    /**
     * 商家创建订单
     * @var string
     */
    protected $mtCreateOrder;

    /**
     * 平台设置的佣金
     * @var float
     */
    protected $lowestCommission;

    /**
     * @var
     */
    protected $feeAmount;

    /**
     * 商家id
     * @var string
     */
    protected $sellerId;

    /**
     * @var string
     */
    protected $mtPvId;

    /**
     * @var
     */
    protected $position;

    /**
     * @var array
     */
    protected $param;

    /**
     * @var
     */
    protected $poiEventId;

    /**
     * 当前登录用户信息
     * @var
     */
    protected $user;

    /**
     * 用户平台， ios，android...
     * @var string
     */
    protected string $platform;

    /**
     * 美团手机号
     * @var string
     */
    protected string $mtPhone;

    /**
     * 用户id
     * @var int
     */
    protected int $userId = 0;

    /**
     * 团单标识id
     * @var string
     */
    protected string $productIndexId = '';

    /**
     * 团单hashKey
     * @var string
     */
    protected string $hashKey = '';

    /**
     * 最近的可⽤⻔店信息
     * @var array
     */
    protected array $nearestPoiInfo = [];

    /**
     * 代理id
     * @var int
     */
    protected int $agentId = 0;

    /**
     * 美团商家redis key前缀
     * @const
     */
    const MT_SELLER_LIST_KEY = 'mt_seller_list';

    /**
     * 美团商家redis key前缀
     * @const
     */
    const MT_SELLER_KEY = 'mt_seller_info';

    /**
     * 美团商家距离
     * @const
     */
    const MT_SELLER_DISTANCE_KEY = 'mt_seller_distance';

    /**
     * 美团官方团购最近的门店key
     * @const
     */
    const MT_SELLER_NEAREST_POI_INFO_KEY = 'mt_seller_nearest_poi_info';

    /**
     * 美团商家活动
     * @const
     */
    const MT_SELLER_ACT_KEY = 'mt_seller_act';

    /**
     * Base constructor.
     */
    public function __construct()
    {
        $this->init();
    }

    /**
     * 初始化配置参数
     */
    protected function init()
    {
        $config = MtConfig::get_column(['value', 'key'], ['data_state' => 0]);

        $this->config = $config;
        $this->p = $config['p'];
        $this->mtSellerUrl = $config['seller_list_url'];
        $this->mtCreateOrder = $config['mt_create_order'];
        $this->lowestCommission = MConfig::getValueByKey('mt_lowest_commission');
    }

    /**
     * 返回数字距离
     * @param $mtPvId
     * @param $sellerId
     * @param mixed $default fallback
     * @return float
     * @throws \RedisException
     */
    protected function getDistance($mtPvId, $sellerId, $default = '')
    {
        // 存储距离hash key
        $hKey = Redis::getFullKeyName($mtPvId, self::MT_SELLER_DISTANCE_KEY);
        // 距离hash
        $distance = Redis::hGet($hKey, $sellerId);
        $distance = $distance === false ? $default : $distance;

        $unit = empty($distance) ? 'km' : substr($distance, -2);
        $distance = (float)$distance;

        if ($unit === 'km') {
            $distance *= 1000;
        }

        return (float)bcdiv($distance, '1000', 2);
    }

    /**
     * 判断是否开启活动
     * @param $lat
     * @param $lng
     * @param $agentId
     * @return int
     */
    public function mtActivityEnabled($lat, $lng, $agentId): int
    {
        // 美团活动总开关
        $mtActEnabled = MConfig::getValueByKey('mt_act_enabled');

        // 活动未启用， 返回false
        if (!$mtActEnabled) {
            return 0;
        }

        $where['mt_activity_enabled'] = 1;
        $where['data_state'] = 0;
        $where['agent_status'] = 1;

        // agentId等于0，尝试从中获取
        if ($agentId === 0) {
            $agentId = $this->getAgentIdFromRedis($lat, $lng);
        }

        if ($agentId > 0) {
            $where['id'] = $agentId;
        } else {
            // 退化到根据用户经纬度解析用户当前所在城市
            $cityCode = invoke([\app\common\service\Position::class, 'getGeoResultByDb'],
                [$lat, $lng, 'getCityAdCode']);

            if (empty($cityCode)) {
                return 0;
            }

            $where['city_code'] = '156' . $cityCode;
        }

        $agentId = MAgent::get_value('id', $where);
        return (int)$agentId;
    }

    /**
     * 从redis中取出50公里范围内，最近的agentId
     * @param $lat
     * @param $lng
     * @return int
     */
    private function getAgentIdFromRedis($lat, $lng): int
    {
        $key = Redis::getFullKeyName(date('Ymd'), Redis::$task_list_date_geo);
        $taskIdList = Redis::georadius_ro($key, $lng, $lat, 50, 'km', ['WITHDIST', 'ASC']);
        $taskIds = array_column($taskIdList, '0');

        if (!empty($taskIds)) {
            return (int)Task::where([
                ['data_state', '=', 0],
                ['status', '=', 1],
                ['id', '=', $taskIds[0]]
            ])
                ->field(['agent_id'])
                ->value('agent_id');
        }

        return 0;
    }

    /**
     * @param $lat
     * @param $lng
     * @return int
     */
    private function getAgentIdFromStore($lat, $lng)
    {
        $agentId = 0;
        $taskInfo = Task::get_one([
            'data_state' => 0,
            'status' => 1,
            'func_array' => [
                ['whereDay', ['task_start_time']]
            ],
        ], [
            'field' => "agent_id,ROUND(6378.138 * 2 * ASIN(SQRT(POW(SIN(({$lat} * PI() / 180 - store_latitude * PI() / 180) / 2 ),2) + 
            COS({$lat} * PI() / 180) * COS(store_latitude * PI() / 180) * 
            POW(SIN(({$lng} * PI() / 180 - store_longitude * PI() / 180) / 2),2))) * 10)/10 AS distance",
            'order' => ['distance' => 'asc'],
            'having' => 'distance <= 50'
        ]);

        if (!empty($taskInfo)) {
            $agentId = intval($taskInfo['agent_id']);
        }

        return $agentId;
    }

    /**
     * 返回设置的基准佣金
     * @return float
     * @throws Exception
     */
    protected function getBaseCommission($averagePrice, $ratio, $maxCommission)
    {
        $maxCommission = (float)bcdiv($maxCommission, '100', 2);
        if (empty($averagePrice)) {
            $averageCommission = 0;
        } else {
            $averageCommission = (float)bcmul($averagePrice, bcdiv($ratio, '10000', 2));
        }
        return min(max($averageCommission, $this->lowestCommission), $maxCommission);
    }

    /**
     * 美团手续费
     * @return float
     */
    protected function feeAmount($agentId)
    {
        if (is_null($this->feeAmount)) {
            $this->feeAmount = Agent::get_value('mt_fee', [
                'id' => $agentId,
                'data_state' => 0,
                'agent_status' => 1,
            ]);
        }

        return max($this->feeAmount, 0);
    }

    /**
     * 从redis获取商家信息
     * @return array
     * @throws Exception
     */
    protected function getSellerInfoFromRedis(): array
    {
        $sellInfo = $this->getSellerInfo();
        if (empty($sellInfo)) {
            $params = $this->getParam();
            if (!empty($params['from'])) {
                throw new ShareStoreTaskException('没有此活动！');
            }
            throw new Exception('没有此活动！');
        }

        return $sellInfo;
    }

    /**
     * 计算返现佣金，扣除手续费，不含晓晓红包
     * @param float $baseCommission
     * @param float $feeAmount
     * @return float|int
     */
    protected function calculateCashBack($baseCommission, $feeAmount)
    {
        return floor((float)bcsub($baseCommission, $feeAmount, 2));
    }

    /**
     * 获取用户可用的最大通用红包
     * @param int|null $userId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    protected function getUserAvailableRedAmount(?int $userId): array
    {
        $result = [
            'red_amount' => 0,
            'id' => 0
        ];

//        $switch = (int)MConfig::getValueByKey('g_bwc_can_use_red_envelope');
        $switch = 0;
        if (!$switch) {
            return $result;
        }

        if (!empty($userId)) {
            $info = RedEnvelopes::get_one([
                'user_id' => $userId,
                'red_type' => 1,
                'red_status' => 1,
                'data_state' => 0,
                'func_array' => [
                    ['where', [function ($query) {
                        $query->where('red_start_time', '<= TIME', date('Y-m-d H:i:s'))
                            ->whereNull('red_start_time', 'or');
                    }]],
                    ['where', [function ($query) {
                        $query->where('red_end_time', '>= TIME', date('Y-m-d H:i:s'))
                            ->whereNull('red_end_time', 'or');
                    }]]
                ]
            ], ['field' => [
                'max(red_amount) max_amount', 'id'
            ]]);

            $result = [
                'red_amount' => $info['max_amount'] ?? 0,
                'id' => $info['id'] ?? 0,
            ];
        }

        return $result;
    }

    /**
     * 排序任务
     * @param $list
     * @return mixed
     */
    protected function sortTask($list)
    {
        usort($list, function ($a, $b) {
            if (($diff = bccomp($b['has_quota'], $a['has_quota'])) !== 0) {
                return $diff;
            } else if (($diff = bccomp($b['max_ratio'], $a['max_ratio'])) !== 0) {
                return $diff;
            } else if (($diff = bccomp($b['is_praise'], $a['is_praise'])) !== 0) {
                return $diff;
            } else if (($diff = bccomp($b['max_commission'], $a['max_commission'])) !== 0) {
                return $diff;
            }
            return 0;
        });
        return $list;
    }

    /**
     * @param $url
     * @param $data
     * @return mixed
     */
    protected function request($url, $data)
    {
        $api = app(\http\Api::class, [
            $url,
            'post',
            $data,
            ['Content-Type:application/json', 'User-Agent: curl']
        ], true);
        $api->request();
        return json_decode($api->getResponseBody(), true);
    }

    /**
     * 用户最近订单美团手机号
     * @param $userId
     * @return string
     */
    public function getTaskMtPhone($userId): string
    {
        return invoke([\app\client\service\UserMtPhone::class, 'getTaskMtPhone'], [
            $userId
        ]);
    }

    public function saveTaskMtPhone($userId, $mtPhone)
    {
        $model = UserMtPhone::get_one([
            'user_id' => $userId,
            'mt_phone' => $mtPhone
        ]);

        if (empty($model)) {
            UserMtPhone::create([
                'user_id' => $userId,
                'mt_phone' => $mtPhone
            ]);
        } else {
            $model->data_state = 0;
            $model->save([
                'update_time' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * 美团引导图片
     * @return mixed
     */
    protected function getGuideTutorialImg()
    {
        $tutorial = MtConfig::getValueByKey('mt_guide_tutorial');
        return json_decode($tutorial, true);
    }

    /**
     * 用户已报名的任务
     * @param $userId
     * @return int
     */
    public function getUserSignUpOrderNum($userId): int
    {
        return Order::get_count([
            'user_id' => $userId,
            'data_state' => 0,
            'order_status' => 1,
            'func_array' => [
                ['whereTime', ['create_time', '>=', date('Y-m-d')]],
            ]
        ]);
    }

    /**
     * @return string
     */
    public function getSellerId(): string
    {
        return rawurlencode(rawurldecode($this->sellerId));
    }

    public function getMtPvId()
    {
        return $this->mtPvId;
    }

    /**
     * @param $sellerId
     * @return static
     */
    public function setSellerId($sellerId)
    {
        $this->sellerId = $sellerId;
        return $this;
    }

    public function setMtPvId($mtPvId)
    {
        $this->mtPvId = $mtPvId;
        return $this;
    }

    /**
     * @param $position
     * @return static
     */
    public function setPosition($position)
    {
        $this->position = $position;
        return $this;
    }

    /**
     * @return array
     */
    public function getPosition()
    {
        return $this->position;
    }

    /**
     * @param $poiEventId
     * @return static
     */
    public function setPoiEventId($poiEventId)
    {
        $this->poiEventId = $poiEventId;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getPoiEventId()
    {
        return $this->poiEventId;
    }

    public function setParam($params)
    {
        $this->param = $params;
        return $this;
    }

    public function getParam()
    {
        return $this->param;
    }

    public function setUserId(int $userId)
    {
        $this->userId = $userId;
        return $this;
    }

    public function getUserId()
    {
        return $this->userId;
    }

    /**
     * @param $mtPhone
     * @return static
     */
    public function setMtPhone($mtPhone)
    {
        $this->mtPhone = $mtPhone;
        return $this;
    }

    /**
     * @return string
     */
    public function getMtPhone()
    {
        return $this->mtPhone;
    }

    /**
     * @param $user
     * @return static
     */
    public function setUser($user)
    {
        $this->user = $user;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getUser()
    {
        return $this->user;
    }

    /**
     * @param $platform
     * @return static
     */
    public function setPlatform($platform)
    {
        $this->platform = $platform;
        return $this;
    }

    /**
     * @return string
     */
    public function getPlatform()
    {
        return $this->platform;
    }

    public function setProductIndexId(string $productIndexId)
    {
        $this->productIndexId = $productIndexId;
        return $this;
    }

    /**
     * 团单标识
     * @return string
     */
    public function getProductIndexId()
    {
        return rawurlencode(rawurldecode($this->productIndexId));
    }

    /**
     * @param string $hashKey
     * @return $this
     */
    public function setHashKey(string $hashKey)
    {
        $this->hashKey = $hashKey;
        return $this;
    }

    /**
     * @return string
     */
    public function getHashKey()
    {
        return $this->hashKey;
    }

    /**
     * @return array
     */
    public function getNearestPoiInfo()
    {
        return $this->nearestPoiInfo;
    }

    /**
     * @param array $nearestPoiInfo
     * @return $this
     */
    public function setNearestPoiInfo(array $nearestPoiInfo = [])
    {
        $this->nearestPoiInfo = $nearestPoiInfo;
        return $this;
    }

    /**
     * @return int
     */
    public function getAgentId()
    {
        return $this->agentId;
    }

    /**
     * @param $agentId
     * @return $this
     */
    public function setAgentId($agentId)
    {
        $this->agentId = $agentId;
        return $this;
    }

    /**
     * 加密手机号
     * @param $userPhone
     * @return string
     */
    protected function encodeUserPhone($userPhone): string
    {
        return \MTAes::encrypt($userPhone);
    }

    /**
     * 获取sellerInfo
     *
     * @return mixed
     */
    public function getSellerInfo()
    {
        $key = Redis::getFullKeyName($this->getSellerId() . ':' . $this->getPoiEventId(), self::MT_SELLER_KEY);
        $sellInfo = Redis::get($key);
        return json_decode($sellInfo, true);
    }

    /**
     * 检查用户取消订单次数
     * @throws Exception
     */
    protected function checkCancelNum()
    {
        $allOrderNum = Order::get_count([
            'data_state' => 0,
            'user_id' => $this->user['id'],
            'func_array' => [
                ['whereTime', ['create_time', '>=', date('Y-m-d')]],
            ]
        ]);

        if ($allOrderNum >= 20) {
            throw new Exception('您今天报名次数过多，请明天再来报名。');
        }
    }

    /**
     * 获取美团官方活动的抽成比例
     * @param $agentRatioConfigList
     * @param $ratio
     * @return int|mixed
     */
    public function getUserRatio($agentRatioConfigList, $ratio)
    {
        [$headquartersCommissionRatio, $agentCommissionRatio] = $this->getCommissionRatio($agentRatioConfigList, $ratio);
        $userRatio = (float)bcsub(bcsub('10000', $headquartersCommissionRatio), $agentCommissionRatio);
        // 不能超过100%
        return min($userRatio, 10000);
    }

    /**
     * 获取美团官方活动的抽成佣金
     * @param $agentRatioConfigList
     * @param $ratio
     * @return array|int[]
     */
    public function getCommissionRatio($agentRatioConfigList, $ratio)
    {
        if (empty($agentRatioConfigList)) {
            return [0, 0];
        }

        $headquartersCommissionRatio = $agentCommissionRatio = 0;
        foreach ($agentRatioConfigList as $key => $value) {
            $startValue = bcmul($value['start_value'], 100);
            $endValue = bcmul($value['end_value'], 100);
            if ($ratio >= $startValue && $ratio <= $endValue) {
                $headquartersCommissionRatio = bcmul($value['main_deduct_ratio'], 100);
                $agentCommissionRatio = bcmul($value['agent_deduct_ratio'], 100);
            }
        }

        return [$headquartersCommissionRatio, $agentCommissionRatio];
    }
}
