<?php

use app\client\model\Config;
use app\client\model\MtConfig;
use app\client\model\Platform;
use app\client\model\PlatformExt;
use app\client\model\StoreLink;
use app\client\model\StoreRepo;
use app\client\model\StoreRepoRelation;
use app\client\model\TaskStoreUserLimit;
use app\common\facade\Redis;
use app\common\model\VersionCompatible;
use app\Log;

/**
 * aes加密
 * @param $data
 * @return array
 * @throws Exception
 */
function aesEncryption($data): array
{
    /** @var \ApiAes $apiAes */
    $key = \CodeGen::gen('2048', \CodeGen::TYPE_MIX_LOWER);
    $iv = substr($key, 32, 16);
    $apiAes = app(\ApiAes::class);
    $ciphertext = json_encode($data, 256);
    $ciphertext = $apiAes->encrypt($ciphertext, $key, $iv);
    $returnData['a'] = $ciphertext;
    $returnData['b'] = $key;
//    $returnData['c'] = $data;
    return $returnData;
}

/**
 * 获取晓晓订单的提醒文字
 * @param $remind
 * @param $isPraise
 * @param $praiseDemand
 * @return string
 */
function getOrderRemind($remind, $isPraise, $praiseDemand, $taskId)
{
    $storeUserLimit = TaskStoreUserLimit::where('task_id', '=', $taskId)
        ->where('data_state', '=', 0)
        ->value('store_user_limit');

    if (!empty($storeUserLimit) && $storeUserLimit === 1) {
        $remind = !empty($remind) ? $remind . ';' : '';
        $remind .= "仅限门店新客下单";
    }

    if ($isPraise === 4) {
        $remind = !empty($remind) ? $remind . ';' : '';
        $remind .= "无需图文，亮星即可";
    }

    return $remind;
}

/**
 * 提醒文字
 * @param int $isPraise
 * @param bool $newVer
 * @return array|string
 */
function getTaskRemind(int $isPraise = 1)
{
    return '本单系统自动识别订单状态，无需提交信息';
//    $version = app()->request->header('xx-version', '');
    // 大于等于20060, 返回新版本数据
//    if (intval($version) >= 20060) {
//        if ($isPraise !== 2) {
//            return '无需提交订单号和相关截图，<text class="highlight">下单并完成评价后等待返现</text>即可';
//        } else {
//            return '无需提交订单号和相关截图，<text class="highlight">订单完成后等待返现</text>即可';
//        }
//    } else {
//        $remind[] = '需在报名后<text class="highlight">1小时内</text>完成下单，<text class="highlight">超时名额会被取消</text>';
//
//        if ($isPraise !== 2) {
//            $remind[] = '下单后需在<text class="highlight">次日23点前完成评价</text>，否则会<text class="highlight">返现失败</text>';
//        } else {
//            $remind[] = '无需提交订单号和相关截图，<text class="highlight">订单完成后等待返现</text>即可';
//        }
//
//        return $remind;
//    }
}

/**
 * 微信小程序进店链接信息
 * @param $info
 * @param string $storeName
 * @return array
 */
function getAppletInfo($info, $storeName = ''): array
{
    if (empty($info['wxPath']) || empty($info['wxAppid']) || empty($info['wxAppOrgid'])) {
        return [
            'app_id' => 'wx2c348cf579062e56',
            'app_original_id' => 'gh_72a4eb2d4324',
            'path' => 'packages/search-business/search-guide/index.html?isFromHome=true&entranceId=0&cateType=0&subCateType=0&keyword=' . $storeName
        ];
    }

    return [
        'path' => $info['wxPath'],
        'app_id' => $info['wxAppid'],
        'app_original_id' => $info['wxAppOrgid'],
    ];
}

/**
 * 微信小程序进店链接信息（饿了么）
 * @param $info
 * @return array
 */
function getEleAppletInfo($info): array
{
    return [
        'path' => $info['wx_path'],
        'app_id' => $info['wx_appid'],
        'app_original_id' => 'gh_6506303a12bb',// 官方接口并未返回这个字段，这里自己写死
    ];
}

/**
 * @param $url
 * @return string
 */
function getFullMtUrl($url): string
{
    if (strpos($url, 'https:') === 0 || strpos($url, 'http:') === 0) {
        return $url;
    }
    return 'https:' . $url;
}

/**
 * 酒水提示
 * @param $platformAbbr
 * @return string
 */
function getDrinksReminder($platformAbbr)
{
    $text = '';
    switch ($platformAbbr) {
        case Platform::PLT_MT:
        case Platform::PLT_HTL_MT:
        case Platform::PLT_ELE:
        case Platform::PLT_HTL_ELE:
        case Platform::PLT_DY:
        case Platform::PLT_JD_MS:
        case Platform::PLT_MTG:
        case Platform::PLT_ELEZ:
        case Platform::PLT_ELEG:
            $text = '每单最多含一瓶酒水，饮品店除外';
            break;
        case Platform::PLT_MT_GP:
        case Platform::PLT_PUBLIC_GP:
        case Platform::PLT_DY_GP:
        case Platform::PLT_MTG_GP:
        default:
            break;
    }
    return $text;
}

function getXxSubsidyAmountText($platformAbbr, $subsidyAmount)
{
    if ($subsidyAmount <= 0) {
        return '';
    }

    $text = '';

    switch ($platformAbbr) {
        case Platform::PLT_MT:
        case Platform::PLT_HTL_MT:
        case Platform::PLT_ELE:
        case Platform::PLT_HTL_ELE:
        case Platform::PLT_DY:
        case Platform::PLT_JD_MS:
        case Platform::PLT_MT_GP:
        case Platform::PLT_PUBLIC_GP:
        case Platform::PLT_DY_GP:
            $text = '(含晓晓补贴' . $subsidyAmount . '元)';
            break;
        case Platform::PLT_MTG:
        case Platform::PLT_ELEZ:
        case Platform::PLT_ELEG:
        case Platform::PLT_MTG_GP:
        default:
            break;
    }

    return $text;
}

/**
 * 获取活动详情文案
 * @return string[]
 */
function getActivityDetailText()
{
    return Config::getValueByKeys([
        'order_time_tip_text', // 抢单时间后面增加问号，提示文案
        'requirement_tip_text', // 图文反馈和文字反馈问号文案
    ]);
}

function getActivityDetailTextByPraiseType($actTextMap, $praiseType)
{
    $result = [];

    // 问号文案处理
    if (!empty($actTextMap['order_time_tip_text'])) {
        $result['order_time_tip_text'] = $actTextMap['order_time_tip_text'];
    }

    if (in_array((int)$praiseType, [1, 3]) && !empty($actTextMap['requirement_tip_text'])) {
        $result['requirement_tip_text'] = $actTextMap['requirement_tip_text'];
    }

    return $result;
}

/**
 * 活动报名按钮文案
 * @param $platformAbbr
 * @return string
 */
function getSignUpBtnText($platformAbbr)
{
    $text = '确认并报名';

    switch ($platformAbbr) {
        case Platform::PLT_MT:
        case Platform::PLT_HTL_MT:
        case Platform::PLT_ELE:
        case Platform::PLT_HTL_ELE:
        case Platform::PLT_MTG:
        case Platform::PLT_ELEZ:
        case Platform::PLT_ELEG:
            $text = '领红包并确定报名';
            break;
        case Platform::PLT_MTG_GP:
        case Platform::PLT_MT_GP:
        case Platform::PLT_PUBLIC_GP:
        case Platform::PLT_DY_GP:
        case Platform::PLT_JD_MS:
        case Platform::PLT_DY:
        default:
            break;
    }

    return $text;
}

/**
 * 美团首次上线app版本号
 * @return int
 */
function getFirstMtVersion(): int
{
    $appVersion = MtConfig::get_value('value', ['key' => 'mt_first_app_version']);
    return intval($appVersion);
}

function getGeneralAppletInfo($task): array
{
    $abbr = Platform::where('id', $task['task_type'])->field(['platform_abbreviation'])->value('platform_abbreviation');
    // 店铺搜索关键字
    $storeName = $task['search_keyword'];
    if ($abbr === Platform::PLT_GENERAL_ELE) {
        $storeNameAppUrl = 'eleme://search?keyword=' . $storeName;
        $storeNameAppMtwmUrl = 'eleme://search?keyword=' . $storeName;
    } elseif ($abbr === Platform::PLT_GENERAL_MT) {
        $storeNameAppUrl = 'imeituan://www.meituan.com/search?q=' . $storeName;
        $storeNameAppMtwmUrl = 'meituanwaimai://waimai.meituan.com/search?query=' . $storeName;
    }
    return [
        'app_url' => $storeNameAppUrl,
        'app_mtwm_url' => $storeNameAppMtwmUrl,
    ];
}

/**
 * @param $task
 * @return array
 */
function getSelfAppletInfo($task): array
{
    $storeId = $task['store_id'];
    try {
        $repoId = (int)StoreRepoRelation::get_value('repo_id', ['store_id' => $storeId]);
        if ($repoId <= 0) {
            return [];
        }

        $info = StoreRepo::get_one(['id' => $repoId,], ['field' => 'store_id,tag,mtwm_poi_id as poi_id,store_type,
        ele_link,vender_id,ele_market_shop_id as shop_id']);
        $abbr = Platform::where('id', $info['store_type'])
            ->field(['platform_abbreviation'])
            ->value('platform_abbreviation');

        if (empty($info)) {
            return [];
        }

        if ($abbr === 'ele' && empty($info['ele_link'])) {
            return [];
        }

        if ($abbr === Platform::PLT_MT && empty($info['store_id']) && strpos($info['poi_id'], '-100') === 0) {
            return [];
        }

        $appUrl = '';
        $linkInfo = StoreLink::get_one(['tag' => $info['tag']], ['field' => 'app_id,original_id as app_original_id,path']);

        if (!empty($linkInfo['path'])) {
            $linkInfo['path'] = preg_replace_callback('/\{#(\w+)\}/', function ($matches) use ($info) {
                return $info[$matches[1]] ?? '';
            }, $linkInfo['path']);
        }

        if ($abbr === Platform::PLT_MT) {
            // 覆写path
            switch ($info['tag']) {
                case 'mt_takeout':
                    $path = 'packages/restaurant/restaurant/restaurant';
                    break;
                case 'mt_market':
                    $path = 'sub_shangou/sg/pages/restaurant/restaurant.html';
                    break;
            }

            if (!empty($path)) {
                if (strpos($info['poi_id'], '-100') === 0) {
                    $linkInfo['path'] = $path . '?poi_id_str=' . $info['store_id'];
                    $appUrl = 'imeituan://www.meituan.com/takeout/foods?poi_id_str=' . $info['store_id'];
                    $appMtwmUrl = 'meituanwaimai://waimai.meituan.com/menu?poi_id_str=' . $info['store_id'];
                } else {
                    $linkInfo['path'] = $path . '?poi_id=' . $info['poi_id'];
                    $appUrl = 'imeituan://www.meituan.com/takeout/foods?poi_id=' . $info['poi_id'];
                    $appMtwmUrl = 'meituanwaimai://waimai.meituan.com/menu?restaurant_id=' . $info['poi_id'];
                }
            }
        }

        $storeName = !empty($task['search_keyword']) ? $task['search_keyword'] : $task['store_name'];// 店铺名称

        if ($abbr === Platform::PLT_ELE) {
            $appUrl = 'eleme://catering?restaurant_id=' . $info['store_id'];
            $storeNameAppUrl = 'eleme://search?keyword=' . $storeName;
            $storeNameAppMtwmUrl = 'eleme://search?keyword=' . $storeName;
        } elseif ($abbr === Platform::PLT_MT) {
            $appUrl = empty($appUrl) ? "imeituan://www.meituan.com/takeout/foods?poi_id={$info['poi_id']}" : $appUrl;
            $appMtwmUrl = empty($appMtwmUrl) ? "meituanwaimai://waimai.meituan.com/menu?restaurant_id={$info['poi_id']}" : $appMtwmUrl;
            $storeNameAppUrl = 'imeituan://www.meituan.com/search?q=' . $storeName;
            $storeNameAppMtwmUrl = 'meituanwaimai://waimai.meituan.com/search?query=' . $storeName;
        } elseif ($abbr === Platform::PLT_PUBLIC_GP) {
            $appUrl = 'dianping://shopinfo?shopuuid=' . $info['store_id'] . '&shoptype=10';
        } elseif ($abbr === Platform::PLT_MT_GP) {
            $appUrl = 'imeituan://www.meituan.com/food/poi/detail?id=' . $info['store_id'];
            $storeNameAppMtwmUrl = 'meituanwaimai://waimai.meituan.com/search?query=' . $storeName;
        } elseif ($abbr === Platform::PLT_JD_MS) {
            $appUrl = 'openapp.jdmobile://virtual?params={"category":"jump","des":"jdmp","appId":"19828010","scene":"applets_share_back","path":"","vapptype":"1","pageAlias":"","param":{"storeId":"' . $info['store_id'] . '","venderId":"' . $info['vender_id'] . '","source":"qrCode","skuId":""}}';
        }

        // 饿了么商超app_url返回空，直接跳转小程序
        if ($info['tag'] == 'ele_market') {
            $appUrl = '';
            $appMtwmUrl = '';
        }

        return array_merge([
            'app_url' => empty($appUrl) ? '' : $appUrl,
            'app_mtwm_url' => empty($appMtwmUrl) ? '' : $appMtwmUrl,
            'store_name_app_url' => empty($storeNameAppUrl) ? '' : $storeNameAppUrl,
            'store_name_app_mtwm_url' => empty($storeNameAppMtwmUrl) ? '' : $storeNameAppMtwmUrl,
        ], $linkInfo->toArray());
    } catch (\Exception $e) {
        Log::error('getSelfAppletInfo', $e, '获取店铺链接失败', $storeId);
    }
    return [];
}

function getDefaultAppletInfo($pltAbbr, $storeName, $type)
{
    if ($type === 2) {
        return [
            'app_id' => 'wx82042dd743cb5160',
            'app_original_id' => 'gh_555eb249f4b8',
            'path' => empty($storeName) ? '' : '/package/miniprogram/to?link=' . urlencode($storeName)
        ];
    }

    switch ($pltAbbr) {
        case Platform::PLT_MT:
        case Platform::PLT_GENERAL_MT:
            // 美团外卖
            return [
                'app_id' => 'wx2c348cf579062e56',
                'app_original_id' => 'gh_72a4eb2d4324',
                'path' => 'packages/search-business/search-guide/index.html?isFromHome=true&entranceId=0&cateType=0&subCateType=0&keyword=' . $storeName
            ];
        case Platform::PLT_REPURCHASE_CARD_MT:
        case Platform::PLT_REPURCHASE_CARD_ELE:
        case Platform::PLT_REPURCHASE_CARD_JD_MS:
            return null;
        case Platform::PLT_ELE:
        case Platform::PLT_GENERAL_ELE:
            // 饿了么外卖
            return [
                'app_id' => 'wxece3a9a4c82f58c9',
                'app_original_id' => 'gh_6506303a12bb',
                'path' => "ele-search/pages/search/search.html?&homeSearchWord={$storeName}&fromPage=wx_miniapp"
            ];
        case Platform::PLT_JD_MS:
            return [
                'app_id' => 'wx91d27dbf599dff74',
                'app_original_id' => 'gh_45b306365c3d',
                'path' => 'pages/index/index?dTabType='
            ];
        default:
            return [];
    }
}

/**
 * 活动详情
 * @param $platform
 * @return mixed
 */
function getPlatformActivity($platform)
{
    $version = app()->request->header('xx-version', '');

    // 小于20060, 返回老版本数据
    if (intval($version) < 20060) {
        return json_decode($platform['activity_flow'], true);
    }

    $activityFlow = PlatformExt::where('platform_abbreviation', $platform['platform_abbreviation'])
        ->where('min_version', '<=', $version)
        ->order(['min_version' => 'desc'])
        ->value('activity_flow');

//    $replaceTag = Config::getValueByKey('high_light_replace_tag');
//    $replaceTag = json_decode($replaceTag, true);
//
//    $activityFlow = str_replace('{highlightStart}', $replaceTag['highlightStart'], $activityFlow);
//    $activityFlow = str_replace('{highlightEnd}', $replaceTag['highlightEnd'], $activityFlow);

    return json_decode($activityFlow, true);
}


/**
 * 活动详情
 * @param $platform
 * @return mixed
 */
function getPlatformActivityOrderLimit($platform)
{
    $version = app()->request->header('xx-version', '');

    // 小于20060, 返回老版本数据
    if (intval($version) < 20060) {
        return json_decode($platform['activity_flow'], true);
    }

    $activityFlow = PlatformExt::where('platform_abbreviation', $platform['platform_abbreviation'])
        ->where('min_version', '<=', $version)
        ->order(['min_version' => 'desc'])
        ->value('activity_flow_order_limit');

    $activityFlow = replaceOrderTimeoutTag($activityFlow);

    return json_decode($activityFlow, true);
}

/**
 * 判断配置
 *
 * @param $redisKey
 * @param $versionConfig
 * @return bool
 * @throws RedisException
 */
function config_version_compare($redisKey, $versionConfig): bool
{
    $clientVersion = getCurrentVersion();
    $key = Redis::getFullKeyName($redisKey);
    $compatibleVersion = Redis::get($key);
    if (empty($compatibleVersion)) {
        $compatibleVersion = VersionCompatible::where('version_name', '=', $versionConfig)
            ->where('data_state', '=', 0)
            ->value('min_version');
        if (!empty($compatibleVersion)) {
            Redis::set($key, $compatibleVersion, 86400);
            return $clientVersion >= $compatibleVersion;
        } else {
            return false;
        }
    } else {
        return $clientVersion >= $compatibleVersion;
    }
}

/**
 * 大牌版本号对比
 *
 * @return bool
 * @throws RedisException
 */
function brand_version_compare(): bool
{
    $clientVersion = getCurrentVersion();
    $key = Redis::getFullKeyName(Redis::$brand_version);
    $compatibleVersion = Redis::get($key);
    if (empty($compatibleVersion)) {
        $compatibleVersion = VersionCompatible::where('version_name', '=', VersionCompatible::BRAND_ONLINE)
            ->where('data_state', '=', 0)
            ->value('min_version');
        if (!empty($compatibleVersion)) {
            Redis::set($key, $compatibleVersion, 86400);
            return $clientVersion >= $compatibleVersion;
        } else {
            return false;
        }
    } else {
        return $clientVersion >= $compatibleVersion;
    }
}

/**
 * 会员体系版本号对比
 *
 * @return bool
 * @throws \RedisException
 */
function member_version_compare(): bool
{
    return config_version_compare(Redis::$member_level, VersionCompatible::MEMBER_LEVEL);
}

/**
 * 运营消息推送版本号对比
 *
 * @return bool
 * @throws \RedisException
 */
function message_push_version_compare(): bool
{
    return config_version_compare(Redis::$message_push, VersionCompatible::MESSAGE_PUSH);
}

/**
 * 会员体系版本号对比
 *
 * @return bool
 * @throws \RedisException
 */
function user_energy_version_compare(): bool
{
    return config_version_compare(Redis::$user_energy, VersionCompatible::USER_ENERGY);
}

/**
 * 会员体系版本号对比
 *
 * @return bool
 * @throws \RedisException
 */
function task_vip_level_version_compare(): bool
{
    return config_version_compare(Redis::$task_vip_level, VersionCompatible::TASK_VIP_LEVEL);
}

/**
 * 上传数据防骗新版本
 * @return bool
 * @throws \RedisException
 */
function upload_data_fp_version_compare(): bool
{
    return config_version_compare(Redis::$upload_data_fp, VersionCompatible::UPLOAD_DATA_FP);
}

/**
 * 多任务合并版本
 * @return bool
 * @throws RedisException
 */
function multi_task_merge_version_compare(): bool
{
    return config_version_compare(Redis::$multi_task_merge, VersionCompatible::MERGE_TASK_LIST_V);
}

/**
 * 手动使用卡券
 * @return bool
 * @return bool
 * @throws RedisException
 * @throws RedisException
 */
function rebate_sort_version_compare(): bool
{
    return config_version_compare(Redis::$rebate_sort, VersionCompatible::REBATE_SORT_V);
}

function select_coupon_v(): bool
{
    return config_version_compare(Redis::$select_coupon_v, VersionCompatible::SELECT_COUPON_V);
}

/**
 * 试吃官超级返活动版本
 * @return bool
 * @throws RedisException
 */
function cjf_scg_activity_version_compare(): bool
{
    return config_version_compare(Redis::$cjf_scg_activity, VersionCompatible::CJF_SCG_ACTIVITY);
}

/**
 * 用户端优化版本
 * @return bool
 * @throws RedisException
 */
function user_client_improve_v_compare(): bool
{
    return config_version_compare(Redis::$user_client_improve, VersionCompatible::USER_CLIENT_IMPROVE_V);
}

/**
 * 超级返利券版本
 * @return bool
 * @throws RedisException
 */
function super_rebate_coupon_v(): bool
{
    return config_version_compare(Redis::$super_rebate_coupon, VersionCompatible::SUPER_REBATE_COUPON_V);
}

/**
 * 超时取消版本
 * @return bool
 * @throws RedisException
 */
function order_timout_v(): bool
{
    return config_version_compare(Redis::$order_timout_v, VersionCompatible::ORDER_TIMOUT_V);
}

/**
 * 多任务合并版本，反馈文案变更
 * @return string
 * @throws RedisException
 */
function multiTaskFeedbackTextChange(): string
{
    // 需求是要兼容老版本：
    // 对于老版本任然显示 `反馈`, 对于新版本，显示 `评价`， 需求如此！╮(╯▽╰)╭
    //return multi_task_merge_version_compare() ? '评价' : '反馈';
    return '反馈';
}

/**
 * java任务合并版本
 * @return bool
 * @throws RedisException
 */
function java_task_merge_version_compare(): bool
{
    return config_version_compare(Redis::$java_task_merge, VersionCompatible::JAVA_TASK_LIST_V);
}

/**
 * 比例任务版本
 * @return bool
 * @throws RedisException
 */
function ratio_task_version(): bool
{
    return config_version_compare(Redis::$ratio_task, VersionCompatible::RATIO_TASK_V);
}

/**
 * 到店团购版本
 * @return bool
 * @throws RedisException
 */
function in_store_gp_version(): bool
{
    return config_version_compare(Redis::$in_store_gp, VersionCompatible::IN_STORE_GP_V);
}

/**
 * 京东秒送版本
 * @return bool
 * @throws RedisException
 */
function jd_ms_version(): bool
{
    return config_version_compare(Redis::$jd_ms, VersionCompatible::JD_MS_V);
}

/**
 * app大改版版本
 * @return bool
 * @throws RedisException
 */
function app_ui_optimization_version(): bool
{
    return config_version_compare(Redis::$app_ui_optimization, VersionCompatible::APP_UI_OPTIMIZATION_V);
}

/**
 * 订单流程优化版本
 * @return bool
 * @throws RedisException
 */
function order_flow_optimization_version(): bool
{
    return config_version_compare(Redis::$order_flow_optimization, VersionCompatible::ORDER_FLOW_OPTIMIZATION_V);
}

/**
 * 美团版本类型(1:满返类型; 2:比例类型 3: 统一返现类型)
 * @return int
 */
function getMtRatioVersionType(): int
{
    // 新版本统一返现类型
    if (cjf_scg_activity_version_compare() && Config::getValueByKey('mtg_cash_back_to_wallet_enable')) {
        return 3;
    } else {
        $version = getCurrentVersion();
        return $version >= 20150 ? 2 : 1;
    }
}

/**
 * 饿了么版本类型(1:满返类型; 2:比例类型)
 * @return int
 */
function getEleRatioVersionType(): int
{
    return getCurrentVersion() >= 20160 ? 2 : 1;
}

/**
 * 当前版本号
 * @return int
 */
function getCurrentVersion(): int
{
    return (int)(app()->request->header('xx-version', ''));
}

/**
 * 限制低版本报名
 * @return void
 * @throws Exception
 */
function limitLowVersionSignUp()
{
    $presetVersion = (int)Config::getValueByKey(Config::LOW_VERSION_LIMIT_SIGN_UP);

    if (getCurrentVersion() < $presetVersion) {
        throw new \Exception('请到手机应用商店更新最新版本再报名哦~');
    }
}

/**
 * 是否跳转app
 * @param $isEnterMtApp
 * @param $platformAbbreviation
 * @param $appletInfo
 * @param $type
 * @return mixed|string
 */
function isGotoAppOrApplet($isEnterMtApp, $platformAbbreviation, $appletInfo, $type)
{
    // 美团和美团官方活动的，并且开关关闭以后，就不跳转app
    if (empty($isEnterMtApp) && in_array($platformAbbreviation, ['mt', 'mtg'])) {
        return '';
    }

    return $appletInfo[$type] ?? '';
}

/**
 * 处理小数点
 * // 示例
 * echo formatNumber(9.99);  // 输出: 9.99
 * echo formatNumber(9.00);  // 输出: 9
 * echo formatNumber(10.0);  // 输出: 10
 * echo formatNumber(10.5);  // 输出: 10.5
 *
 * @param $number
 * @return string
 */
function formatNumber($number): string
{
    $formatted = number_format($number, 2, '.', '');
    return rtrim(rtrim($formatted, '0'), '.');
}

/**
 * 根据不同的版本号返回
 * @param $isPraise
 * @return int|mixed
 */
function getCurrentIsPraise($isPraise)
{
    // 如果说文字反馈的状态，返回给前端都是1，图文反馈
    if ($isPraise == 3 || $isPraise == 4) {
        $isPraise = 1;
    }

    return $isPraise;
}

/**
 * 根据反馈类型获取文案
 * @param $isPraise
 * @return string|void
 */
function getTipsByIsPraise($isPraise)
{
    switch ($isPraise) {
        case 1:
            return "晓晓提醒：反馈内容需要满足含图含字哦！";
        case 2:
            return "晓晓提醒: 本单无需反馈，只需上传已完成截图。如反馈将无法拿到额外返利";
        case 3:
            return "晓晓提醒：反馈内容仅需文字！";
        case 4:
            return "晓晓提醒：本单无需图文反馈，只亮星即可，如反馈图文将无法拿到额外返利哦";
        default:
            return "";
    }
}

/**
 * 神抢手链接
 * @param array $redirectLink
 * @return array
 */
function getSqsLink($redirectLink = [])
{
    $resInfoArr = [];
    try {
        if (empty($redirectLink)) {
            return $resInfoArr;
        }

        if (isset($redirectLink['meituanDeepLink'])) {
            $resInfoArr['sqs_meal_imt_dp_link'] = $redirectLink['meituanDeepLink'];
            $deprArr = explode('?', $redirectLink['meituanDeepLink'], 2);

            if (!empty($deprArr[1])) {
                $resInfoArr['sqs_meal_mtwm_dp_link'] = 'meituanwaimai://waimai.meituan.com/browser?' . $deprArr[1];
            }
        }

        if (isset($redirectLink['meituanMicroProgramLink'])) {
            $resInfoArr['sqs_meal_micro_link'] = [
                'app_id' => 'wxde8ac0a21135c07d',
                'app_original_id' => 'gh_870576f3c6f9',
                'path' => $redirectLink['meituanMicroProgramLink']
            ];
        }
    } catch (\Exception $e) {
    }

    return $resInfoArr;
}

function convert_distance($distance)
{
    $unitKm = substr($distance, -2);
    if ($unitKm === 'km') {
        return floatval($distance);
    } else {
        // 米转千米
        return floatval($distance) / 1000;
    }
}

/**
 * 补全跳转url信息
 * @param $urlInfo
 * @return array
 */
function completionJumpUrl($urlInfo)
{
    $result = [];

    if (isset($urlInfo['nativeUrl'])) {
        $result['app_url'] = $urlInfo['nativeUrl'];
    }

    if (isset($urlInfo['wxUrl'])) {
        $result['applet_info'] = [
            'app_id' => 'wxde8ac0a21135c07d',
            'app_original_id' => 'gh_870576f3c6f9',
            'path' => $urlInfo['wxUrl']
        ];
    }

    return $result;
}

/**
 * 美团分数显示
 * @param $mtGpScore
 * @return float
 */
function displayMtGpScore($mtGpScore)
{
    return (float)bcdiv($mtGpScore, '10', 2);
}
