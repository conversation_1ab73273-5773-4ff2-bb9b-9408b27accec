<?php

namespace app\client\implement\task;


class ProcessorContext
{
    /**
     * @var array|IProcessor[]
     */
    private array $processors = [];

    /**
     * 注册处理器
     * @param IProcessor $processor
     */
    public function register(IProcessor $processor): void
    {
        $this->processors[$processor->key()] = $processor;
    }

    /**
     * @param string|null $key
     * @return mixed
     */
    public function getProcessor(?string $key= null)
    {
        if (is_null($key)) {
            return $this->processors;
        }

        return $this->processors[$key] ?? null;
    }
}