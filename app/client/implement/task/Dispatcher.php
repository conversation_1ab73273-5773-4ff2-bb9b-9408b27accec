<?php

namespace app\client\implement\task;

use app\client\implement\task\version\JavaTaskListVersion;
use app\client\implement\task\version\MergeTaskListVersion;
use app\client\implement\task\version\NewTaskListVersion;
use RedisException;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Dispatcher
{
    /**
     * 获取任务列表
     * @param int $version
     * @param array $params
     * @return array
     * @throws RedisException
     */
    public function dispatch(int $version, array $params = []): array
    {
        if (java_task_merge_version_compare()) {
            $task = new JavaTaskListVersion();
        } elseif (multi_task_merge_version_compare()) {
            $task = new MergeTaskListVersion();
        } else {
            $task = new NewTaskListVersion();
        }

        $task->setVersion($version);
        $task->setParams($params);

        return $task->getTaskList();
    }
}