<?php

namespace app\client\implement\task;


class TaskList
{
    /**
     * 任务调度
     * @var Dispatcher
     */
    private Dispatcher $dispatcher;

    /**
     * 版本
     * @var int
     */
    private int $version = 0;

    /**
     * @var array
     */
    private array $params;

    /**
     * 构造函数
     * @param array $params
     */
    public function __construct(array $params = [])
    {
        if (isset($params['version'])) {
            $this->version = intval($params['version']);
        }

        $this->params = $params;
        $this->dispatcher = new Dispatcher();
    }

    /**
     * 任务列表
     * @return array
     * @throws \RedisException
     */
    public function getTaskList(): array
    {
        return $this->dispatcher->dispatch($this->version, $this->params);
    }
}