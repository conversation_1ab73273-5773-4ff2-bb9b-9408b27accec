<?php

namespace app\client\implement\task;

abstract class TaskListVersion implements ITaskList
{
    /**
     * @var array
     */
    protected array $params;

    /**
     * @var int
     */
    protected int $version;

    /**
     * 默认数据
     * @return array
     */
    protected function getDefaultData(): array
    {
        return [
            'count' => 0,
            'totalPages' => 0,
            'list' => [],
            'sessionId' => '',
            'pagePvId' => '',
            'mtPvId' => '',
            'requestId' => ''
        ];
    }

    /**
     * @param string $key
     * @param null $default
     * @return mixed|null
     */
    public function getVByKey(string $key, $default = null)
    {
        return $this->params[$key] ?? $default;
    }

    /**
     * @param array $params
     * @return void
     */
    public function setParams(array $params)
    {
        $this->params = $params;
    }

    /**
     * @return array
     */
    public function getParams(): array
    {
        return $this->params;
    }

    /**
     * @param int $version
     * @return void
     */
    public function setVersion(int $version)
    {
        $this->version = $version;
    }

    /**
     * @return int
     */
    public function getVersion(): int
    {
        return $this->version;
    }
}