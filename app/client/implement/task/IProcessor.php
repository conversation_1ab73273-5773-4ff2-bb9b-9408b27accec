<?php

namespace app\client\implement\task;

interface IProcessor
{
    /**
     * 绑定要处理的数据
     * @param array $data
     */
    public function bind(array $data);

    /**
     * @return string
     */
    public function key(): string;

    /**
     * 是否允许加载
     * @param int $agentId
     * @param array $params
     * @return bool
     */
    public function canLoad(int $agentId, array $params): bool;

    /**
     * 处理数据
     * @param $requestId
     * @param $userId
     * @param $agentId
     * @param array $params
     * @return array
     */
    public function process($requestId, $userId, $agentId, array $params = []): array;
}