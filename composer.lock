{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "b148eeacb6e6e6baa5247bcc2b0d5de2", "packages": [{"name": "adbario/php-dot-notation", "version": "2.5.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/adbario/php-dot-notation/2.5.0/adbario-php-dot-notation-2.5.0.zip", "reference": "081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae", "shasum": ""}, "require": {"ext-json": "*", "php": "^5.5 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.7|^6.6|^7.5|^8.5|^9.5", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Adbar\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP dot notation access to arrays", "homepage": "https://github.com/adbario/php-dot-notation", "keywords": ["ArrayA<PERSON>ess", "dotnotation"], "time": "2022-10-14T20:31:46+00:00"}, {"name": "alibabacloud/tea", "version": "3.2.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/alibabacloud/tea/3.2.1/alibabacloud-tea-3.2.1.zip", "reference": "1619cb96c158384f72b873e1f85de8b299c9c367", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.4", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "*", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Client of Tea for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibabacloud", "client", "cloud", "tea"], "time": "2023-05-16T06:43:41+00:00"}, {"name": "alibabacloud/tea-fileform", "version": "0.3.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/alibabacloud/tea-fileform/0.3.4/alibabacloud-tea-fileform-0.3.4.zip", "reference": "4bf0c75a045c8115aa8cb1a394bd08d8bb833181", "shasum": ""}, "require": {"alibabacloud/tea": "^3.0", "php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\FileForm\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea File Library for PHP", "time": "2020-12-01T07:24:35+00:00"}, {"name": "alipaysdk/easysdk", "version": "2.2.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/alipaysdk/easysdk/2.2.3/alipaysdk-easysdk-2.2.3.zip", "reference": "c6008839a22a5fca08e9f8536730f7abfed522d5", "shasum": ""}, "require": {"alibabacloud/tea": "^3.1", "alibabacloud/tea-fileform": "^0.3.2", "ext-ctype": "*", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": ">=6.3", "php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Alipay\\EasySDK\\": "php/src/"}}, "license": ["Apache-2.0"], "authors": [{"name": "junying.wjy", "email": "<EMAIL>"}], "description": "支付宝官方 Alipay Easy SDK", "time": "2022-11-28T14:04:57+00:00"}, {"name": "bacon/bacon-qr-code", "version": "2.0.8", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/bacon/bacon-qr-code/2.0.8/bacon-bacon-qr-code-2.0.8.zip", "reference": "8674e51bb65af933a5ffaf1c308a660387c35c22", "shasum": ""}, "require": {"dasprid/enum": "^1.0.3", "ext-iconv": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phly/keep-a-changelog": "^2.1", "phpunit/phpunit": "^7 | ^8 | ^9", "spatie/phpunit-snapshot-assertions": "^4.2.9", "squizlabs/php_codesniffer": "^3.4"}, "suggest": {"ext-imagick": "to generate QR code images"}, "type": "library", "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "time": "2022-12-07T17:46:57+00:00"}, {"name": "dasprid/enum", "version": "1.0.6", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/dasprid/enum/1.0.6/dasprid-enum-1.0.6.zip", "reference": "8dfd07c6d2cf31c8da90c53b83c026c7696dda90", "shasum": ""}, "require": {"php": ">=7.1 <9.0"}, "require-dev": {"phpunit/phpunit": "^7 || ^8 || ^9 || ^10 || ^11", "squizlabs/php_codesniffer": "*"}, "type": "library", "autoload": {"psr-4": {"DASPRiD\\Enum\\": "src/"}}, "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "PHP 7.1 enum implementation", "keywords": ["enum", "map"], "time": "2024-08-09T14:30:48+00:00"}, {"name": "edward1108/edward-captcha", "version": "1.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/edward1108/edward-captcha/1.1/edward1108-edward-captcha-1.1.zip", "reference": "426ceee34507c30d4b21e0dd349b571371aef700", "shasum": ""}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0.0"}, "type": "library", "extra": {"think": {"services": ["edward\\captcha\\CaptchaService"], "config": {"captcha": "src/config.php"}}}, "autoload": {"files": ["src/helper.php"], "psr-4": {"edward\\captcha\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "edward", "email": "<EMAIL>"}], "description": "ThinkPHP6验证码(图片、短信)支持api友好化", "homepage": "https://github.com/Edward1108/edward-captcha", "time": "2020-07-22T06:49:35+00:00"}, {"name": "endroid/qr-code", "version": "4.6.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/endroid/qr-code/4.6.1/endroid-qr-code-4.6.1.zip", "reference": "a75c913b0e4d6ad275e49a2c1de1cacffc6c2184", "shasum": ""}, "require": {"bacon/bacon-qr-code": "^2.0.5", "php": "^7.4||^8.0"}, "require-dev": {"endroid/quality": "dev-master", "ext-gd": "*", "khanamiryan/qrcode-detector-decoder": "^1.0.4", "setasign/fpdf": "^1.8.2"}, "suggest": {"ext-gd": "Enables you to write PNG images", "khanamiryan/qrcode-detector-decoder": "Enables you to use the image validator", "roave/security-advisories": "Makes sure package versions with known security issues are not installed", "setasign/fpdf": "Enables you to use the PDF writer"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"Endroid\\QrCode\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Endroid QR Code", "homepage": "https://github.com/endroid/qr-code", "keywords": ["code", "endroid", "php", "qr", "qrcode"], "time": "2022-10-26T08:48:17+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.18.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/ezyang/htmlpurifier/v4.18.0/ezyang-htmlpurifier-v4.18.0.zip", "reference": "cb56001e54359df7ae76dc522d08845dc741621b", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2024-11-01T03:51:45+00:00"}, {"name": "guzzlehttp/command", "version": "1.3.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/guzzlehttp/command/1.3.1/guzzlehttp-command-1.3.1.zip", "reference": "0eebc653784f4902b3272e826fe8e88743d14e77", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.8", "guzzlehttp/promises": "^1.5.3 || ^2.0.1", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.19 || ^9.5.8"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Command\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "description": "Provides the foundation for building command-based web service clients", "time": "2023-12-03T20:46:20+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.2", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/guzzlehttp/guzzle/7.9.2/guzzlehttp-guzzle-7.9.2.zip", "reference": "d281ed313b989f213357e3be1a179f02196ac99b", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "time": "2024-07-24T11:22:20+00:00"}, {"name": "guzzlehttp/guzzle-services", "version": "1.4.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/guzzlehttp/guzzle-services/1.4.1/guzzlehttp-guzzle-services-1.4.1.zip", "reference": "bcab7c0d61672b606510a6fe5af3039d04968c0f", "shasum": ""}, "require": {"guzzlehttp/command": "^1.3.1", "guzzlehttp/guzzle": "^7.8", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "guzzlehttp/uri-template": "^1.0.1", "php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.19 || ^9.5.8"}, "suggest": {"gimler/guzzle-description-loader": "^0.0.4"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Command\\Guzzle\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Konafets"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "description": "Provides an implementation of the Guzzle Command library that uses Guzzle service descriptions to describe web services, serialize requests, and parse responses into easy to use model structures.", "time": "2023-12-03T20:48:14+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/guzzlehttp/promises/2.0.4/guzzlehttp-promises-2.0.4.zip", "reference": "f9c436286ab2892c7db7be8c8da4ef61ccf7b455", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2024-10-17T10:06:22+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/guzzlehttp/psr7/2.7.0/guzzlehttp-psr7-2.7.0.zip", "reference": "a70f5c95fb43bc83f07c9c948baa0dc1829bf201", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2024-07-18T11:15:46+00:00"}, {"name": "guzzlehttp/uri-template", "version": "v1.0.3", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/guzzlehttp/uri-template/v1.0.3/guzzlehttp-uri-template-v1.0.3.zip", "reference": "ecea8feef63bd4fef1f037ecb288386999ecc11c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "uri-template/tests": "1.0.0"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\UriTemplate\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "description": "A polyfill class for uri_template of PHP", "keywords": ["guzzlehttp", "uri-template"], "time": "2023-12-03T19:50:20+00:00"}, {"name": "liliuwei/thinkphp-jump", "version": "v1.5", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/liliuwei/thinkphp-jump/v1.5/liliuwei-thinkphp-jump-v1.5.zip", "reference": "481d41b922095f08230609919be5d19354c50540", "shasum": ""}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0", "topthink/think-view": "^1.0"}, "type": "think-extend", "extra": {"think": {"config": {"jump": "src/config/jump.php"}}}, "autoload": {"psr-4": {"liliuwei\\think\\": "src/"}}, "license": ["Apache-2.0"], "authors": [{"name": "lili<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "适用于thinkphp6.0的跳转扩展", "keywords": ["error", "redirect", "result", "success", "think-jump", "thinkphp"], "time": "2020-03-20T15:11:56+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.2.6", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/maennchen/zipstream-php/2.2.6/maennchen-zipstream-php-2.2.6.zip", "reference": "30ad6f93cf3efe4192bc7a4c9cad11ff8f4f237f", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": "^7.4 || ^8.0", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.9", "guzzlehttp/guzzle": "^6.5.3 || ^7.2.0", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.4", "phpunit/phpunit": "^8.5.8 || ^9.4.2", "vimeo/psalm": "^4.1"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "time": "2022-11-25T18:57:19+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/markbaker/complex/3.0.2/markbaker-complex-3.0.2.zip", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/markbaker/matrix/3.0.1/markbaker-matrix-3.0.1.zip", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "time": "2022-12-02T22:17:43+00:00"}, {"name": "myclabs/php-enum", "version": "1.8.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/myclabs/php-enum/1.8.4/myclabs-php-enum-1.8.4.zip", "reference": "a867478eae49c9f59ece437ae7f9506bfaa27483", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.6.2"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "time": "2022-08-04T09:53:51+00:00"}, {"name": "php-amqplib/php-amqplib", "version": "v2.8.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/php-amqplib/php-amqplib/v2.8.1/php-amqplib-php-amqplib-v2.8.1.zip", "reference": "84449ffd3f5a7466bbee3946facb3746ff11f075", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-sockets": "*", "php": ">=5.4.0"}, "replace": {"videlalvaro/php-amqplib": "self.version"}, "require-dev": {"phpdocumentor/phpdocumentor": "^2.9", "phpunit/phpunit": "^4.8", "scrutinizer/ocular": "^1.1", "squizlabs/php_codesniffer": "^2.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"PhpAmqpLib\\": "PhpAmqpLib/"}}, "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "Original Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Formerly videlalvaro/php-amqplib.  This library is a pure PHP implementation of the AMQP protocol. It's been tested against RabbitMQ.", "homepage": "https://github.com/php-amqplib/php-amqplib/", "keywords": ["message", "queue", "rabbitmq"], "time": "2018-11-13T09:35:17+00:00"}, {"name": "phpoffice/phpexcel", "version": "1.8.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpoffice/phpexcel/1.8.2/phpoffice-phpexcel-1.8.2.zip", "reference": "1441011fb7ecdd8cc689878f54f8b58a6805f870", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "php": "^5.2|^7.0"}, "require-dev": {"squizlabs/php_codesniffer": "2.*"}, "type": "library", "autoload": {"psr-0": {"PHPExcel": "Classes/"}}, "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "http://blog.maartenballiauw.be"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://rootslabs.net"}, {"name": "<PERSON>", "homepage": "http://markbakeruk.net"}], "description": "PHPExcel - OpenXML - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PHPExcel", "keywords": ["OpenXML", "excel", "php", "spreadsheet", "xls", "xlsx"], "time": "2018-11-22T23:07:24+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.29.6", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/phpoffice/phpspreadsheet/1.29.6/phpoffice-phpspreadsheet-1.29.6.zip", "reference": "08597725b84570cd6f32bf0ea92e75a803ef28c2", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.15", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.4 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^1.0 || ^2.0 || ^3.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "time": "2024-12-08T05:49:00+00:00"}, {"name": "psr/container", "version": "1.1.2", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/container/1.1.2/psr-container-1.1.2.zip", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/http-client/1.0.3/psr-http-client-1.0.3.zip", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/http-factory/1.1.0/psr-http-factory-1.1.0.zip", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/http-message/1.1/psr-http-message-1.1.zip", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "1.1.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/log/1.1.4/psr-log-1.1.4.zip", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/psr/simple-cache/1.0.1/psr-simple-cache-1.0.1.zip", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "time": "2017-10-23T01:57:42+00:00"}, {"name": "qcloud/cos-sdk-v5", "version": "v2.6.15", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/qcloud/cos-sdk-v5/v2.6.15/qcloud-cos-sdk-v5-v2.6.15.zip", "reference": "17b973a388dc8848947054daace0fff8d2a4eee0", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.2.1 || ^7.0", "guzzlehttp/guzzle-services": "^1.1", "guzzlehttp/psr7": "^1.3.1 || ^2.0", "php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"files": ["src/Common.php"], "psr-4": {"Qcloud\\Cos\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "yaozongyou", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "tu<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP SDK for QCloud COS", "keywords": ["cos", "php", "qcloud"], "time": "2024-11-08T09:31:12+00:00"}, {"name": "qcloud_sts/qcloud-sts-sdk", "version": "3.0.6", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/qcloud_sts/qcloud-sts-sdk/3.0.6/qcloud_sts-qcloud-sts-sdk-3.0.6.zip", "reference": "67eec72786ed5f19b2e426d61109120ccab3aa32", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-4": {"QCloud\\COSSTS\\": "src"}}, "license": ["MIT"], "authors": [{"name": "qcloudterminal", "email": "<EMAIL>"}], "description": "PHP SDK for QCloud STS", "homepage": "https://github.com/tencentyun/qcloud-cos-sts-sdk", "keywords": ["cos", "php", "qcloud", "sts"], "time": "2020-09-24T11:31:14+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/ralouphie/getallheaders/3.0.3/ralouphie-getallheaders-3.0.3.zip", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.4", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/deprecation-contracts/v2.5.4/symfony-deprecation-contracts-v2.5.4.zip", "reference": "605389f2a7e5625f273b53960dc46aeaf9c62918", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "time": "2024-09-25T14:11:13+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.31.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-mbstring/v1.31.0/symfony-polyfill-mbstring-v1.31.0.zip", "reference": "85181ba99b2345b0ef10ce42ecac37612d9fd341", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.31.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-php80/v1.31.0/symfony-polyfill-php80-v1.31.0.zip", "reference": "60328e362d4c2c802a54fcbf04f9d3fb892b4cf8", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2024-09-09T11:45:10+00:00"}, {"name": "tencentcloud/tencentcloud-sdk-php", "version": "3.0.1277", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/tencentcloud/tencentcloud-sdk-php/3.0.1277/tencentcloud-tencentcloud-sdk-php-3.0.1277.zip", "reference": "dbb9c2d04ce0b20496aa9d911416a939a56b35a8", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.3 || ^7.0", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"TencentCloud\\": "./src/TencentCloud"}, "classmap": ["src/QcloudApi/QcloudApi.php"]}, "license": ["Apache-2.0"], "authors": [{"name": "coolli", "email": "<EMAIL>", "homepage": "https://cloud.tencent.com/document/sdk/PHP", "role": "Developer"}], "description": "TencentCloudApi php sdk", "homepage": "https://github.com/TencentCloud/tencentcloud-sdk-php", "time": "2024-12-09T20:07:03+00:00"}, {"name": "topthink/framework", "version": "v6.1.5", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/topthink/framework/v6.1.5/topthink-framework-v6.1.5.zip", "reference": "57d1950a1844ef8d3098ea290032aeb92e2e32c3", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": ">=7.2.5", "psr/container": "~1.0", "psr/http-message": "^1.0", "psr/log": "~1.0", "psr/simple-cache": "^1.0", "topthink/think-helper": "^3.1.1", "topthink/think-orm": "^2.0|^3.0"}, "require-dev": {"guzzlehttp/psr7": "^2.1.0", "mikey179/vfsstream": "^1.6", "mockery/mockery": "^1.2", "phpunit/phpunit": "^7.0"}, "type": "library", "autoload": {"files": [], "psr-4": {"think\\": "src/think/"}}, "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP Framework.", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "time": "2024-04-16T02:01:19+00:00"}, {"name": "topthink/think-helper", "version": "v3.1.10", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/topthink/think-helper/v3.1.10/topthink-think-helper-v3.1.10.zip", "reference": "ac66cc0859a12cd5d73258f50f338aadc95e9b46", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Helper Package", "time": "2024-11-21T01:47:51+00:00"}, {"name": "topthink/think-image", "version": "v1.0.8", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/topthink/think-image/v1.0.8/topthink-think-image-v1.0.8.zip", "reference": "d1d748cbb2fe2f29fca6138cf96cb8b5113892f1", "shasum": ""}, "require": {"ext-gd": "*"}, "require-dev": {"phpunit/phpunit": "4.8.*", "topthink/framework": "^5.0"}, "type": "library", "autoload": {"psr-4": {"think\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP5 Image Package", "time": "2024-08-07T10:06:35+00:00"}, {"name": "topthink/think-multi-app", "version": "v1.1.1", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/topthink/think-multi-app/v1.1.1/topthink-think-multi-app-v1.1.1.zip", "reference": "f93c604d5cfac2b613756273224ee2f88e457b88", "shasum": ""}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0|^8.0"}, "type": "library", "extra": {"think": {"services": ["think\\app\\Service"]}}, "autoload": {"psr-4": {"think\\app\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp multi app support", "time": "2024-11-25T08:52:44+00:00"}, {"name": "topthink/think-orm", "version": "v2.0.62", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/topthink/think-orm/v2.0.62/topthink-think-orm-v2.0.62.zip", "reference": "e53bfea572a133039ad687077120de5521af617f", "shasum": ""}, "require": {"ext-json": "*", "ext-pdo": "*", "php": ">=7.1.0", "psr/log": "^1.0|^2.0", "psr/simple-cache": "^1.0|^2.0", "topthink/think-helper": "^3.1"}, "require-dev": {"phpunit/phpunit": "^7|^8|^9.5"}, "type": "library", "autoload": {"files": ["stubs/load_stubs.php"], "psr-4": {"think\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "think orm", "keywords": ["database", "orm"], "time": "2024-09-22T06:17:47+00:00"}, {"name": "topthink/think-template", "version": "v2.0.10", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/topthink/think-template/v2.0.10/topthink-think-template-v2.0.10.zip", "reference": "2b28c9f787c94f6c22312c9fe97dd3d926c03e1c", "shasum": ""}, "require": {"php": ">=7.1.0", "psr/simple-cache": "^1.0"}, "type": "library", "autoload": {"psr-4": {"think\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "the php template engine", "time": "2024-08-12T05:48:57+00:00"}, {"name": "topthink/think-view", "version": "v1.0.14", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/topthink/think-view/v1.0.14/topthink-think-view-v1.0.14.zip", "reference": "edce0ae2c9551ab65f9e94a222604b0dead3576d", "shasum": ""}, "require": {"php": ">=7.1.0", "topthink/think-template": "^2.0"}, "type": "library", "autoload": {"psr-4": {"think\\view\\driver\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp template driver", "time": "2019-11-06T11:40:13+00:00"}, {"name": "wechatpay/wechatpay", "version": "1.4.10", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/wechatpay/wechatpay/1.4.10/wechatpay-wechatpay-1.4.10.zip", "reference": "9c364872bd2063bf3f10efc3765e96e3e1b20e2e", "shasum": ""}, "require": {"ext-curl": "*", "ext-libxml": "*", "ext-openssl": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.5 || ^7.0", "guzzlehttp/uri-template": "^0.2 || ^1.0", "php": ">=7.1.2"}, "require-dev": {"phpstan/phpstan": "^0.12.89 || ^1.0", "phpunit/phpunit": "^7.5 || ^8.5.16 || ^9.3.5"}, "bin": ["bin/CertificateDownloader.php"], "type": "library", "autoload": {"psr-4": {"WeChatPay\\": "src/"}}, "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/TheNorthMemory"}, {"name": "WeChatPay Community", "homepage": "https://developers.weixin.qq.com/community/pay"}], "description": "[A]Sync Chainable WeChatPay v2&v3's OpenAPI SDK for PHP", "homepage": "https://pay.weixin.qq.com/", "keywords": ["AES-GCM", "aes-ecb", "openapi-chainable", "rsa-oaep", "wechatpay", "xml-builder", "xml-parser"], "time": "2024-09-19T04:22:06+00:00"}], "packages-dev": [{"name": "symfony/polyfill-php72", "version": "v1.31.0", "dist": {"type": "zip", "url": "https://mirrors.cloud.tencent.com/repository/composer/symfony/polyfill-php72/v1.31.0/symfony-polyfill-php72-v1.31.0.zip", "reference": "fa2ae56c44f03bed91a39bfc9822e31e7c5c38ce", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "metapackage", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/var-dumper", "version": "v4.4.47", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/symfony/var-dumper/v4.4.47/symfony-var-dumper-v4.4.47.zip", "reference": "1069c7a3fca74578022fab6f81643248d02f8e63", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "time": "2022-10-03T15:15:11+00:00"}, {"name": "topthink/think-trace", "version": "v1.6", "dist": {"type": "zip", "url": "https://mirrors.tencent.com/repository/composer/topthink/think-trace/v1.6/topthink-think-trace-v1.6.zip", "reference": "136cd5d97e8bdb780e4b5c1637c588ed7ca3e142", "shasum": ""}, "require": {"php": ">=7.1.0", "topthink/framework": "^6.0|^8.0"}, "type": "library", "extra": {"think": {"services": ["think\\trace\\Service"], "config": {"trace": "src/config.php"}}}, "autoload": {"psr-4": {"think\\trace\\": "src"}}, "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "thinkphp debug trace", "time": "2023-02-07T08:36:32+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.4.1", "ext-json": "*", "ext-redis": "*", "ext-openssl": "*", "ext-bcmath": "*", "ext-curl": "*", "ext-gd": "*", "ext-mbstring": "*"}, "platform-dev": [], "plugin-api-version": "2.2.0"}