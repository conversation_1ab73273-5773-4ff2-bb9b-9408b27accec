INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`,
                                  `data_state`, `create_time`, `update_time`)
VALUES ('团长收益查询', 'group_query_tool', 0, '', 0, 'user', '', 2, 0, '2023-08-10 14:04:08', '2023-08-10 14:04:13');
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '团长收益查询-列表', 'group_query_tool_list', 1, '', t.`id`, 'user', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'group_query_tool' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '通用活动列表-接口', 'GroupQueryTool.list', 2, 'GET', t.`id`, 'user', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'group_query_tool_list' AND `type` = 1) AS t;
