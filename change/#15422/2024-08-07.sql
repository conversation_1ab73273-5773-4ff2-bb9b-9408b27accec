CREATE TABLE `bwc_withdrawal_day_amount`
(
    `id`                    int(10) unsigned NOT NULL AUTO_INCREMENT,
    `day`                   date           NOT NULL COMMENT '日期',
    `withdrawal_amount`     decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '提现总金额',
    `aqf_withdrawal_amount` decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '安全发提现总金额',
    `success_amount`        decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '提现成功总金额',
    `data_state`            tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`           timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`           timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                     `idx_day` (`day`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现总金额记录表';


INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`)
VALUES ('string', '安全发平台打款开关(开1，关0)', 'aqf_withdrawal_switch', '1', '', 0);

INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`)
VALUES ('string', '安全发提现总金额上限', 'aqf_max_withdrawal_total_amount', '150000', '', 0);

