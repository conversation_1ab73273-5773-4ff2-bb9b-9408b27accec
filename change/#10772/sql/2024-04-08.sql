CREATE TABLE `bwc_promotion_group`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `name`        varchar(200) NOT NULL DEFAULT '' COMMENT '推广分组名称',
    `operate_id`  int(11) NOT NULL DEFAULT '0' COMMENT '修改人',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    `update_time` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广分组';

CREATE TABLE `bwc_promotion_group_relation`
(
    `id`                 int(11) unsigned NOT NULL AUTO_INCREMENT,
    `promotion_id`       int(11) NOT NULL DEFAULT '0' COMMENT '推广员id',
    `promotion_group_id` int(11) NOT NULL COMMENT '推广分组id',
    `create_time`        timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广员和分组的关联关系表';

ALTER TABLE `bwc_promotion` ADD COLUMN `remark` VARCHAR(255) DEFAULT NULL COMMENT '备注' AFTER `name`;

ALTER TABLE `bwc_promotion_order` ADD INDEX `idx_channel_promotion_id` (`channel_promotion_id`);

ALTER TABLE `bwc_promotion_group_relation` ADD INDEX `idx_promotion_group_relation` (`promotion_id`, `promotion_group_id`);

INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1536, '推广分组', 'promotion_grouping', 0, '', 0, 'promotion', '', 2, 0, '2023-08-10 14:04:08', '2023-08-10 14:04:13');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1531, '复制推广链接', 'promotion_copy_promotion_link', 1, '', 815, 'promotion', '', 2, 0, '2023-08-12 16:53:35', '2023-08-12 16:54:44');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1532, '查看推广二维码', 'promotion_view_promotional_qr_code', 1, '', 815, 'promotion', '', 2, 0, '2023-08-12 16:53:35', '2023-08-12 16:54:44');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1533, '推广用户数据查询', 'promotion_data_query', 1, '', 815, 'promotion', '', 2, 0, '2023-08-12 16:53:35', '2023-08-12 16:54:44');

INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1534, '查看推广二维码-接口', 'promotion.qr_code', 2, 'GET', 1532, 'promotion', '', 2, 0, '2023-08-12 16:54:22', '2023-08-12 17:01:17');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1535, '推广用户数据查询-接口', 'promotion.dashboard', 2, 'GET', 1533, 'promotion', '', 2, 0, '2023-08-12 16:54:22', '2023-08-12 17:01:17');

INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1537, '新建分组', 'promotion_grouping_create', 1, '', 1536, 'promotion', '', 2, 0, '2023-08-12 16:53:35', '2023-08-12 16:54:44');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1538, '编辑分组', 'promotion_grouping_edit', 1, '', 1536, 'promotion', '', 2, 0, '2023-08-12 16:53:35', '2023-08-12 16:54:44');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1539, '查询数据', 'promotion_grouping_query', 1, '', 1536, 'promotion', '', 2, 0, '2023-08-12 16:53:35', '2023-08-12 16:54:44');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1540, '导出数据', 'promotion_grouping_export', 1, '', 1536, 'promotion', '', 2, 0, '2023-08-12 16:53:35', '2023-08-12 16:54:44');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1541, '删除分组', 'promotion_grouping_delete', 1, '', 1536, 'promotion', '', 2, 0, '2023-08-12 16:53:35', '2023-08-12 16:54:44');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1542, '分组列表', 'promotion_grouping_list', 1, '', 1536, 'promotion', '', 2, 0, '2023-08-12 16:53:35', '2023-08-12 16:54:44');

INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1543, '新建分组-接口', 'PromotionGroup.create', 2, 'POST', 1537, 'promotion', '', 2, 0, '2023-08-12 16:54:22', '2023-08-12 17:01:17');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1544, '分组详情-接口', 'PromotionGroup.show', 2, 'GET', 1538, 'promotion', '', 2, 0, '2023-08-12 16:54:22', '2023-08-12 17:01:17');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1545, '分组编辑-接口', 'PromotionGroup.update', 2, 'POST', 1538, 'promotion', '', 2, 0, '2023-08-12 16:54:22', '2023-08-12 17:01:17');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1546, '导出数据-接口', 'PromotionGroup.promotion_data_export', 2, 'POST', 1540, 'promotion', '', 2, 0, '2023-08-12 16:54:22', '2023-08-12 17:01:17');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1547, '查询数据-接口', 'PromotionGroup.promotion_data', 2, 'GET', 1539, 'promotion', '', 2, 0, '2023-08-12 16:54:22', '2023-08-12 17:01:17');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1548, '删除分组-接口', 'PromotionGroup.destroy', 2, 'POST', 1541, 'promotion', '', 2, 0, '2023-08-12 16:54:22', '2023-08-12 17:01:17');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1549, '分组列表-接口', 'PromotionGroup.list', 2, 'GET', 1542, 'promotion', '', 2, 0, '2023-08-12 16:54:22', '2023-08-12 17:01:17');
