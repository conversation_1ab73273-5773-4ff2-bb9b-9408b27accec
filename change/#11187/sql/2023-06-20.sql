INSERT INTO `bwc_coupon_template` (`id`, `coupon_name`, `coupon_type`, `coupon_desc`, `create_by`, `update_by`, `gmt_created`, `gmt_modified`, `is_delete`, `advance_time`, `use_desc`) VALUES (NULL, '超级返利券', 6, '用券享{super_rebate_rate}%额外多返', 0, 0, '2024-06-14 09:56:01', '2024-06-20 11:10:47', 0, 0, '自营活动可用券叠加原活动优惠{super_rebate_rate}%返利，最多{super_rebate_max_amount}元');
INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`) VALUES (NULL, 'string', '超级返利券返利额度(%)', 'super_rebate_rate', '10', '', 0);
INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`) VALUES (NULL, 'string', '超级返利券返利上限金额(元)', 'super_rebate_max_amount', '2', '', 0);

INSERT INTO `bwc_energy_goods` (`id`, `name`, `brief_desc`, `cover`, `type`, `price`, `set_stock_quantity`, `stock_quantity`, `stock_type`, `tips`, `weight`, `status`, `sales_volume`, `attach`, `created_by`, `updated_by`, `data_state`, `create_time`, `update_time`, `current_sales_volume`) VALUES (NULL, '超级返利券', '用券享{super_rebate_rate}%额外返', 'https://image.xiaoxiaoyouxuan.com/admin/image_20240620161143d7bea55ba3488868.png', 1, 5000.00, 100, 100, 1, '', 0.00, 1, 0, '{\"coupon_id\": 5}', 0, 0, 0, '2024-06-20 16:11:50', '2024-06-20 16:11:50', 0);
INSERT INTO `bwc_version_compatible` (`id`, `version_name`, `min_version`, `version_desc`, `data_state`, `create_time`, `update_time`) VALUES (NULL, 'super_rebate_coupon_v', 20310, '超级返利券版本', 0, '2024-06-18 17:11:57', '2024-06-18 17:11:57');

CREATE TABLE `bwc_order_super_rebate_info` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL COMMENT '用户id，bwc_user.id',
    `order_id` int(11) NOT NULL COMMENT '订单id，bwc_order.id',
    `user_coupon_id` int(11) NOT NULL COMMENT '卡券id，bwc_user_coupon.id',
    `super_rebate_amount` decimal(10,2) NOT NULL COMMENT '超级返利金额',
    `super_rebate_rate` int(11) NOT NULL COMMENT '超级返利券返利额度(%)',
    `super_rebate_max_amount` decimal(10,2) NOT NULL COMMENT '超级返利券返利上限金额(元)',
    `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_order_coupon` (`order_id`,`user_coupon_id`),
    KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


INSERT INTO bwc_vip_gift (vip_level_id, max_vip_level_id, min_vip_level_id, gift_num, gift_type, gift_name, coupon_template_id, red_amount)
SELECT 3, 3, 3, 1, 1, t.coupon_name, t.id, 0.00
FROM (SELECT `id`,coupon_name FROM `bwc_coupon_template` WHERE `coupon_type` = 6) AS t;

INSERT INTO bwc_vip_gift (vip_level_id, max_vip_level_id, min_vip_level_id, gift_num, gift_type, gift_name, coupon_template_id, red_amount)
SELECT 4, 4, 4, 2, 1, t.coupon_name, t.id, 0.00
FROM (SELECT `id`,coupon_name FROM `bwc_coupon_template` WHERE `coupon_type` = 6) AS t;

INSERT INTO bwc_vip_gift (vip_level_id, max_vip_level_id, min_vip_level_id, gift_num, gift_type, gift_name, coupon_template_id, red_amount)
SELECT 5, 5, 5, 4, 1, t.coupon_name, t.id, 0.00
FROM (SELECT `id`,coupon_name FROM `bwc_coupon_template` WHERE `coupon_type` = 6) AS t;

INSERT INTO bwc_vip_gift (vip_level_id, max_vip_level_id, min_vip_level_id, gift_num, gift_type, gift_name, coupon_template_id, red_amount)
SELECT 6, 6, 6, 6, 1, t.coupon_name, t.id, 0.00
FROM (SELECT `id`,coupon_name FROM `bwc_coupon_template` WHERE `coupon_type` = 6) AS t;

INSERT INTO bwc_vip_gift (vip_level_id, max_vip_level_id, min_vip_level_id, gift_num, gift_type, gift_name, coupon_template_id, red_amount)
SELECT 7, 7, 7, 8, 1, t.coupon_name, t.id, 0.00
FROM (SELECT `id`,coupon_name FROM `bwc_coupon_template` WHERE `coupon_type` = 6) AS t;

INSERT INTO bwc_vip_gift (vip_level_id, max_vip_level_id, min_vip_level_id, gift_num, gift_type, gift_name, coupon_template_id, red_amount)
SELECT 8, 8, 8, 10, 1, t.coupon_name, t.id, 0.00
FROM (SELECT `id`,coupon_name FROM `bwc_coupon_template` WHERE `coupon_type` = 6) AS t;

INSERT INTO bwc_vip_gift (vip_level_id, max_vip_level_id, min_vip_level_id, gift_num, gift_type, gift_name, coupon_template_id, red_amount)
SELECT 9, 9, 9, 12, 1, t.coupon_name, t.id, 0.00
FROM (SELECT `id`,coupon_name FROM `bwc_coupon_template` WHERE `coupon_type` = 6) AS t;

INSERT INTO bwc_vip_gift (vip_level_id, max_vip_level_id, min_vip_level_id, gift_num, gift_type, gift_name, coupon_template_id, red_amount)
SELECT 10, 10, 10, 14, 1, t.coupon_name, t.id, 0.00
FROM (SELECT `id`,coupon_name FROM `bwc_coupon_template` WHERE `coupon_type` = 6) AS t;

