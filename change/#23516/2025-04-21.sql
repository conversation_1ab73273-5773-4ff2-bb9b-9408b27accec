INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`)
    (select '禁用记录', 'get_disable_record', 1, '', t.id, 'user', '', 2 from bwc_authority_temp as t where t.key= 'user' and type=0);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`)
    (select '记录接口', 'user.get_user_operation_record', 2, 'POST', t.id, 'business', '', 2 from bwc_authority_temp as t where t.key= 'get_disable_record' and type=1);

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`)
    (select '支付宝拉黑记录', 'get_alipay_black_record', 1, '', t.id, 'user', '', 2 from bwc_authority_temp as t where t.key= 'user' and type=0);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`)
    (select '记录接口', 'user.get_user_operation_record', 2, 'POST', t.id, 'business', '', 2 from bwc_authority_temp as t where t.key= 'get_alipay_black_record' and type=1);

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`)
    (select '微信拉黑记录', 'get_wechat_black_record', 1, '', t.id, 'user', '', 2 from bwc_authority_temp as t where t.key= 'user' and type=0);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`)
    (select '记录接口', 'user.get_user_operation_record', 2, 'POST', t.id, 'business', '', 2 from bwc_authority_temp as t where t.key= 'get_wechat_black_record' and type=1);

delete from bwc_authority_temp where `key`= 'user.get_user_operation_record' and type=2;
delete from bwc_authority_temp where `key`= 'get_disable_record' and type=1;
delete from bwc_authority_temp where `key`= 'get_alipay_black_record' and type=1;
delete from bwc_authority_temp where `key`= 'get_wechat_black_record' and type=1;
