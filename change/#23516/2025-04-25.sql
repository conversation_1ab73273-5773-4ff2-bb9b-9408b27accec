INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`)
    (select '禁用记录', 'get_disable_record', 1, '', t.id, 'user', '', 2 from bwc_authority_temp as t where t.key= 'user' and type=0);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`)
    (select '记录接口', 'user.get_user_disable_record', 2, 'POST', t.id, 'business', '', 2 from bwc_authority_temp as t where t.key= 'get_disable_record' and type=1);

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`)
    (select '支付宝拉黑记录', 'get_black_record', 1, '', t.id, 'user', '', 2 from bwc_authority_temp as t where t.key= 'user' and type=0);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`)
    (select '记录接口', 'user.get_user_blacklist_record', 2, 'POST', t.id, 'business', '', 2 from bwc_authority_temp as t where t.key= 'get_black_record' and type=1);

CREATE TABLE `bwc_user_blacklist_record`
(
    `id`          int(10) NOT NULL AUTO_INCREMENT,
    `admin_id`    int(10) NOT NULL DEFAULT '0' COMMENT '操作员对应的admin表id',
    `user_id`     int(10) NOT NULL DEFAULT '0' COMMENT '后台被操作人id',
    `type`        tinyint(2) NOT NULL DEFAULT '0' COMMENT '操作类型：1拉黑支付宝，2解封支付宝，3拉黑微信，4解封微信',
    `reason`      varchar(300) NOT NULL DEFAULT '' COMMENT '操作信息',
    `data`        json         NOT NULL COMMENT '数据json',
    `create_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_admin_id` (`admin_id`) USING BTREE,
    KEY           `idx_user_id` (`user_id`) USING BTREE,
    KEY           `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户相关拉黑日志表';