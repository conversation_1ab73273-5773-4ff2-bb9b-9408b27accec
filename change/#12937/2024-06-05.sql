CREATE TABLE `bwc_store_sale_limit`
(
    `id`                int(10) unsigned NOT NULL AUTO_INCREMENT,
    `store_id`          int(10) NOT NULL DEFAULT '0' COMMENT '店铺id',
    `sale_id`           int(10) NOT NULL DEFAULT '0' COMMENT '销售id',
    `limit_day`         int(10) NOT NULL DEFAULT '0' COMMENT '未结算天数限制',
    `limit_money`       decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '未结算金额限制',
    `create_operate_id` int(10) NOT NULL DEFAULT '0' COMMENT '后台创建数据的admin_id',
    `delete_operate_id` int(10) NOT NULL DEFAULT '0' COMMENT '后台删除数据的admin_id',
    `data_state`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`       timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`       timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                 `idx_store_id` (`store_id`,`data_state`) USING BTREE,
    KEY                 `idx_sale_id` (`sale_id`,`data_state`) USING BTREE,
    KEY                 `idx_create_time` (`create_time`,`data_state`) USING BTREE,
    KEY                 `idx_create_operate_id` (`create_operate_id`) USING BTREE,
    KEY                 `idx_delete_operate_id` (`delete_operate_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺/销售发布任务的时候的限制';

ALTER TABLE `bwc_task_cycle_create`
DROP INDEX `idx_task_end_date`,
ADD INDEX `idx_task_end_date`(`task_end_date`, `task_end_time`) USING BTREE,
ADD INDEX `idx_task_start_date`(`task_start_date`, `task_start_time`) USING BTREE;

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES ('未结算额度配置', 'store_limit_config', 1, '', 105, 'business', '', 2, 0, '2024-06-05 15:07:14', '2024-06-05 15:07:14');
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) (select '未结算额度配置', 'store.limit_config_detail', 2, 'POST', t.id, 'business', '', 2, 0, '2024-06-05 15:07:35', '2024-06-05 15:08:21' from bwc_authority_temp as t where t.key= 'store_limit_config' and type=1);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) (select '未结算额度配置', 'store.limit_config', 2, 'POST', t.id, 'business', '', 2, 0, '2024-06-05 15:08:05', '2024-06-05 15:08:28' from bwc_authority_temp as t where t.key= 'store_limit_config' and type=1);

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES ('批量额度配置', 'batch_store_limit_config', 1, '', 105, 'business', '', 2, 0, '2024-06-05 15:07:14', '2024-06-05 15:07:14');
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) (select '批量额度配置', 'store.batch_limit_config', 2, 'POST', t.id, 'business', '', 2, 0, '2024-06-05 15:07:35', '2024-06-05 15:08:21' from bwc_authority_temp as t where t.key= 'batch_store_limit_config' and type=1);
