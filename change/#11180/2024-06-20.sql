CREATE TABLE `bwc_user_score`
(
    `id`            int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id`       int(10) NOT NULL COMMENT '用户id',
    `not_yun_score` decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '非云账户积分总额',
    `yun_score`     decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '云账户积分总额',
    `data_state`    tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`   timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`   timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY             `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '拆分积分提现是否启用云账户', 'split_score_yun_open', '1', '', 0);
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '拆分积分云账户提现最小版本号', 'split_score_yun_min_version', '20240', '', 0);
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '走云账户积分包含的活动类型:美团官方活动', 'split_score_yun_task_mtg', 'mtg', 'split_score_yun_task', 0);
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '走云账户积分包含的活动类型:饿了么官方活动', 'split_score_yun_task_eleg', 'eleg', 'split_score_yun_task', 0);
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '走云账户积分包含的活动类型:饿了么专享活动', 'split_score_yun_task_elez', 'elez', 'split_score_yun_task', 0);
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '云账户拆分积分报名的提示文案', 'split_score_yun_sign_up_tip', '根据《非银行支付机构网络支付业务管理办法》等国家相关法律法规对保护个人资金安全的需求，{highlightStart}提现时需提供有效身份信息后方可提现{highlightEnd}，感谢您的理解和配合！', '', 0);

CREATE TABLE `bwc_withdrawal_version`
(
    `id`            int(11) unsigned NOT NULL AUTO_INCREMENT,
    `withdrawal_id` int(11) NOT NULL COMMENT '提现记录表id',
    `version`       int(11) NOT NULL DEFAULT '0' COMMENT '提现时的版本号',
    `is_yun_open`   tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否打款到云账户积分：1是，0不是（默认）',
    `yun_score`     decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '云账户扣除积分',
    `not_yun_score` decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '非云账户扣出积分',
    `data_state`    tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`   timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`   timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY             `idx_withdrawal_id` (`withdrawal_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;