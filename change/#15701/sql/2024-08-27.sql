-- 修改agent表
ALTER TABLE `bwc_agent` ADD COLUMN `htl_subsidy_amount` DECIMAL ( 10, 2 ) NOT NULL DEFAULT 0 COMMENT '灰太狼活动补贴金额' AFTER `htl_activity_enabled`;

-- 默认补贴金额
UPDATE `bwc_agent`
SET `htl_subsidy_amount` = 1
WHERE
        `id` = 28
   OR `id` = 52;

-- 订单补贴表
CREATE TABLE `bwc_order_subsidy` (
                                     `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                     `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单id',
                                     `subsidy_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单补贴金额',
                                     `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                     `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                     PRIMARY KEY (`id`),
                                     KEY `idx_order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;