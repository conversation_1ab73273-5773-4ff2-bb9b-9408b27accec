CREATE TABLE `bwc_user_close_yun`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `user_id`     int(11) NOT NULL COMMENT '用户表',
    `is_close`    tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否关闭个人云账户验证：默认开启0，失败1',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：默认0，删除1',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '结束时间',
    PRIMARY KEY (`id`),
    KEY           `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户是否单独关闭云账户验证表';

insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '开启/关闭云账户实名', 'user_is_close_yun',1, '', t.id, 'user', '开启/关闭云账户实名组件', 2 from bwc_authority_temp as t where t.`key`='user' and type=0);

insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '开启/关闭云账户实名', 'user.user_is_close_yun',2, 'POST', t.id, 'user', '开启/关闭云账户实名接口', 2 from bwc_authority_temp as t where t.`key`='user_is_close_yun' and type=1);
