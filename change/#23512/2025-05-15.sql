-- 页面权限
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
values('店铺变更销售记录', 'store_change_promoter_record', 0, '', 0, 'log', '店铺变更销售记录页面', 2);
-- 列表
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled) (
    select '列表', 'store_change_promoter_record_list', 1, '', t.id, 'log', '店铺变更销售记录列表组件', 2 from bwc_authority_temp as t where `key`='store_change_promoter_record' and type=0
);
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled) (
    select '列表', 'StoreChangePromoterRecord.list', 2, 'GET', t.id, 'log', '店铺变更销售记录列表接口', 2 from bwc_authority_temp as t where `key`='store_change_promoter_record_list' and type=1
);
-- 查询总数
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled) (
    select '查询总数', 'store_change_promoter_record_count', 1, '', t.id, 'log', '店铺变更销售记录查询总数组件', 2 from bwc_authority_temp as t where `key`='store_change_promoter_record' and type=0
);
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled) (
    select '查询总数', 'StoreChangePromoterRecord.count', 2, 'GET', t.id, 'log', '店铺变更销售记录查询总数接口', 2 from bwc_authority_temp as t where `key`='store_change_promoter_record_count' and type=1
);

CREATE TABLE `bwc_store_change_promoter_record`
(
    `id`                 int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `store_id`           int(11) unsigned NOT NULL DEFAULT '0' COMMENT '店铺id',
    `type`               tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '类型：1修改店铺，2批量修改店铺，3一键移交店铺',
    `admin_id`           int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作员id',
    `before_promoter_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改前的销售',
    `after_promoter_id`  int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改后的销售',
    `operation_ip`       varchar(20) NOT NULL DEFAULT '' COMMENT '操作ip',
    `create_time`        timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                  `idx_store_id` (`store_id`,`type`) USING BTREE,
    KEY                  `idx_admin_id` (`admin_id`) USING BTREE,
    KEY                  `idx_create_time` (`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;