CREATE TABLE `bwc_user_unbind_wechat`
(
    `id`                  int(11) unsigned NOT NULL AUTO_INCREMENT,
    `user_id`             int(11) NOT NULL DEFAULT '0' COMMENT 'bwc_user表id',
    `user_unionid`        varchar(255) NOT NULL DEFAULT '' COMMENT '微信unionid',
    `user_device_cid`     varchar(60)  NOT NULL DEFAULT '' COMMENT '设备号',
    `user_alipay_account` varchar(255) NOT NULL DEFAULT '' COMMENT '支付宝账号',
    `identity_card_id`    int(11) NOT NULL DEFAULT '0' COMMENT 'bwc_identity_card表id',
    `user_redundancy`     json         NOT NULL COMMENT 'User表冗余信息',
    `create_time`         timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;