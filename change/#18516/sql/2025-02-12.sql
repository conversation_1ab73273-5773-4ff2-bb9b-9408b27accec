INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`)
VALUES (NULL, 6, '成人用品', 0, '2025-02-12 14:32:28', '2025-02-12 14:32:28');

UPDATE `bwc_config`
SET `value` = '你是一个店铺标签评选助手，请根据店铺名称来给出符合其主营业务的标签：\n【标签列表】\n包子粥铺、饺子馄饨、饭团早点、快餐便当、汉堡薯条、意面披萨、地方菜系、能量西餐、夹馍饼类、日韩料理、香锅干锅、异国料理、火锅冒菜、米粉面馆、轻食沙拉、东南亚菜、烧烤夜宵、特色小吃、炸鸡炸串、鸭脖卤味、奶茶果汁、咖啡、面包蛋糕、冰凉甜品、生日蛋糕、水果捞、中西糕点、水果果切、鲜花礼品、超市便利、生鲜菜市场、宠物用品、看病买药、成人用品\n【店铺名称】\n{shop}\n【要求】\n1.输出内容用顿号隔开\n2.不要冗余的解释'
WHERE `key` = 'baidu_prompt_template' LIMIT 1;

CREATE TABLE `bwc_user_category_tag_disable`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id`         int(11) unsigned NOT NULL COMMENT '用户id',
    `category_tag_id` int(11) unsigned NOT NULL COMMENT '品类标签id:bwc_category_tag.id',
    `create_by`       int(11) NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_by`       int(11) NOT NULL DEFAULT '0' COMMENT '修改人',
    `create_time`     timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `data_state`      tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
    PRIMARY KEY (`id`),
    KEY               `idx_category_tag_id` (`category_tag_id`),
    KEY               `idx_store_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='用户与标签屏蔽表';

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '解除隐藏成人用品店铺', 'remove_mute_adult_goods', 1, '', t.`id`, 'user', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'user' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '解除隐藏成人用品店铺-接口', 'User.remove_mute_adult_goods', 2, 'POST', t.`id`, 'user', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'remove_mute_adult_goods' AND `type` = 1) AS t;

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '隐藏成人用品店铺', 'add_mute_adult_goods', 1, '', t.`id`, 'user', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'user' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '隐藏成人用品店铺-接口', 'User.add_mute_adult_goods', 2, 'POST', t.`id`, 'user', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'add_mute_adult_goods' AND `type` = 1) AS t;

