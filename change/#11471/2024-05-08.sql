ALTER TABLE `bwc_seller`
    ADD COLUMN `alipay_name` varchar(50) NULL DEFAULT '' COMMENT '支付宝姓名' AFTER `status`,
ADD COLUMN `alipay_account` varchar(50) NULL DEFAULT '' COMMENT '支付宝账号' AFTER `alipay_name`;

INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1625, '查看支付宝姓名', 'seller_alipay_name', 1, '', 1, 'business', '查看支付宝姓名组件', 2, 0, '2024-05-08 16:38:30', '2024-05-08 16:38:46');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1626, '查看支付宝姓名', 'seller.get_alipay_name', 2, 'POST', 1625, 'business', '查看支付宝姓名接口', 2, 0, '2024-05-08 16:39:19', '2024-05-08 16:39:39');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1627, '查看支付宝账号', 'seller_alipay_account', 1, '', 1, 'business', '查看支付宝账号组件', 2, 0, '2024-05-08 16:39:50', '2024-05-08 16:40:37');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1628, '查看支付宝账号', 'seller.get_alipay_account', 2, 'POST', 1627, 'business', '查看支付宝账号接口', 2, 0, '2024-05-08 16:40:02', '2024-05-08 16:40:33');

ALTER TABLE `bwc_ticket`
    ADD COLUMN `alipay_name` varchar(50) NULL DEFAULT '' COMMENT '商家支付宝姓名' AFTER `refund_amount`,
ADD COLUMN `alipay_account` varchar(50) NULL DEFAULT '' COMMENT '商家支付宝账号' AFTER `alipay_name`;

INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1634, '工单详情-查看支付宝姓名', 'ticket_get_alipay_name', 1, '', 1468, 'ticket', '', 2, 0, '2024-05-09 11:28:55', '2024-05-09 11:30:32');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1635, '工单详情-查看支付宝姓名', 'Ticket.get_alipay_name', 2, 'POST', 1634, 'ticket', '', 2, 0, '2024-05-09 11:29:02', '2024-05-09 11:30:52');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1636, '工单详情-查看支付宝账号', 'ticket_get_alipay_account', 1, '', 1468, 'ticket', '', 2, 0, '2024-05-09 11:29:14', '2024-05-09 11:30:42');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1637, '工单详情-查看支付宝账号', 'Ticket.get_alipay_account', 2, 'POST', 1636, 'ticket', '', 2, 0, '2024-05-09 11:29:18', '2024-05-09 11:31:04');

INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1638, '商家退款', 'ticket_refund_amount', 1, '', 1468, 'ticket', '', 2, 0, '2024-05-09 11:37:14', '2024-05-09 11:38:22');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1639, '商家退款', 'Ticket.refund_amount', 2, 'POST', 1638, 'ticket', '', 2, 0, '2024-05-09 11:37:20', '2024-05-09 11:38:32');

ALTER TABLE `bwc_ticket_tracking`
    ADD COLUMN `alipay_name` varchar(50) NULL DEFAULT '' COMMENT '商家支付宝姓名' AFTER `refund_amount`,
ADD COLUMN `alipay_account` varchar(50) NULL DEFAULT '' COMMENT '商家支付宝账号' AFTER `alipay_name`;

ALTER TABLE `bwc_ticket_tracking`
    MODIFY COLUMN `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '提交类型：1提单人，2受理人，3系统信息，4修改工单，5工单提醒补充，6工单已退款' AFTER `ticket_id`;

ALTER TABLE `bwc_ticket_tracking`
    MODIFY COLUMN `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '提交类型：1提单人，2受理人，3系统信息，4修改工单，5工单提醒补充，6工单退款' AFTER `ticket_id`,
    ADD COLUMN `refund_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '退款状态：1退款中，2退款成功，3退款失败' AFTER `refund_amount`;

CREATE TABLE `bwc_ticket_refund_amount`
(
    `id`                 int(11) unsigned NOT NULL AUTO_INCREMENT,
    `ticket_id`          int(11) unsigned NOT NULL DEFAULT '0' COMMENT '工单id',
    `ticket_tracking_id` int(11) NOT NULL COMMENT '工单跟进信息表id',
    `seller_id`          int(11) NOT NULL DEFAULT '0' COMMENT '商家id',
    `seller_name`        varchar(50)    NOT NULL DEFAULT '' COMMENT '商家名称',
    `store_id`           int(11) NOT NULL DEFAULT '0' COMMENT '店铺id',
    `store_name`         varchar(80)    NOT NULL DEFAULT '' COMMENT '店铺名称',
    `task_id`            int(11) NOT NULL DEFAULT '0' COMMENT '任务id',
    `task_time`          timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '任务活动时间',
    `order_id`           int(11) NOT NULL DEFAULT '0' COMMENT '订单id',
    `agent_id`           int(11) NOT NULL COMMENT '代理城市id',
    `refund_amount`      decimal(10, 2) NOT NULL COMMENT '退款金额',
    `alipay_name`        varchar(50)    NOT NULL DEFAULT '' COMMENT '支付宝姓名',
    `alipay_account`     varchar(80)    NOT NULL DEFAULT '' COMMENT '支付宝账号',
    `pay_status`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '打款状态：1打款中，2打款成功，3打款失败',
    `pay_desc`           text COMMENT '打款描述',
    `pay_no`             varchar(80)    NOT NULL DEFAULT '' COMMENT '打款流水号',
    `pay_time`           timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '支付时间',
    `operator_id`        int(11) NOT NULL DEFAULT '0' COMMENT '操作人id',
    `data_state`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`        timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`        timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单退款表';

INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1640, '商家退款记录', 'ticket_refund_list', 0, '', 0, 'finance', '', 2, 0, '2024-05-10 09:45:24', '2024-05-10 09:52:41');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1641, '列表', 'ticket_refund_list', 1, '', 1640, 'finance', '', 2, 0, '2024-05-10 09:46:25', '2024-05-10 09:52:41');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1642, '列表', 'TicketRefund.list', 2, 'GET', 1641, 'finance', '', 2, 0, '2024-05-10 09:48:27', '2024-05-10 09:52:41');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1643, 'index', 'TicketRefund.index', 2, 'GET', 1641, 'finance', '', 2, 0, '2024-05-10 09:49:10', '2024-05-10 09:52:41');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1644, '查看总数', 'ticket_refund_count', 1, '', 1640, 'finance', '', 2, 0, '2024-05-10 09:49:31', '2024-05-10 09:52:41');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1645, '查看总数', 'TicketRefund.couunt', 2, 'GET', 1644, 'finance', '', 2, 0, '2024-05-10 09:49:57', '2024-05-10 09:52:41');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1646, '查看总金额', 'ticket_refund_total', 1, '', 1640, 'finance', '', 2, 0, '2024-05-10 09:50:11', '2024-05-10 09:52:41');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1647, '查看总金额', 'TicketRefund.total', 2, 'GET', 1646, 'finance', '', 2, 0, '2024-05-10 09:50:31', '2024-05-10 09:52:41');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1648, '导出表格', 'ticket_refund_excel', 1, '', 1640, 'finance', '', 2, 0, '2024-05-10 09:50:53', '2024-05-10 09:52:41');

ALTER TABLE `bwc_ticket_tracking`
    ADD COLUMN `refund_desc` text NULL COMMENT '退款失败原因' AFTER `alipay_account`;
