INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '商家端顶部滚动的内容', 'seller_top_roll_content', '晓晓惠生活平台发布的的活动营销均为真实用户、真实下单、真实反馈', '', 0);

update bwc_config set `value`='["https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240718152711549b9c20cc981889.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240718152802b5f1ae3bca602728.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240718152820e62dd52a0d761392.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_202407181528363dbaf78d67587742.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240718152853dd6ab1a7e4141210.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240726114356ed0f368559285796.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_202407181529264a1e7f4615145483.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240718152941962bbc492b210892.jpg"]' where `key` = 'newcomer_orientation_how';
update bwc_config set `value`='["https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240718154014e288fedaec863385.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240718154040127d615740646061.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_202407181541019394bc374c208541.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240718154116bf5fac0aed515367.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_2024071815413126e5cbdb6f458147.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240718154146d5abfba9fc259336.jpg"]' where `key` = 'newcomer_orientation_question';

update bwc_config set `value`='https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_202407181614096f5a71b9c3787354.png' where `key` = 'promotion_poster_background_picture';
update bwc_config set `value`='https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_2024071816142822a24a58f2581124.png' where `key` = 'promotion_poster_background_picture4';
update bwc_config set `value`='https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240718161451737dd18684715464.png' where `key` = 'promotion_poster_background_picture2';

update bwc_config set `value` = '{"app":"https://image.xiaoxiaoyouxuan.com/admin/image_20240719142640be3fc1c40e931477.png","h5":"https://image.xiaoxiaoyouxuan.com/admin/image_2024071914282472cd81508e657848.png"}' where `key` = 'task_share_bg_img';

update bwc_config set `value`='https://image.xiaoxiaoyouxuan.com/admin/image_2024072310184792f223b791909548.png' where `key`='score_or_gold_default_image';

#兼容历史数据：更新循环发布任务
UPDATE bwc_task_cycle_create SET is_praise=3 WHERE task_end_date> '2024-08-02' AND data_state=0 AND is_praise=1 AND praise_demand like '%0,%';
#兼容历史数据：更新订单表字段
UPDATE bwc_order AS o LEFT JOIN bwc_task AS t ON t.id=o.task_id SET o.is_praise=3 WHERE t.task_start_time>='2024-07-26 00:00:00' AND t.is_praise=1 AND t.praise_demand like '%0,%';
#兼容历史数据：更新任务表字段
update bwc_task set is_praise =3 where task_start_time >= '2024-07-26 00:00:00' and is_praise=1 and praise_demand like '%0,%';
