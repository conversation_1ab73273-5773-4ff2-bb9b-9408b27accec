-- 增加美团团购账单表
CREATE TABLE `bwc_order_mt_gp_bill` (
                                        `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                        `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
                                        `cash_back_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '类型 1核销返; 2反馈返',
                                        `real_charge` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实付金额',
                                        `amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '返现金额',
                                        `agent_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '代理收入',
                                        `bill_id` int(11) NOT NULL DEFAULT '0' COMMENT '账单id',
                                        `is_settlement` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '1已结算，其他未结算',
                                        `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
                                        `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_order_id` (`order_id`) USING BTREE,
                                        KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 增加excel导出类型
ALTER TABLE `bwc_excel_download_task`
    MODIFY COLUMN `excel_type` TINYINT ( 1 ) UNSIGNED NOT NULL DEFAULT 0 COMMENT '1.全部订单,2.未结算订单,3.商家账单批量导出excel,4.商家账单导出excel,5.发账单导出excel,6.发店铺账单导出excel,7.美团霸王餐账单导出excel,8.导出吃吃龟格式的excel,9.导出吃吃龟未结算账单,10.导出吃吃龟多个商家excel的zip,11.任务excel,13.订单明细导出,14.代理数据统计导出,15.提成结算导出,16.数据统计导出,17.提现账号excel,18.订单审核记录excel,19.修改账单记录excel,20.任务修改记录excel,21.工单中心-工单列表-工单明细导出,22.工单中心-明细统计-工作量明细导出,23.饿了么账单excel,24.推广-推广分组-查询数据-推广数据导出,25.财务-商家退款记录导出,26.饿了么专享账单excel,27.商务-线索管理-列表数据导出,28.灰太狼账单excel,29.店铺开票支付记录,30.标记店铺每日账单导出,31.批量订单行为导出， 32：商家端招商加盟，33. 美团对账单, 34:美团官方团购对账单, 35:美团官方团购账单导出Excel' AFTER `task_finish_time`;

INSERT INTO `bwc_mt_config`(`name`, `key`, `value`, `data_state`, `create_time`, `update_time`) VALUES ('美团团购获取对账单接口', 'mtg_gp_load_order_url', 'https://offsiteact.st.meituan.com/act/cpsapi/load_order', 0, '2025-02-11 14:15:51', '2025-02-12 17:57:00');


