INSERT INTO `bwc_config`
(`id`, `type`, `name`, `key`, `value`, `group`, `platform`, `create_time`, `update_time`)
VALUES
    (NULL, 'string', '老用户是否可参与复购卡活动1:是0:否', 'repurchase_card_old_user_enabled', '1', '', 0, NOW(), NOW());

ALTER TABLE `bwc_user_register_source`
    MODIFY COLUMN `register_source` varchar(50) NOT NULL COMMENT '注册来源，例如: generalActivity通用活动，repurchaseCardActivity复购卡活动（新）\r\nleaderInvite#wechatFriend 暗号海报微信好友\r\nleaderInvite#wechatMoments 暗号海报微信朋友圈\r\nleaderInvite#xiaohongshu暗号海报小红书\r\nleaderInvite#douyin暗号海报抖音\r\nleaderInvite#weibo暗号海报微博\r\nleaderInvite#mp-weixin暗号海报微信小程序或h5\r\ncityChallenge#wechatFriend 暗号城市挑战赛微信好友\r\ncityChallenge#wechatMoments 暗号城市挑战赛微信朋友圈\r\ncityChallenge#xiaohongshu暗号城市挑战赛小红书\r\ncityChallenge#douyin暗号城市挑战赛抖音\r\ncityChallenge#weibo暗号城市挑战赛微博\r\ncityChallenge#mp-weixin暗号城市挑战赛微信小程序或h5\r\noldRepurchaseCardActivity复购卡活动（老）';

ALTER TABLE `bwc_agent_billing_record`
    MODIFY COLUMN `type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '类型1充值，2短信通知扣费，3短信验证码扣费，4用户返现扣费，5邀请奖励扣费，6机审接口付费，7每单服务费，8中间人打款，9管理员扣款，10渠道推广分佣，11渠道推广退款，12商家支付打款给代理，13代理提现，14代理提现驳回，15APP推送通知，16ai外呼，17全区域分成扣费，18全区域分成收入，19活动完成-下单返利，20活动完成-邀请返利，21订单取消扣除积分，22订单扣除积分，23订单扣除金币，24订单取消扣除团员奖励，25订单取消扣除下单返利活动奖励，26订单取消扣除邀请返利活动奖励，27订单增加积分，28订单增加金币，29订单恢复增加积分，30订单恢复增加团员奖励，31订单恢复增加下单返利活动奖励，32订单恢复增加邀请返利活动奖励，33营销推送，34商家退款-扣费，35运营短信，36自动任务-APP推送，37自动任务-短信，38渠道推广分佣（上级），39渠道推广退款（上级），40评价卡返现支出-线上，41评价卡市场部拉新提成-扣费，42评价卡商家佣金支出-线上，43新人通用大牌返现支出，44订单助力加返-增加用户积分，45评价卡经纪人支出-线上，46订单助力加返-扣除用户积分，47助力领现金现金任务-扣除代理，48助力领现金-可提现金币-扣除代理，49经纪人-扣费，50手动增加中间人余额-扣除代理，51砍价捡漏商户端验证码扣费，52到店砍价-技术服务费-扣费，53到店砍价-开户手续费-扣费，54到店砍价-城市互通-扣费，55到店砍价-城市互通-收入，56到店砍价-用户核销订单-收入，57到店砍价-结算商家-扣费，58到店砍价-邀请奖励-扣费，59到店砍价-订单取消-扣除团员奖励，60巅峰赛奖金费用-扣除代理商，61巅峰赛奖金扣除-代理商费用返还，62餐餐有返订单机审扣费，63餐餐有返订单机审扣费，64新用户返现-扣费（复购卡活动），65营销支出-任务完成增加金币，66老用户返现-扣费（复购卡活动）';
