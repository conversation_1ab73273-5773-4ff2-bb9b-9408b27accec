INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) VALUES(
                                                                                                                   '卡劵管理',
                                                                                                                   'coupon_manage',
                                                                                                                   0,
                                                                                                                   '',
                                                                                                                   0,
                                                                                                                   'user',
                                                                                                                   '卡劵管理组件'
                                                                                                               );

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '卡劵管理',
        'coupon_manage_list',
        1,
        '',
        t.id,
        'user',
        '卡劵管理组件'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'coupon_manage'
      AND type = 0
);

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '卡劵管理列表',
        'CouponManage.list',
        2,
        'GET',
        t.id,
        'user',
        '卡劵管理列表接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'coupon_manage_list'
      AND type = 1
);

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '卡劵管理删除',
        'coupon_manage_delete',
        1,
        '',
        t.id,
        'user',
        '卡劵管理删除组件'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'coupon_manage'
      AND type = 0
);

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '卡劵管理删除',
        'CouponManage.delete',
        2,
        'POST',
        t.id,
        'user',
        '卡劵管理删除接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'coupon_manage_delete'
      AND type = 1
);

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '卡劵管理恢复',
        'coupon_manage_recover',
        1,
        '',
        t.id,
        'user',
        '卡劵管理恢复组件'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'coupon_manage'
      AND type = 0
);

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '卡劵管理恢复',
        'CouponManage.recover',
        2,
        'POST',
        t.id,
        'user',
        '卡劵管理恢复接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'coupon_manage_recover'
      AND type = 1
);