CREATE TABLE `bwc_user_intercept_whitelist`
(
    `id`                       int(11) unsigned NOT NULL AUTO_INCREMENT,
    `user_id`                  int(11) NOT NULL DEFAULT '0' COMMENT 'bwc_user表id',
    `user_intercept_whitelist` tinyint(1) NOT NULL DEFAULT '0' COMMENT '开启/关闭北京骗返白名单，1关闭，2开启',
    `data_state`               tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`              timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`              timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                        `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;