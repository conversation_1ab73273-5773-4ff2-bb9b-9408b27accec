CREATE TABLE `bwc_advertisement_click_record`
(
    `id`              int(10) unsigned NOT NULL AUTO_INCREMENT,
    `platform`        varchar(30)  NOT NULL DEFAULT '' COMMENT '平台',
    `version`         varchar(255) NOT NULL DEFAULT '' COMMENT '版本',
    `user_id`         int(10) NOT NULL DEFAULT '0' COMMENT '用户id',
    `user_device_cid` varchar(80)  NOT NULL DEFAULT '' COMMENT '设备号',
    `ad_id`           int(10) NOT NULL DEFAULT '0' COMMENT 'bwc_advertisement表id',
    `create_time`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY               `idx_create_time` (`create_time`) USING BTREE,
    KEY               `idx_user_device_cid` (`user_device_cid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;