-- 修改表数据
ALTER TABLE `bwc_order_verify`
    ADD COLUMN `is_server_proxy` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否服务端数据代理 0否 1是' AFTER `version`;

-- 启用服务端数据通过curl获取 1：启用(服务端自行获取数据); 2:禁用(客户端获取数据上传)
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
    ( 'string', '数据上传方式开关', 'enable_server_curl_data_upload', '1', '', 0 );

-- 防骗版本
INSERT INTO `bwc_version_compatible` ( `version_name`, `min_version`, `version_desc`, `data_state`, `create_time`, `update_time` )
VALUES
    ( 'upgrade_fp_way', 20230, '升级上传数据防骗方式', 0, '2024-05-07 15:38:18', '2024-05-07 15:38:18' );
