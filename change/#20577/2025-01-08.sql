ALTER TABLE `bwc_seller`
    ADD COLUMN `relive_switch` tinyint NOT NULL DEFAULT 1 COMMENT '商家控制订单能否复活开关：1可以复活，0不能复活' AFTER `alipay_account`;

insert into bwc_authority_temp(`name`, `key`, type, method, parent_id,`group`,description,disabled) (
    select '商家复活设置', 'seller_relive_setting', 1, '', t.id,'business', '商家复活组件', 2 from bwc_authority_temp as t where t.`key`='seller' and t.type=0
);

insert into bwc_authority_temp(`name`, `key`, type, method, parent_id,`group`,description,disabled) (
    select '商家复活设置接口', 'seller.seller_relive_setting', 2, 'POST', t.id,'business', '商家复活接口', 2 from bwc_authority_temp as t where t.`key`='seller_relive_setting' and t.type=1
);