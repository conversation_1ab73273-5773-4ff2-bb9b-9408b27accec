CREATE TABLE `bwc_user_competitor_app`
(
    `id`             INT(11) NOT NULL AUTO_INCREMENT,
    `user_id`        INT(11) NOT NULL,
    `competitor_app` int(11) NOT NULL DEFAULT '0' COMMENT '竞对app，1：小蚕，2：歪麦',
    `data_state`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `update_time`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_user_id_competitor_app` (`user_id`, `competitor_app`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户竞对app安装表';

CREATE INDEX idx_send_scene ON bwc_red_envelopes(send_scene);

INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`, `create_time`, `update_time`) VALUES (NULL, 'string', '领红包弹窗背景图', 'claim_red_envelope_bg_img', 'https://image.xiaoxiaoyouxuan.com/admin/image_202412231412177e1d5c137b883814.png', '', 0, '2024-12-06 15:12:42', '2024-12-06 15:12:42');
INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`, `create_time`, `update_time`) VALUES (NULL, 'string', '领红包弹窗文案', 'claim_red_envelope_text', '由多个晓晓红包组成', '', 0, '2024-12-06 15:12:42', '2024-12-06 15:12:42');


INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '查看总数', 'red_envelope_manage_count', 1, '', t.`id`, 'user', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'red_envelope_manage' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '查看总数-接口', 'RedEnvelopeManage.count', 2, 'GET', t.`id`, 'user', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'red_envelope_manage_count' AND `type` = 1) AS t;
