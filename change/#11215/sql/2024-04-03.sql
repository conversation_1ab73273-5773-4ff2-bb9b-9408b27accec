ALTER TABLE `bwc_task` ADD COLUMN `is_brand_coupon` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否需要大牌专享卡券：1：是 0：否' AFTER `is_brand`;
ALTER TABLE `bwc_task_cycle_create` ADD COLUMN `is_brand_coupon` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否需要大牌专享卡券：1：是 0：否' AFTER `is_brand`;
ALTER TABLE `bwc_middleman_statement` MODIFY COLUMN `remark` varchar(500) DEFAULT NULL COMMENT '备注';
-- 权限
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1526, '发布需要大牌专享卡券任务', 'publish_brand_coupon', 1, '', 147, 'business', '', 2, 0, '2023-11-07 15:37:38', '2023-11-07 15:41:34');

