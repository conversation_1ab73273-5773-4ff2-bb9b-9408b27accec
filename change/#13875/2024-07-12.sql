CREATE TABLE `bwc_ele_order_finished_record`
(
    `id`          int(11) UNSIGNED        NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `admin_id`    int(11) UNSIGNED        NOT NULL DEFAULT 0 COMMENT '操作人id',
    `order_id`    int(11) UNSIGNED        NOT NULL DEFAULT 0 COMMENT '订单id',
    `amount`      decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '返现金额',
    `data_state`  tinyint(1)              NOT NULL DEFAULT 0 COMMENT '数据状态0正常1删除',
    `create_time` timestamp(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `update_time` timestamp(0)            NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='饿了么订单状态更新为完成记录';

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '完成更新状态',
        'mt_finish_order',
        1,
        '',
        t.id,
        'business',
        '完成更新状态'
    FROM
        bwc_authority_temp AS t
    WHERE
        t.KEY = 'mt_order'
      AND type = 0
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '完成更新状态接口',
        'MtOrder.update_to_finished',
        2,
        'POST',
        t.id,
        'business',
        '完成更新状态接口'
    FROM
        bwc_authority_temp AS t
    WHERE
        t.KEY = 'mt_finish_order'
      AND type = 1
);