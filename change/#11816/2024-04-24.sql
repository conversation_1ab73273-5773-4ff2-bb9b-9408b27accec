ALTER TABLE `bwc_blacklist_rules`
    MODIFY COLUMN `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '匹配类型:1:登录ip，2设备号，3支付宝姓名，4支付宝账号，5实时ip，6极光设备号，7实名认证身份证号' AFTER `id`;

INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1568, '全部关联信息拉黑', 'user_blacklist_all', 1, '', 743, 'user', '', 2, 0, '2024-04-24 15:30:33', '2024-04-24 15:30:33');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1569, '全部关联信息拉黑', 'user.blacklist_all', 2, 'POST', 1568, 'user', '', 2, 0, '2024-04-24 15:31:14', '2024-04-24 15:31:14');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1570, '解除全部关联信息拉黑', 'user_relieve_blacklist_all', 1, '', 743, 'user', '', 2, 0, '2024-04-24 15:31:35', '2024-04-24 15:31:43');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1571, '解除全部关联信息', 'user.blacklist_all', 2, 'POST', 1570, 'user', '', 2, 0, '2024-04-24 15:31:54', '2024-04-24 15:32:17');

CREATE TABLE `bwc_user_operation_auth`
(
    `id`            int(11) unsigned NOT NULL AUTO_INCREMENT,
    `user_id`       int(11) NOT NULL DEFAULT '0',
    `blacklist_all` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否拉黑所有关联信息：0否，1是',
    `data_state`    tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`   timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`   timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY             `idx_user_id` (`user_id`,`data_state`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;