INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`)
VALUES (NULL, 'string', '如何使用晓晓优选', 'newcomer_orientation_how',
        '["https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240606134805f12880c488288076.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240606134809286605d3d0286447.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240606134812f559677bcd514597.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240606134816d66b1d6b3f624390.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240606134821912cc56a27351367.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_202406061348241c71c8ccdd468338.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_202406061348271d93671afb857802.jpg"]',
        '', 0);
INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`)
VALUES (NULL, 'string', '查看常见问题', 'newcomer_orientation_question',
        '["https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_2024060613460151d07ac432453382.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240606134610cd65e320bb467461.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240606134615fb6aefead8446640.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_20240606134618ce3caf9447694455.jpg","https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_2024060613462140f313a64e392574.jpg"]',
        '', 0);

CREATE TABLE `bwc_user_address_history` (
                                            `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            `name` varchar(255) NOT NULL COMMENT '地址名称',
                                            `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id，bwc_user.id',
                                            `longitude` varchar(20) NOT NULL COMMENT '经度',
                                            `latitude` varchar(20) NOT NULL COMMENT '纬度',
                                            `city` varchar(255) NOT NULL COMMENT '城市',
                                            `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
                                            `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                            PRIMARY KEY (`id`) USING BTREE,
                                            KEY `idx_user_update_time` (`user_id`,`update_time`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='用户历史地址表';

CREATE TABLE `bwc_user_search_history` (
                                           `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `keyword` varchar(255) NOT NULL COMMENT '关键词',
                                           `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id，bwc_user.id',
                                           `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
                                           `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           PRIMARY KEY (`id`) USING BTREE,
                                           KEY `idx_user_update_time` (`user_id`,`update_time`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='用户历史搜索表';

CREATE TABLE `bwc_user_footprint` (
                                      `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      `platform_abbr` varchar(32) NOT NULL COMMENT '平台',
                                      `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id，bwc_user.id',
                                      `task_id` varchar(255) NOT NULL COMMENT '任务id',
                                      `store_id` int(11) DEFAULT NULL COMMENT '店铺id，bwc_store.id',
                                      `task_info` json DEFAULT NULL COMMENT '官方活动的信息',
                                      `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
                                      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      KEY `idx_store_id` (`store_id`),
                                      KEY `idx_user_update_time` (`user_id`,`update_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='用户足迹表';
INSERT INTO `bwc_version_compatible` (`id`, `version_name`, `min_version`, `version_desc`, `data_state`, `create_time`, `update_time`) VALUES (NULL, 'user_client_improve_v', 20220, '用户端优化版本', 0, '2024-05-28 11:39:23', '2024-05-28 11:39:23');

ALTER TABLE `bwc_order_verify` ADD COLUMN `switch_check_type` TINYINT (1) NOT NULL DEFAULT '0' COMMENT '切换审核方式：0：默认 1：接口检测 2：自主检测' AFTER `check_type`;
