INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1614, '列表index接口', 'ThirdParty.index', 2, 'GET', 1064, 'open_platform', '', 2, 0, '2024-05-06 15:11:32', '2024-05-06 15:11:54');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1613, '删除接口', 'ThirdParty.delete', 2, 'POST', 1612, 'open_platform', '', 2, 0, '2024-05-06 14:07:23', '2024-05-06 14:11:26');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1612, '删除', 'third_party_delete', 1, '', 1603, 'open_platform', '', 2, 0, '2024-05-06 14:07:13', '2024-05-06 14:09:25');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1611, '详情接口', 'ThirdParty.detail', 2, 'GET', 1610, 'open_platform', '', 2, 0, '2024-05-06 14:07:08', '2024-05-06 14:11:20');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1610, '详情', 'third_party_detail', 1, '', 1603, 'open_platform', '', 2, 0, '2024-05-06 14:06:56', '2024-05-06 14:09:14');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1609, '编辑接口', 'ThirdParty.update', 2, 'POST', 1608, 'open_platform', '', 2, 0, '2024-05-06 14:06:49', '2024-05-06 14:11:17');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1608, '编辑', 'third_party_update', 1, '', 1603, 'open_platform', '', 2, 0, '2024-05-06 14:06:37', '2024-05-06 14:09:03');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1607, '新增接口', 'ThirdParty.create', 2, 'POST', 1606, 'open_platform', '', 2, 0, '2024-05-06 14:06:20', '2024-05-06 15:10:15');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1606, '新增', 'third_party_create', 1, '', 1603, 'open_platform', '', 2, 0, '2024-05-06 14:05:41', '2024-05-06 15:10:09');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1605, '列表接口', 'ThirdParty.list', 2, 'GET', 1604, 'open_platform', '', 2, 0, '2024-05-06 14:04:54', '2024-05-06 14:10:55');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1604, '列表', 'third_party_list', 1, '', 1603, 'open_platform', '', 2, 0, '2024-05-06 14:03:35', '2024-05-06 14:05:23');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1603, '第三方平台', 'third_party', 0, '', 0, 'open_platform', '', 2, 0, '2024-05-06 14:03:13', '2024-05-06 14:03:13');