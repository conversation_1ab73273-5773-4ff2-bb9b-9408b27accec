-- 服务端检测饿了么单数阈值
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
    ( 'string', '服务端检测饿了么单数阈值', 'server_check_ele_user_order_num', '2', '', 0 );

-- 服务端检测饿了么任务金额阈值
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
    ( 'string', '服务端检测饿了么任务金额阈值', 'server_check_ele_order_money', '20', '', 0 );

-- 美团数据上传方式开关
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
    ( 'string', '美团数据上传方式开关', 'enable_mt_server_curl_data_upload', '1', '', 0 );

-- 饿了么数据上传方式开关
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
    ( 'string', '饿了么数据上传方式开关', 'enable_ele_server_curl_data_upload', '1', '', 0 );

-- 京东秒送数据上传方式开关
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
    ( 'string', '京东秒送数据上传方式开关', 'enable_jd_ms_server_curl_data_upload', '1', '', 0 );

-- 服务端检测京东秒送单数阈值
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
    ( 'string', '服务端检测京东秒送单数阈值', 'server_check_jd_ms_user_order_num', '1', '', 0 );

-- 服务端检测京东秒送任务金额阈值
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
    ( 'string', '服务端检测京东秒送任务金额阈值', 'server_check_jd_ms_order_money', '20', '', 0 );
