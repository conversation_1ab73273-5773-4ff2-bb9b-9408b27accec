ALTER TABLE `bwc_user_all_amount_cash`
    ADD INDEX `idx_sign_up_time`(`sign_up_time`) USING BTREE;

ALTER TABLE `bwc_user_all_amount_cash`
    ADD COLUMN `red_envelope_amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '返还的红包金额' AFTER `status`;

-- 页面
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled) VALUES ('首单全额返查询', 'user_all_amount_cash', 0, '', 0, 'user', '首单全额返查询', 2);
-- 组件
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled) (
    select '列表', 'user_all_amount_cash_list', 1, '', t.id, 'user', '', 2 from bwc_authority_temp as t where `key`='user_all_amount_cash' and type=0
);
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled) (
    select '查询总数', 'user_all_amount_cash_count', 1, '', t.id, 'user', '', 2 from bwc_authority_temp as t where `key`='user_all_amount_cash' and type=0
);
-- 接口
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled) (
    select 'index接口', 'UserAllAmountCash.index', 2, 'GET', t.id, 'user', '', 2 from bwc_authority_temp as t where `key`='user_all_amount_cash_list' and type=1
);
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled) (
    select '列表接口', 'UserAllAmountCash.list', 2, 'GET', t.id, 'user', '', 2 from bwc_authority_temp as t where `key`='user_all_amount_cash_list' and type=1
);
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled) (
    select '查询总数接口', 'UserAllAmountCash.count', 2, 'GET', t.id, 'user', '', 2 from bwc_authority_temp as t where `key`='user_all_amount_cash_count' and type=1
);