CREATE TABLE `bwc_order_sort`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `order_id`    int(11) NOT NULL DEFAULT '0' COMMENT '订单id',
    `sort_level`  tinyint(1) NOT NULL DEFAULT '1' COMMENT '排序等级：数值越大，排序越靠前',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY           `idx_order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;