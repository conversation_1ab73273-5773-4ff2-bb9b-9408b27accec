-- 用户和黑名单手机号映射表
CREATE TABLE `bwc_blacklist_map` (
                                     `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                     `user_id` int(10) NOT NULL DEFAULT '0' COMMENT '用户id',
                                     `blacklist_phone` varchar(11) NOT NULL DEFAULT '' COMMENT '黑名单手机号',
                                     `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     UNIQUE KEY `idx_user_id_phone` (`user_id`,`blacklist_phone`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='用户和黑名单手机号映射表';

