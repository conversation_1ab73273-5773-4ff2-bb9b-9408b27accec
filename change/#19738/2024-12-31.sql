CREATE TABLE `bwc_store_relive_switch`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `store_id`    int(11) NOT NULL DEFAULT '0' COMMENT '店铺id',
    `type`        tinyint(4) NOT NULL DEFAULT '1' COMMENT '店铺复活方式：1遵循中间人配置，2独立设置',
    `switch`      tinyint(4) NOT NULL DEFAULT '1' COMMENT '独立设置开关：1开，0关',
    `data_state`  tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY           `idx_store_id` (`store_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺控制订单是否显示复活按钮';

insert into bwc_authority_temp(`name`, `key`, type, method, parent_id,`group`,description,disabled) (
    select '店铺复活设置', 'store_relive_setting', 1, '', t.id,'business', '店铺复活组件', 2 from bwc_authority_temp as t where t.`key`='store' and t.type=0
);

insert into bwc_authority_temp(`name`, `key`, type, method, parent_id,`group`,description,disabled) (
    select '店铺复活设置接口', 'store.store_relive_setting', 2, 'POST', t.id,'business', '店铺复活组件', 2 from bwc_authority_temp as t where t.`key`='store_relive_setting' and t.type=1
);