#开关
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '订单详情弹窗开关(图文反馈)(1开，0关)', 'order_tip_switch_picture_praise', '1', '', 0);
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '订单详情弹窗开关(无需反馈)(1开，0关)', 'order_tip_switch_no_praise', '1', '', 0);
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '订单详情弹窗开关(文字反馈)(1开，0关)', 'order_tip_switch_text_praise', '1', '', 0);
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '订单详情弹窗开关(无需图文)(1开，0关)', 'order_tip_switch_no_picture_text', '1', '', 0);

#内容
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '订单详情滚动条内容(图文反馈)', 'order_scroll_content_picture_praise', '反馈图片或文字，必须为此订单餐品收到的真实反馈，否则无法参与活动哦', '', 0);
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '订单详情滚动条内容(无需反馈)', 'order_scroll_content_no_praise', '本单无需反馈，如反馈将无法拿到额外返利哦', '', 0);
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '订单详情滚动条内容(文字反馈)', 'order_scroll_content_text_praise', '反馈的文字内容，必须为此订单餐品收到的真实反馈，否则无法参与活动哦', '', 0);
INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '订单详情滚动条内容(无需图文)', 'order_scroll_content_no_picture_text', '本单无需图文反馈，只亮星即可，如反馈图文将无法拿到额外返利哦', '', 0);

#创建订单提示表
CREATE TABLE `bwc_order_tip`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT,
    `order_id`    int(11) NOT NULL DEFAULT '0',
    `is_tip`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经提示：0没有，1已提示',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY           `idx_order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单提示表，一个订单提示一次';