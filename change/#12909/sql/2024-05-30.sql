-- 修改代理表
ALTER TABLE `bwc_agent`
    ADD COLUMN `open_third_push_task` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否开启给第三方平台推送活动，0否1是，默认开启' AFTER `ele_feedback_main_deduct_ratio`;

-- 修改第三方用户表
ALTER TABLE `bwc_third_user`
    ADD COLUMN `is_black` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否拉黑 0否；1是' AFTER `restricted`,
    ADD COLUMN `operator_id` int(11) NOT NULL DEFAULT 0 COMMENT '操作人id' AFTER `is_black`;

-- task_app_agent表
CREATE TABLE `bwc_task_app_agent` (
                                      `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `task_app_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '三方平台id',
                                      `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型 1禁用；2启用',
                                      `agent_id` int(11) NOT NULL DEFAULT '0' COMMENT '代理id',
                                      `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
                                      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3003 DEFAULT CHARSET=utf8mb4 COMMENT='第三方对应城市开放表';

-- 商家拉黑第三方用户数据表
CREATE TABLE `bwc_seller_third_user_blacklist` (
                                                   `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                   `identifier` varchar(128) NOT NULL DEFAULT '' COMMENT '用户唯一标识',
                                                   `appid` varchar(50) NOT NULL DEFAULT '' COMMENT '第三方appid',
                                                   `store_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '店铺id',
                                                   `operator_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '操作类型0商家1客服',
                                                   `operator_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作人id，根据operator_type指示，0表示记录seller_id, 1表示admin_id',
                                                   `reason` varchar(255) NOT NULL DEFAULT '' COMMENT '拉黑原因',
                                                   `remove_operator_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '移除操作类型0商家，1后台管理人员操作',
                                                   `remove_operator_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '移除操作人id',
                                                   `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
                                                   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                   PRIMARY KEY (`id`),
                                                   KEY `idx_user_app_store` (`identifier`,`appid`,`store_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 权限设置
INSERT INTO `bwc_authority_temp` ( `id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time` )
VALUES
    ( 1681, '拉黑第三方用户', 'order_add_third_user_to_black', 1, '', 159, 'business', '拉黑第三方用户', 2, 0, '2024-06-07 10:54:35', '2024-06-07 16:40:57' );
INSERT INTO `bwc_authority_temp` ( `id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time` )
VALUES
    ( 1682, '拉黑第三方用户接口', 'order.add_third_user_to_black', 2, 'POST', 1681, 'business', '拉黑第三方用户接口', 2, 0, '2024-06-07 10:55:13', '2024-06-07 16:40:57' );
