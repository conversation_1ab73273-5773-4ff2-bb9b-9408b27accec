-- 中间人阶梯奖励表
CREATE TABLE `bwc_middleman_ladder_reward`
(
    `id`                 int(10) unsigned NOT NULL AUTO_INCREMENT,
    `middleman_id`       int(10) NOT NULL DEFAULT '0' COMMENT '中间人表id',
    `date_month`         int(11) NOT NULL DEFAULT '0' COMMENT '所属年份月份：例如202503',
    `order_num`          int(10) NOT NULL DEFAULT '0' COMMENT '订单量',
    `is_full_settlement` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否全部结算：1是，0否',
    `amount`             decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '额外打款金额',
    `is_payment`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否打款：0未打款，1打款中，2已打款，3打款失败',
    `data_state`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`        timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`        timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                  `idx_middleman_id` (`middleman_id`) USING BTREE,
    KEY                  `idx_year_month` (`date_month`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中间人阶梯奖励表';

-- 中间人阶梯奖励配置表
CREATE TABLE `bwc_middleman_ladder_reward_config`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `date_month`  int(11) NOT NULL DEFAULT '0' COMMENT '日期转成数字',
    `data`        json               DEFAULT NULL COMMENT '保存的数据json',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_date_month` (`date_month`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中间人阶梯奖励配置表';
-- 默认初始化配置
INSERT INTO `bwc_middleman_ladder_reward_config` (`date_month`, `data`) VALUES (202503, '{\"method\": [{\"reward\": 0.1, \"end_value\": 3999, \"start_value\": 3000}, {\"reward\": 0.3, \"end_value\": 5999, \"start_value\": 4000}, {\"reward\": 0.5, \"end_value\": 999999, \"start_value\": 6000}], \"is_open\": true, \"commission\": 2, \"reward_type\": \"ladder\"}');

-- 修改bwc_agent_billing_record
ALTER TABLE `bwc_agent_billing_record`
    MODIFY COLUMN `type` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型 1充值，2短信通知扣费，3短信验证码扣费，4用户返现扣费，5邀请奖励扣费，6机审接口付费，7每单服务费，8中间人打款，9管理员扣款，10渠道推广分佣，11渠道推广退款，12商家支付打款给代理，13代理提现，14代理提现驳回 15APP推送通知 16ai外呼 17 全区域分成扣费 18 全区域分成收入 19活动完成-下单返利 20活动完成-邀请返利 21订单取消扣除积分 22订单扣除积分 23订单扣除金币 24订单取消扣除团员奖励 25订单取消扣除下单返利活动奖励 26订单取消扣除邀请返利活动奖励 27订单增加积分 28订单增加金币 29订单恢复增加积分 30订单恢复增加团员奖励 31订单恢复增加下单返利活动奖励 32订单恢复增加邀请返利活动奖励 33营销推送 34商家退款-扣费 35运营短信 36自动任务-APP推送 37自动任务-短信 38渠道推广分佣（上级）39渠道推广退款（上级）40物料返现补贴-扣费 41市场部拉新提成-扣费 42商家获客费用-扣费 43用户返现-扣费（通用活动）44订单助力加返-增加用户积分 45餐餐有返中间人返佣 46订单助力加返-扣除用户积分 47助力领现金现金任务-扣除代理 48助力领现金-可提现金币-扣除代理 49经纪人-扣费' AFTER `agent_id`;
-- 修改bwc_middleman_statement
ALTER TABLE `bwc_middleman_statement`
    MODIFY COLUMN `type` tinyint(2) NULL DEFAULT NULL COMMENT '流水类型枚举(1:订单佣金入账 2:财务人工扣除 3:财务人工扣除返还 4:提现扣除5:提现失败返还6:餐餐有返-中间人返点)7中间人阶梯奖励' AFTER `middleman_id`,
    MODIFY COLUMN `source_type` tinyint(2) NULL DEFAULT NULL COMMENT '来源类型枚举(1:代理商账单表bwc_agent_middleman_bill 2:中间人提现id3:餐餐有返订单id，4中间人阶梯奖励id，5中间人阶梯奖励批量表id)' AFTER `source_id`;

-- 页面
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
VALUES ('中间人阶梯奖励', 'middleman_ladder_reward', 0, '', 0, 'finance', '', 2);
-- 列表组件
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '中间人阶梯奖励', 'middleman_ladder_reward_list',1, '', t.id, 'finance', '中间人阶梯奖励列表', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward' and type=0);
-- 列表接口
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '中间人阶梯奖励列表接口', 'MiddlemanLadderReward.list',2, 'GET', t.id, 'finance', '中间人阶梯奖励列表接口', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward_list' and type=1);
-- index接口
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '中间人阶梯奖励index接口', 'MiddlemanLadderReward.index',2, 'GET', t.id, 'finance', '中间人阶梯奖励index接口', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward_list' and type=1);

-- 阶梯奖励配置组件
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '阶梯奖励配置', 'middleman_ladder_reward_config',1, '', t.id, 'finance', '阶梯奖励配置', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward' and type=0);
-- 阶梯奖励配置查询接口
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '阶梯奖励配置查询接口', 'MiddlemanLadderReward.get_config',2, 'GET', t.id, 'finance', '阶梯奖励配置查询接口', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward_config' and type=1);
-- 阶梯奖励配置提交接口
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '阶梯奖励配置提交接口', 'MiddlemanLadderReward.update_config',2, 'POST', t.id, 'finance', '阶梯奖励配置提交接口', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward_config' and type=1);

-- 打款
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '打款', 'middleman_ladder_reward_payment',1, '', t.id, 'finance', '打款', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward' and type=0);
-- 打款接口
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '打款接口', 'MiddlemanLadderReward.payment',2, 'POST', t.id, 'finance', '打款接口', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward_payment' and type=1);

-- 查看完整手机号
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '查看完整手机号', 'middleman_ladder_reward_mobile',1, '', t.id, 'finance', '查看完整手机号', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward' and type=0);
-- 查看完整手机号接口
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '查看完整手机号接口', 'MiddlemanLadderReward.get_mobile',2, 'GET', t.id, 'finance', '查看完整手机号接口', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward_mobile' and type=1);

-- java定时任务相关的表
create table bwc_middleman_ladder_reward_order
(
    `id`                         int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `middleman_ladder_reward_id` int(11)          not null default 0 comment '中间人阶梯奖励表id',
    `order_id`                   int(11)          not null default 0 comment '订单id',
    `task_id`                    int(11)          not null default 0 comment '任务id',
    `agent_id`                   int(11)          not null default 0 comment '代理城市id',
    `create_by`                  int(11)          NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_by`                  int(11)          NOT NULL DEFAULT '0' COMMENT '修改人',
    `gmt_created`                timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`               timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete`                  tinyint(1)       NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
    PRIMARY KEY (`id`),
    index idx_middleman_ladder_reward_order (middleman_ladder_reward_id, order_id) using btree,
    index idx_middleman_ladder_reward_task (middleman_ladder_reward_id, task_id) using btree
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中间人阶梯奖励订单关联表';

create table bwc_middleman_ladder_reward_task
(
    `id`                         int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `middleman_ladder_reward_id` int(11)          not null default 0 comment '中间人阶梯奖励表id',
    `task_id`                    int(11)          not null default 0 comment '任务id',
    `create_by`                  int(11)          NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_by`                  int(11)          NOT NULL DEFAULT '0' COMMENT '修改人',
    `gmt_created`                timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`               timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete`                  tinyint(1)       NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
    PRIMARY KEY (`id`),
    index idx_middleman_ladder_reward_order (middleman_ladder_reward_id, task_id) using btree
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中间人阶梯奖励任务关联表';

-- 返点的编辑权限
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '编辑商家/中间人/销售', 'rebate_edit_info',1, '', t.id, 'business', '编辑商家/中间人/销售组件', 2 from bwc_authority_temp as t where t.`key`='rebate' and type=0);

-- 变更原来的权限
UPDATE bwc_authority_temp t
    JOIN (
    SELECT id FROM bwc_authority_temp WHERE `key`='rebate_list' and type=1
    ) AS tt
SET t.parent_id = tt.id where `key`='rebate.index' and type=2;
UPDATE bwc_authority_temp t
    JOIN (
    SELECT id FROM bwc_authority_temp WHERE `key`='rebate_update' and type=1
    ) AS tt
SET t.parent_id = tt.id where `key`='rebate.details' and type=2;

-- 基本配置关联中间人
CREATE TABLE `bwc_middleman_ladder_reward_config_relation`
(
    `id`           int(10) unsigned NOT NULL AUTO_INCREMENT,
    `config_id`    int(11) NOT NULL DEFAULT '0' COMMENT '基础信息配置表id',
    `middleman_id` int(11) NOT NULL COMMENT '中间人id',
    `data_state`   tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`  timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`  timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY            `idx_config_id` (`config_id`) USING BTREE,
    KEY            `idx_middleman_id` (`middleman_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中间人阶梯奖励配置关联中间人表';

insert into bwc_middleman_ladder_reward_config_relation(config_id, middleman_id) select 1, id from bwc_middleman where mobile in (
    '18810448766', '18210123643'
    ) and data_state=0;

-- 批量合并打款组件
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '批量合并打款', 'middleman_ladder_reward_batch',1, '', t.id, 'finance', '批量合并打款', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward' and type=0);
-- 批量合并打款查询接口
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '批量合并打款查询接口', 'MiddlemanLadderReward.get_batch_payment',2, 'POST', t.id, 'finance', '批量合并打款查询接口', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward_batch' and type=1);
-- 批量合并打款提交接口
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '批量合并打款提交接口', 'MiddlemanLadderReward.batch_payment',2, 'POST', t.id, 'finance', '批量合并打款提交接口', 2 from bwc_authority_temp as t where t.`key`='middleman_ladder_reward_batch' and type=1);

-- 批量合并打款
CREATE TABLE `bwc_middleman_ladder_reward_batch`
(
    `id`           int(10) unsigned NOT NULL AUTO_INCREMENT,
    `middleman_id` int(10) NOT NULL DEFAULT '0' COMMENT '打款中间人id',
    `order_num`    int(10) NOT NULL DEFAULT '0' COMMENT '总的订单量',
    `amount`       decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '额外打款金额',
    `data_state`   tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`  timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY            `idx_middleman_id` (`middleman_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中间人阶梯奖励批量合并打款表';
CREATE TABLE `bwc_middleman_ladder_reward_batch_relation`
(
    `id`                               int(10) unsigned NOT NULL AUTO_INCREMENT,
    `middleman_ladder_reward_batch_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'bwc_middleman_ladder_reward_batch表id',
    `middleman_ladder_reward_id`       int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'bwc_middleman_ladder_reward表id',
    `data_state`                       tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`                      timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                      timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                                `idx_middleman_ladder_reward_batch_id` (`middleman_ladder_reward_batch_id`) USING BTREE,
    KEY                                `idx_middleman_ladder_reward_id` (`middleman_ladder_reward_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='中间人阶梯奖励批量合并打款关联中间人表';
