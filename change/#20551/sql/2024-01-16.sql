-- 新增order_mt_code表
CREATE TABLE `bwc_order_mt_code` (
                                     `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                     `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
                                     `code` int(11) NOT NULL DEFAULT '0' COMMENT '美团回调推送的code值',
                                     `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`),
                                     KEY `idx_order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;