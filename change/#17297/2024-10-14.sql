CREATE TABLE `bwc_agent_ratio_config`
(
    `id`                 int(11) unsigned NOT NULL AUTO_INCREMENT,
    `agent_id`           int(11) unsigned NOT NULL COMMENT '代理区域id',
    `type`               tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型配置：1（美团官方活动比例配置）',
    `start_value`        int(1) NOT NULL DEFAULT '0' COMMENT '比例区间，开始字段（万分比）',
    `end_value`          int(1) NOT NULL DEFAULT '0' COMMENT '比例区间结束字段（万分比）',
    `main_deduct_ratio`  int(1) NOT NULL DEFAULT '0' COMMENT '总部抽成比例（万分比）',
    `agent_deduct_ratio` int(1) NOT NULL DEFAULT '0' COMMENT '代理抽成比例（万分比）',
    `data_state`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`        timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`        timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY                  `idx_agent_id` (`agent_id`,`data_state`,`type`,`start_value`,`end_value`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;