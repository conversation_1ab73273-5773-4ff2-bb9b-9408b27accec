CREATE TABLE `bwc_user_favorite`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `platform_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '平台id，bwc_platform.id',
    `user_id`     int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID，bwc_user.id',
    `store_id`    varchar(255) NOT NULL DEFAULT '' COMMENT '店铺ID',
    `store_name`  varchar(64)  NOT NULL DEFAULT '' COMMENT '店铺名称',
    `cover`       varchar(255) NOT NULL DEFAULT '' COMMENT '店铺封面',
    `create_time`  timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`  timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unique_user_favorite` (`platform_id`,`user_id`,`store_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;


INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`, `create_time`, `update_time`) VALUES (NULL, 'string', '店铺距离过远距离阈值公里', 'task_distance_limit', '5', '', 0, '2024-12-06 15:12:38', '2024-12-09 11:05:50');
