ALTER TABLE `bwc_store_error_report`
    ADD COLUMN `num` int(11) unsigned NOT NULL DEFAULT 1 COMMENT '上报次数' after `user_id`;

ALTER TABLE `bwc_store_error_report`
    ADD COLUMN `report_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上报时间' AFTER `num`;

update bwc_store_error_report
set data_state = 1;

CREATE TABLE `bwc_store_error_report_user`
(
    `id`                         int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id`                    int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'bwc_user.id c用户id',
    `store_error_report_type_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'bwc_store_error_report_type.id 类型id',
    `store_id`                   int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'bwc_store.id 店铺id',
    `data_state`                 tinyint(3) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time`                timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                          `idx_user_id` (`user_id`),
    KEY                          `idx_store_error_report_type_id` (`store_error_report_type_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='店铺错误信息上报用户';
