INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '订单详情选择进店方式(1开，0关)', 'order_detail_enter_store_method_switch', '1', '', 0);

CREATE TABLE `bwc_order_store_enter_method`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `order_id`    int(11) NOT NULL DEFAULT '0' COMMENT '任务id',
    `method`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '进店方式：1app进店，2二维码进店，3小程序进店，4店铺名称进店',
    `platform`    varchar(50) NOT NULL DEFAULT '' COMMENT '平台：ios,android,weixin-web,web',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY           `idx_order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户订单选择的进入方式';

CREATE TABLE `bwc_order_store_enter_method_record`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `order_id`    int(11) NOT NULL DEFAULT '0' COMMENT '订单id',
    `task_id`     int(11) NOT NULL DEFAULT '0' COMMENT '任务id',
    `user_id`     int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `method`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '进店方式：1app进店，2二维码进店，3小程序进店，4店铺名称进店',
    `platform`    varchar(50) NOT NULL DEFAULT '' COMMENT '平台：ios,android,weixin-web,web',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY           `idx_order_id` (`order_id`) USING BTREE,
    KEY           `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户订单选择的进入方式记录表';