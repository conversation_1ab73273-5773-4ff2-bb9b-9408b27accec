-- 修改task_cycle_create_ext表
ALTER TABLE `bwc_task_cycle_create_ext` ADD COLUMN `task_commission_fee` DECIMAL ( 10, 2 ) NOT NULL DEFAULT 0 COMMENT '活动服务费，管理后台填写的值' AFTER `max_amount`;

-- 短信模板
INSERT INTO `bwc_sms_template`(`channel_id`, `code`, `name`, `enabled`, `config`, `create_time`, `update_time`) VALUES (2, 'STORE_UNBIND_NOTICE', '解绑门店任务自动关闭通知', 1, '{\"sign_id\":24895,\"temp_id\":\"237832\",\"url\":\"https://api.sms.jpush.cn\",\"uri\":\"/v1/messages\"}', '2024-11-11 11:07:15', NULL);
