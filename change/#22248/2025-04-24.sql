CREATE TABLE `bwc_user_identity_record`
(
    `id`               int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户表id',
    `user_id`          int(10) NOT NULL DEFAULT '0' COMMENT '用户表id',
    `real_name`        varchar(50) NOT NULL DEFAULT '' COMMENT '实名姓名',
    `identity_no`      varchar(80) NOT NULL DEFAULT '' COMMENT '加密的身份证',
    `identity_card_id` int(10) NOT NULL DEFAULT '0' COMMENT '对应的身份信息表id',
    `type`             tinyint(1) NOT NULL DEFAULT '0' COMMENT '操作类型：1用户主动清空实名信息，2管理后台清空用户实名信息，3调用云账户接口发现非签约状态清空实名信息，4绑定用户实名信息',
    `data_state`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`      timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`      timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY                `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户变更实名认证身份证信息';