ALTER TABLE `bwc_store`
    ADD COLUMN `is_mark_invoice` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否标记开票，0否，1是' AFTER `is_advance`;

ALTER TABLE `bwc_store`
    ADD INDEX `idx_is_mark_invoice`(`is_mark_invoice`) USING BTREE;

CREATE TABLE `bwc_store_invoice`
(
    `id`           int(10) unsigned NOT NULL AUTO_INCREMENT,
    `store_id`     int(11) NOT NULL DEFAULT '0' COMMENT '店铺id',
    `amount`       decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '金额',
    `channel`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '支付渠道：1晓晓惠点餐，2沪杭惠，3成都分公司',
    `start_time`   timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `end_time`     timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `remark`       text           NOT NULL COMMENT '备注',
    `pay_method`   tinyint(1) NOT NULL DEFAULT '0' COMMENT '支付方式：1支付宝，2微信',
    `pay_no`       varchar(50)    NOT NULL DEFAULT '' COMMENT '商户订单号',
    `payment_time`   datetime              DEFAULT NULL COMMENT '商家支付时间',
    `pay_status`   tinyint(1) NOT NULL DEFAULT '1' COMMENT '支付状态：0未支付，1待支付，2支付成功，3支付失败',
    `pay_remark`   text COMMENT '支付失败信息',
    `data_state`   tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0正常，1删除',
    `create_time`  timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`  timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY            `idx_store_id` (`store_id`) USING BTREE,
    KEY `idx_pay_no` (`pay_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺开票记录表';

CREATE TABLE `bwc_store_invoice_pay_order`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `store_id`       int(11) NOT NULL DEFAULT '0',
    `invoice_id`     int(11) NOT NULL DEFAULT '0',
    `bill_fee`       decimal(10, 2) unsigned NOT NULL DEFAULT '0.00' COMMENT '账单金额',
    `pay_method`     tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '1支付宝 2微信',
    `receipt_amount` decimal(10, 2) unsigned NOT NULL DEFAULT '0.00' COMMENT '到账金额',
    `pay_no`         varchar(255) NOT NULL DEFAULT '' COMMENT '商户订单号',
    `payment_time`   datetime              DEFAULT NULL COMMENT '商家支付时间',
    `pay_status`     tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '支付状态1.支付中 2.已支付，3支付失败',
    `pay_remark`     text COMMENT '备注',
    `request_body`   text COMMENT '请求数据',
    `response_body`  text COMMENT '返回数据',
    `data_state`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_ invoice_id` (`invoice_id`) USING BTREE,
    KEY `idx_pay_no` (`pay_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开票三方支付订单表';

CREATE TABLE `bwc_store_invoice_seller_bill`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT,
    `invoice_id`     int(11) NOT NULL DEFAULT '0' COMMENT '关联开票记录表',
    `seller_bill_id` int(11) NOT NULL DEFAULT '0' COMMENT '账单表id',
    `data_state`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`    timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`    timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY              `idx_invoice_id` (`invoice_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='开票记录关联账单表';

