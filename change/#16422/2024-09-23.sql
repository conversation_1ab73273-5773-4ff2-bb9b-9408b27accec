#权限：标记开票/取消标记开票
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '标记/取消开票',
        'change_store_mark_invoice',
        1,
        '',
        t.id,
        'business',
        '标记/取消开票组件'
    FROM
        bwc_authority_temp AS t
    WHERE
        t.KEY = 'store'
      AND type = 0
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '标记/取消开票接口',
        'Store.change_store_invoice',
        2,
        'POST',
        t.id,
        'business',
        '标记/取消开票接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'change_store_mark_invoice'
      AND type = 1
);

#权限：付款二维码
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '付款二维码',
        'store_invoice_create',
        1,
        '',
        t.id,
        'business',
        '付款二维码'
    FROM
        bwc_authority_temp AS t
    WHERE
        t.KEY = 'store'
      AND type = 0
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '付款二维码接口',
        'StoreInvoice.create',
        2,
        'POST',
        t.id,
        'business',
        '付款二维码接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'store_invoice_create'
      AND type = 1
);

#权限：店铺开票记录
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) values (
    '店铺开票记录', 'store_invoice', 0, '', 0, 'finance', '店铺开票记录'
);
#权限：店铺开票记录：列表
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '店铺开票记录列表',
        'store_invoice_list',
        1,
        '',
        t.id,
        'finance',
        '店铺开票记录列表'
    FROM
        bwc_authority_temp AS t
    WHERE
        t.KEY = 'store_invoice'
      AND type = 0
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '店铺开票记录列表index接口',
        'StoreInvoice.index',
        2,
        'POST',
        t.id,
        'finance',
        '店铺开票记录列表index接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'store_invoice_list'
      AND type = 1
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '店铺开票记录列表接口',
        'StoreInvoice.list',
        2,
        'POST',
        t.id,
        'finance',
        '店铺开票记录列表接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'store_invoice_list'
      AND type = 1
);
#权限：店铺开票记录：总数
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '店铺开票记录总数',
        'store_invoice_count',
        1,
        '',
        t.id,
        'finance',
        '店铺开票记录总数'
    FROM
        bwc_authority_temp AS t
    WHERE
        t.KEY = 'store_invoice'
      AND type = 0
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '店铺开票记录总数接口',
        'StoreInvoice.count',
        2,
        'POST',
        t.id,
        'finance',
        '店铺开票记录总数接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'store_invoice_count'
      AND type = 1
);
#权限：店铺开票记录：总金额
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '店铺开票记录总金额',
        'store_invoice_total',
        1,
        '',
        t.id,
        'finance',
        '店铺开票记录总金额'
    FROM
        bwc_authority_temp AS t
    WHERE
        t.KEY = 'store_invoice'
      AND type = 0
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '店铺开票记录总金额接口',
        'StoreInvoice.total',
        2,
        'POST',
        t.id,
        'finance',
        '店铺开票记录总金额接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'store_invoice_total'
      AND type = 1
);
#权限：店铺开票记录：查看二维码
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '店铺开票记录查看二维码',
        'store_invoice_get_qr_url',
        1,
        '',
        t.id,
        'finance',
        '店铺开票记录查看二维码'
    FROM
        bwc_authority_temp AS t
    WHERE
        t.KEY = 'store_invoice'
      AND type = 0
);
#权限：店铺开票记录：导出excel
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '店铺开票记录导出excel',
        'store_invoice_export',
        1,
        '',
        t.id,
        'finance',
        '店铺开票记录导出excel'
    FROM
        bwc_authority_temp AS t
    WHERE
        t.KEY = 'store_invoice'
      AND type = 0
);

#权限：任务管理：标记店铺导出
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '标记店铺导出',
        'task_store_mark_invoice_export',
        1,
        '',
        t.id,
        'business',
        '标记店铺导出'
    FROM
        bwc_authority_temp AS t
    WHERE
        t.KEY = 'task'
      AND type = 0
);