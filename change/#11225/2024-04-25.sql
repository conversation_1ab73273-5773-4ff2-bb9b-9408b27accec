ALTER TABLE `bwc_ticket`
    ADD COLUMN `refund_amount` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '商家退款' AFTER `deduction_amount`;

INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1572, '工单列表-修改信息', 'update_ticket', 1, '', 1468, 'ticket', '', 2, 0, '2024-04-25 16:06:38', '2024-04-25 16:06:38');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (1573, '工单列表-修改信息', 'Ticket.update', 2, '', 1572, 'ticket', '', 2, 0, '2024-04-25 16:07:22', '2024-04-25 16:07:22');

ALTER TABLE `bwc_ticket_tracking`
    ADD COLUMN `refund_amount` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商家退款' AFTER `deduction_amount`,
ADD COLUMN `ticket_type_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '工单类型id' AFTER `refund_amount`;

ALTER TABLE `bwc_ticket_tracking`
    MODIFY COLUMN `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '提交类型：1提单人，2受理人，3系统信息，4修改工单，5提醒补充' AFTER `ticket_id`;
