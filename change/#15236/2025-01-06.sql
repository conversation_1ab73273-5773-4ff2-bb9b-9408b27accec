insert into bwc_authority_temp(`name`, `key`, type, method, parent_id,`group`,description,disabled) (
    select '伴餐上店', 'store_bck_upload', 1, '', t.id,'business', '伴餐上店组件', 2 from bwc_authority_temp as t where t.`key`='store' and t.type=0
);

CREATE TABLE `bwc_user_from`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id`     int(11) NOT NULL DEFAULT '0',
    `scene`       varchar(255) NOT NULL DEFAULT '' COMMENT '场景：register注册',
    `from`        varchar(255) NOT NULL DEFAULT '' COMMENT '来源：bck伴餐卡',
    `data_state`  tinyint(4) NOT NULL DEFAULT '0' COMMENT '0正常，1删除',
    `create_time` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户来源表';