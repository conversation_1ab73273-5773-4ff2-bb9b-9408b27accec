-- 增加权限
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group` ) SELECT
                                                                                               '用户来源分析',
                                                                                               'dashboard_user_register_source_analyse',
                                                                                               1,
                                                                                               '',
                                                                                               t.`id`,
                                                                                               'dashboard'
FROM
    ( SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'console' AND `type` = 0 ) AS t;


INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group` ) SELECT
                                                                                               '用户来源分析下拉参数',
                                                                                               'userRegisterSource.index',
                                                                                               2,
                                                                                               'GET',
                                                                                               t.`id`,
                                                                                               'dashboard'
FROM
    ( SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'dashboard_user_register_source_analyse' AND `type` = 1 ) AS t;

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group` ) SELECT
                                                                                               '用户来源分析统计',
                                                                                               'userRegisterSource.statistics',
                                                                                               2,
                                                                                               'GET',
                                                                                               t.`id`,
                                                                                               'dashboard'
FROM
    ( SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'dashboard_user_register_source_analyse' AND `type` = 1 ) AS t;

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group` ) SELECT
                                                                                               '用户来源分析列表',
                                                                                               'userRegisterSource.list',
                                                                                               2,
                                                                                               'GET',
                                                                                               t.`id`,
                                                                                               'dashboard'
FROM
    ( SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'dashboard_user_register_source_analyse' AND `type` = 1 ) AS t;

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group` ) SELECT
                                                                                               '用户来源分析数量',
                                                                                               'userRegisterSource.count',
                                                                                               2,
                                                                                               'GET',
                                                                                               t.`id`,
                                                                                               'dashboard'
FROM
    ( SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'dashboard_user_register_source_analyse' AND `type` = 1 ) AS t;




ALTER TABLE `bwc_user_register_source`
    ADD COLUMN `category` tinyint(3) NOT NULL DEFAULT 0 COMMENT '分类 1官方; 2 达人;' AFTER `register_source`,
ADD INDEX `idx_category`(`category`) USING BTREE;