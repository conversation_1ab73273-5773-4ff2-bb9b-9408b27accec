CREATE TABLE `bwc_activity_blacklist`
(
    `id`           int(11) unsigned NOT NULL AUTO_INCREMENT,
    `type`         tinyint(4) DEFAULT NULL COMMENT '类型：1，会员红包天天领',
    `user_id`      int(11) NOT NULL COMMENT 'bwc_user.id，C用户id',
    `created_by`   int(11) NOT NULL DEFAULT 0 COMMENT 'bwc_admin.id 管理员id',
    `updated_by`   int(11) DEFAULT NULL COMMENT 'bwc_admin.id 管理员id',
    `data_state`   tinyint(4) NOT NULL DEFAULT 0,
    `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY            `idx_user_id_type` (`type`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动拉黑表';


INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '限制会员红包天天领', 'add_activity_black_list', 1, '', t.`id`, 'user', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'user' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '不限制会员红包天天领', 'remove_activity_black_list', 1, '', t.`id`, 'user', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'user' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '限制会员红包天天领-接口', 'User.add_activity_black_list', 2, 'POST', t.`id`, 'user', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'add_activity_black_list' AND `type` = 1) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '不限制会员红包天天领-接口', 'User.remove_activity_black_list', 2, 'POST', t.`id`, 'user', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'remove_activity_black_list' AND `type` = 1) AS t;


