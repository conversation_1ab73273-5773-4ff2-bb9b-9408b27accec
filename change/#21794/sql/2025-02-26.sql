UPDATE `bwc_config` SET `name` = '批量任务(自动任务,营销推送)禁发时间段配置(state:1开启配置，0关闭配置)（运营短信不受这个控制）' WHERE `key` = 'batch_task_forbid_time_scope' LIMIT 1;
INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`, `create_time`, `update_time`) VALUES (NULL, 'string', '运营短信禁发时间段及开关配置(state:1开启配置，0关闭配置)', 'sms_push_forbid_time_scope', '{\"timeList\":[\"10:00:00-11:40:00\",\"19:00:00-20:00:00\"],\"state\":0}', '', 0, '2024-12-06 15:12:38', '2025-02-20 15:50:15');
