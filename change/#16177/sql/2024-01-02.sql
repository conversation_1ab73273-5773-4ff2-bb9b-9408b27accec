ALTER TABLE `bwc_seller`
    ADD COLUMN `account_type` varchar(32) DEFAULT 'sub' COMMENT '账号类型:main=主账号，sub=子账号';
-- 为 account_type 添加索引
CREATE INDEX `idx_account_type` ON `bwc_seller` (`account_type`);

CREATE TABLE `bwc_seller_account_relation`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `main_seller_id` int(11) unsigned NOT NULL COMMENT '主账号ID',
    `sub_seller_id`  int(11) unsigned NOT NULL COMMENT '子账号ID',
    `data_state`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态，0表示正常，1表示删除',
    `created_by`     int(11) NOT NULL DEFAULT '0' COMMENT 'bwc_admin.id 管理员ID',
    `updated_by`     int(11) DEFAULT NULL COMMENT 'bwc_admin.id 管理员ID',
    `create_time`    timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_main_seller_id` (`main_seller_id`) USING BTREE,
    KEY              `idx_sub_seller_id` (`sub_seller_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '用户详情', 'seller_info', 1, '', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '禁用', 'seller_disable', 1, '', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '未禁用', 'seller_enable', 1, '', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '设为主账号', 'seller_main', 1, '', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '设为子账号', 'seller_sub', 1, '', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '转移子账号', 'seller_transfer', 1, '', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '管理主账号', 'seller_main_manage', 1, '', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '管理子账号', 'seller_sub_manage', 1, '', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller' AND `type` = 0) AS t;

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '禁用-接口', 'Seller.disable', 2, 'GET', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_disable' AND `type` = 1) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '未禁用-接口', 'Seller.enable', 2, 'GET', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_enable' AND `type` = 1) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '设为主账号-接口', 'Seller.main', 2, 'GET', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_main' AND `type` = 1) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '设为子账号-接口', 'Seller.sub', 2, 'GET', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_sub' AND `type` = 1) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '转移子账号-接口', 'Seller.transfer', 2, 'GET', t.`id`, 'business', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_transfer' AND `type` = 1) AS t;
