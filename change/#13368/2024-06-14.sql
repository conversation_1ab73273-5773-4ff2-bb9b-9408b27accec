INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES ('订单详情修改', 'mt_order_detail_update', 1, '', 238, 'business', '', 2, 0, '2024-06-14 15:07:14', '2024-06-14 15:07:14');
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) (select '订单详情修改', 'MtOrder.detail_update', 2, 'POST', t.id, 'business', '', 2, 0, '2024-06-14 15:07:35', '2024-06-14 15:08:21' from bwc_authority_temp as t where t.key= 'mt_order_detail_update' and type=1);

update bwc_authority_temp set `name`= '恢复订单' where `key`='mt_order_recover' and type=1;
update bwc_authority_temp set `name`= '恢复订单' where `key`='MtOrder.recover' and type=2;
