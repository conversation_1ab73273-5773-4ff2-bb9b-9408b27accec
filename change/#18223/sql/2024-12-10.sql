-- 任务排序返佣的比例
CREATE TABLE `bwc_task_ratio` (
                                  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                  `task_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '任务id',
                                  `ratio` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '任务排序计算的比例',
                                  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  UNIQUE KEY `idx_task_id` (`task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 增加距离选项配置
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform`, `create_time`, `update_time` )
VALUES
    ( 'string', '首页下拉距离选项列表', 'index_select_distance_options', '[{\"id\":\"km_1\",\"name\":\"1km内\"},{\"id\":\"km_2\",\"name\":\"2km内\"},{\"id\":\"km_3\",\"name\":\"3km内\"},{\"id\":\"km_5\",\"name\":\"5km内\"}]', '', 0, '2024-12-10 11:53:47', '2024-12-10 11:53:47' );

INSERT INTO `bwc_version_compatible` ( `version_name`, `min_version`, `version_desc`, `data_state`, `create_time`, `update_time` )
VALUES
    ( 'high_rebate_sort_v', 20435, '首页返利优先排序', 0, '2024-12-20 17:23:50', '2024-12-20 17:23:50' );
