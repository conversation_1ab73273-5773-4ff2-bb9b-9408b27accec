/*
 Navicat Premium Data Transfer

 Source Server         : test
 Source Server Type    : MySQL
 Source Server Version : 50718
 Source Host           : ***********:3306
 Source Schema         : xxh-dev

 Target Server Type    : MySQL
 Target Server Version : 50718
 File Encoding         : 65001

 Date: 27/09/2024 10:57:51
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for bwc_ticket_order_resolution_method_type
-- ----------------------------
DROP TABLE IF EXISTS `bwc_ticket_order_resolution_method_type`;
CREATE TABLE `bwc_ticket_order_resolution_method_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '类型名称',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型：1表示后台用的订单类型',
  `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='工单的订单类型：订单解决方法';

-- ----------------------------
-- Records of bwc_ticket_order_resolution_method_type
-- ----------------------------
BEGIN;
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (0, '已取消', 0, 0, '2024-09-27 10:47:49', '2024-09-27 10:48:16');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (1, '无需取消', 0, 0, '2024-09-27 10:48:37', '2024-09-27 10:48:37');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (2, '部分扣款', 0, 0, '2024-09-27 10:48:41', '2024-09-27 10:48:41');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (3, '个人承担', 1, 0, '2024-09-27 10:48:48', '2024-09-27 10:52:12');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (4, '公司承担', 0, 0, '2024-09-27 10:48:59', '2024-09-27 10:48:59');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (5, '门店拉黑', 0, 0, '2024-09-27 10:49:02', '2024-09-27 10:49:02');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (6, '已追回', 1, 0, '2024-09-27 10:49:12', '2024-09-27 10:52:09');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (7, '未追回', 1, 0, '2024-09-27 10:49:22', '2024-09-27 10:52:00');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (8, '负积分', 1, 0, '2024-09-27 10:49:33', '2024-09-27 10:52:00');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (9, '全额退款/下错店铺', 1, 0, '2024-09-27 10:49:43', '2024-09-27 10:52:00');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (10, '反馈/折叠/录视频', 1, 0, '2024-09-27 10:50:07', '2024-09-27 10:52:00');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (11, '实付不满/敷衍灌水图文/部分退款/饮料凑单/领商家红包', 1, 0, '2024-09-27 10:51:11', '2024-09-27 10:52:00');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (12, '多平台', 1, 0, '2024-09-27 10:51:19', '2024-09-27 10:52:00');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (13, '风控订单', 1, 0, '2024-09-27 10:51:27', '2024-09-27 10:52:00');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (14, '联盟多平台', 1, 0, '2024-09-27 10:51:36', '2024-09-27 10:52:00');
INSERT INTO `bwc_ticket_order_resolution_method_type` (`id`, `name`, `type`, `data_state`, `create_time`, `update_time`) VALUES (15, 'p图/虚假订单', 1, 0, '2024-09-27 10:51:39', '2024-09-27 10:52:00');
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
