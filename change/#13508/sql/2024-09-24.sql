INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`)
VALUES (NULL, 'string', '商家端-分享背景图', 'seller_share_bg',
        'https://image.xiaoxiaoyouxuan.com/admin/image_20241018105703f45ffa3fc9839149.png',
        '', 0);

CREATE INDEX idx_store_id ON bwc_task_views (store_id);

CREATE TABLE `bwc_seller_franchise`
(
    `id`               int(11) NOT NULL AUTO_INCREMENT,
    `advertisement_id` int(11) NOT NULL COMMENT '品牌，bwc_advertisement.id',
    `brand_name`       varchar(255) NOT NULL COMMENT '品牌名称',
    `name`             varchar(255) NOT NULL COMMENT '姓名',
    `mobile`           varchar(50)  NOT NULL COMMENT '联系电话',
    `seller_id`        int(11) NOT NULL COMMENT '商家id，bwc_seller.id',
    `data_state`       tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '1：删除 0：正常',
    `create_time`      timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`      timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY                `idx_seller_id` (`seller_id`),
    KEY                `idx_advertisement_id` (`advertisement_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`,
                                  `data_state`, `create_time`, `update_time`)
VALUES ('招商加盟', 'seller_franchise', 0, '', 0, 'business', '', 2, 0, '2023-08-10 14:04:08', '2023-08-10 14:04:13');
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '招商加盟列表', 'seller_franchise_list', 1, '', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_franchise' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '招商加盟导出', 'seller_franchise_export', 1, '', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_franchise' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '招商加盟列表-接口', 'SellerFranchise.list', 2, 'GET', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_franchise_list' AND `type` = 1) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '招商加盟导出-接口', 'SellerFranchise.export', 2, 'POST', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_franchise_export' AND `type` = 1) AS t;


ALTER TABLE `bwc_excel_download_task`
    MODIFY COLUMN `excel_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '1.全部订单,2.未结算订单,3.商家账单批量导出excel,4.商家账单导出excel,5.发账单导出excel,6.发店铺账单导出excel,7.美团霸王餐账单导出excel,8.导出吃吃龟格式的excel,9.导出吃吃龟未结算账单,10.导出吃吃龟多个商家excel的zip,11.任务excel,13.订单明细导出,14.代理数据统计导出,15.提成结算导出,16.数据统计导出,17.提现账号excel,18.订单审核记录excel,19.修改账单记录excel,20.任务修改记录excel,21.工单中心-工单列表-工单明细导出,22.工单中心-明细统计-工作量明细导出,23.饿了么账单excel,24.推广-推广分组-查询数据-推广数据导出,25.财务-商家退款记录导出,26.饿了么专享账单excel,27.商务-线索管理-列表数据导出,28.灰太狼账单excel,29.店铺开票支付记录,30.标记店铺每日账单导出,31.批量订单行为导出， 32：商家端招商加盟';


ALTER TABLE `bwc_seller`
    ADD `parent_seller_id` int(11) DEFAULT NULL,
ADD INDEX `idx_parent_seller_id` (`parent_seller_id`);

INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`) VALUES (NULL, 'string', '商家端大改版上线时间例如（2024-09-11 00:00:00）', 'new_seller_app_online',  NOW(), '', 0);
INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`) VALUES (NULL, 'string', '商家端大改版假数据倍数', 'new_seller_app_fake_data',  3, '', 0);


CREATE TABLE `bwc_seller_popup_setting` (
                                          `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                          `seller_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户id，bwc_seller.id',
                                          `type` int(11) NOT NULL DEFAULT '0' COMMENT '弹窗类型：1，首页新手引导弹窗 2：店铺新手引导弹窗',
                                          `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1：开启 0：关闭',
                                          `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
                                          `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                          PRIMARY KEY (`id`),
                                          KEY `idx_seller_id_type` (`seller_id`,`type`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4;
