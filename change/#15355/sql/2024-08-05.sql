-- 修改store_repo表
ALTER TABLE `bwc_store_repo` ADD COLUMN `average_price` DECIMAL ( 10, 2 ) NOT NULL DEFAULT 0.00 COMMENT '店铺人均价' AFTER `store_latitude`,
ADD COLUMN `delivery_time` INT ( 10 ) NOT NULL DEFAULT 0 COMMENT '配送时长单位：分钟' AFTER `average_price`;

-- 创建表
CREATE TABLE `bwc_store_repo_ext` (
                                      `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                      `repo_id` int(11) NOT NULL DEFAULT '0',
                                      `recommend_goods` text NOT NULL,
                                      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      KEY `idx_repoId` (`repo_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;