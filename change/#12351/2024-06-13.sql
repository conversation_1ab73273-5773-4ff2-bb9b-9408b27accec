INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '商家端餐标展示定义（1:接单价格 2：用户返现）', 'seller_label_display_definition', '1', '', 0);

INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '任务提醒：不允许下预订单', 'tags_list_not_pre_order', '不允许下预订单', 'tags_list', 0);
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '任务提醒：非饮品店，不允许多个饮品凑单', 'tags_list_not_drinks', '非饮品店，不允许多个饮品凑单', 'tags_list', 0);
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '任务提醒：实付金额需满足活动要求', 'tags_list_pay_mast_meet_task', '实付金额需满足活动要求', 'tags_list', 0);
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '任务提醒：评价需在当天提交', 'tags_list_comment_only_today', '评价需在当天提交', 'tags_list', 0);

INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '商家端新增任务提示：顾客实付', 'seller_notice_meal_price', '若用户实付金额未满足门槛要求，会扣除不满部分（向上取整 )，后返现剩余金额至用户', '', 0);
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '商家端新增任务提示：复购间隔', 'seller_notice_registration_interval', '指同一用户再次下单的间隔天数', '', 0);

