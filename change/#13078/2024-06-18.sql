CREATE TABLE `bwc_task_store_user_limit`
(
    `id`               int(10) unsigned NOT NULL AUTO_INCREMENT,
    `task_id`          int(10) NOT NULL DEFAULT '0' COMMENT '任务id',
    `store_user_limit` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否限制门店新用户下单：1限制，2不限制',
    `data_state`       tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`      timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`      timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY                `idx_task_id` (`task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务关联门线新用户下单专享限制表';