-- 用户app/h5首页最后一次访问位置
CREATE TABLE `bwc_user_visit_last_position` (
                                                `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                `user_id` int(11) NOT NULL COMMENT '用户id',
                                                `agent_id` int(11) NOT NULL DEFAULT '0' COMMENT '代理区域id',
                                                `platform` varchar(255) NOT NULL DEFAULT '',
                                                `ip` varchar(255) NOT NULL DEFAULT '',
                                                `longitude` varchar(20) NOT NULL DEFAULT '' COMMENT '经度',
                                                `latitude` varchar(20) NOT NULL DEFAULT '' COMMENT '纬度',
                                                `user_device_cid` varchar(60) NOT NULL DEFAULT '' COMMENT '用户设备号',
                                                `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
                                                `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                PRIMARY KEY (`id`) USING BTREE,
                                                KEY `idx_user_id_device_cid` (`user_id`,`user_device_cid`) USING BTREE,
                                                KEY `idx_update_time` (`user_id`,`update_time`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='用户app/h5首页最后一次访问位置';

-- 修改表结构
ALTER TABLE `bwc_app_push_template` MODIFY COLUMN `config` text CHARACTER
    SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置' AFTER `enabled`;

-- 增加推送模板
INSERT INTO `bwc_app_push_template` ( `channel_id`, `type`, `code`, `name`, `enabled`, `config`, `create_time`, `update_time` )
VALUES
    ( 1, 2, 'STORE_NEW_RECOMMEND', '店铺上新推荐', 1, '{\"platform\":[\"ios\",\"android\"],\"notification\":{\"ios\":{\"alert\":{\"title\":\"📢附近有{{categoryTag}}店铺上新啦\"}},\"android\":{\"title\":\"📢附近有{{categoryTag}}店铺上新啦\",\"style\":\"bigText\",\"big_text\":\"\",\"alert_type\":7}},\"options\":{\"third_party_channel\":{\"xiaomi\":{\"distribution\":\"ospush\",\"distribution_fcm\":\"pns\"},\"huawei\":{\"distribution\":\"ospush\",\"distribution_fcm\":\"pns\",\"distribution_customize\":\"first_ospush\"},\"honor\":{\"distribution\":\"ospush\",\"distribution_fcm\":\"pns\",\"distribution_customize\":\"first_ospush\"},\"meizu\":{\"distribution\":\"ospush\",\"distribution_fcm\":\"pns\"},\"fcm\":{\"distribution\":\"ospush\"},\"oppo\":{\"distribution\":\"ospush\",\"distribution_fcm\":\"pns\"},\"vivo\":{\"distribution\":\"ospush\",\"distribution_fcm\":\"pns\"}},\"classification\":1,\"apns_production\":false},\"template\":\"快人一步锁定报名，戳>>>\",\"callback\":{\"url\":\"https://xxyx-client-api.xiaoxiaoyouxuan.com/callback/notice_jpush_app_push\",\"type\":11}}', '2025-05-27 15:26:34', '2025-05-27 15:26:34' );

-- 个推模板
INSERT INTO `bwc_app_push_template` ( `channel_id`, `type`, `code`, `name`, `enabled`, `config`, `create_time`, `update_time` )
VALUES
    ( 2, 2, 'STORE_NEW_RECOMMEND', '店铺上新推荐', 1, '{\"settings\":{\"ttl\":7200000,\"strategy\":{\"default\":1,\"ios\":2}},\"push_message\":{\"notification\":{\"title\":\"📢附近有{{categoryTag}}店铺上新啦\",\"body\":\"\",\"click_type\":\"intent\"}},\"push_channel\":{\"android\":{\"ups\":{\"notification\":{\"title\":\"📢附近有{{categoryTag}}店铺上新啦\",\"body\":\"\",\"click_type\":\"intent\"},\"options\":{\"XM\":{\"/extra.channel_id\":\"134525\"},\"VV\":{\"/category\":\"MARKETING\",\"/extra/callback.id\":\"6520\"},\"OP\":{\"/category\":\"MARKETING\",\"/notify_level\":2},\"HW\":{\"/message/android/category\":\"MARKETING\",\"/message/android/receipt_id\":\"RCP5A5AB8AE\"},\"HO\":{\"/android/notification/importance\":\"LOW\"}}}},\"ios\":{\"type\":\"notify\",\"aps\":{\"alert\":{\"title\":\"📢附近有{{categoryTag}}店铺上新啦\",\"body\":\"\"},\"content-available\":0},\"auto_badge\":\"+1\"}},\"template\":\"快人一步锁定报名，戳>>>\",\"intent\":\"intent://com.getui.push/detail?#Intent;scheme=gtpushscheme;launchFlags=0x4000000;package=moc.nauxuoyoaixoaix.www;component=moc.nauxuoyoaixoaix.www/.ui.activity.MainActivity;S.gttask=;\"}', '2025-05-27 15:38:09', '2025-05-27 15:38:09' );

-- 店铺上新推荐推送
INSERT INTO `bwc_message_event` ( `parent_id`, `event_name`, `event_code`, `show_msg_config_status`, `show_user_config`, `create_by`, `update_by`, `gmt_created`, `gmt_modified`, `is_delete` )
VALUES
    ( 4, '店铺上新推荐', 'STORE_NEW_RECOMMEND', 1, 1, 0, 0, '2025-05-28 15:41:45', '2025-05-28 15:41:45', 0 );

-- 店铺上新推荐推送配置
INSERT INTO `bwc_message_event_config` ( `message_event_id`, `config_type`, `config_status`, `create_by`, `update_by`, `gmt_created`, `gmt_modified`, `is_delete` ) SELECT
                                                                                                                                                                        t.id,
                                                                                                                                                                        2,
                                                                                                                                                                        1,
                                                                                                                                                                        0,
                                                                                                                                                                        0,
                                                                                                                                                                        '2025-05-28 15:51:55',
                                                                                                                                                                        '2025-05-28 15:51:55',
                                                                                                                                                                        0
FROM
    ( SELECT `id` FROM `bwc_message_event` WHERE `event_code` = 'STORE_NEW_RECOMMEND' ) AS t;

