-- 修改表结构store_repo
ALTER TABLE `bwc_store_repo` MODIFY COLUMN `tag` VARCHAR ( 50 ) CHARACTER
    SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '店铺标签，不同的标签进店链接不同' AFTER `area_ad_code`;

-- 修改表结构store_link
ALTER TABLE `bwc_store_link` MODIFY COLUMN `tag` VARCHAR ( 50 ) CHARACTER
    SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签' AFTER `id`;

-- 添加休闲娱乐店铺类型
INSERT INTO `bwc_store_link` ( `tag`, `app_id`, `original_id`, `path` )
VALUES
    ( 'mt_group_buy_amusement', 'wxde8ac0a21135c07d', 'gh_870576f3c6f9', 'gcpoi/pages/index?id={#store_id}' );
