begin;
#第一步:更新所有商家冻结金额为0
update bwc_seller_info set freeze_money = 0.00 ;
#第二步:更新今日发布任务的商家的冻结金额，金额为当日任务的总额（如果是下午1点以后）
update bwc_seller_info as i,
    (select sum(task_total_quota * task_receipt_price) as fee,seller_id from bwc_task where task_start_time >= DATE_FORMAT(NOW(),'%Y-%m-%d 00:00:00') and is_pre_pay=3 group by seller_id) as a
set i.freeze_money = a.fee where i.seller_id = a.seller_id;
#第二步备选:更新今日发布任务的商家的冻结金额，金额为当日任务的总额（如果是下午1点以前）
#update bwc_seller_info as i,
#(select sum(task_total_quota * task_receipt_price) as fee,seller_id from bwc_task where task_start_time >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY),'%Y-%m-%d 00:00:00') and is_pre_pay=3 group by seller_id) as a
#set i.freeze_money = a.fee where i.seller_id = a.seller_id;
commit;