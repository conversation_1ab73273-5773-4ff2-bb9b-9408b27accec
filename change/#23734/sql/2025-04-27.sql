ALTER TABLE bwc_middleman_statement
    MODIFY COLUMN `type` TINYINT(2) DEFAULT NULL COMMENT '流水类型枚举(1:订单佣金入账 2:财务人工扣除 3:财务人工扣除返还 4:提现扣除 5:提现失败返还 6:餐餐有返-中间人返点 7:中间人阶梯奖励 8:财务人工增加)';

ALTER TABLE bwc_agent_billing_record
    MODIFY COLUMN `type` TINYINT(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '类型 1充值，2短信通知扣费，3短信验证码扣费，4用户返现扣费，5邀请奖励扣费，6机审接口付费，7每单服务费，8中间人打款，9管理员扣款，10渠道推广分佣，11渠道推广退款，12商家支付打款给代理，13代理提现，14代理提现驳回，15APP推送通知，16ai外呼，17全区域分成扣费，18全区域分成收入，19活动完成-下单返利，20活动完成-邀请返利，21订单取消扣除积分，22订单扣除积分，23订单扣除金币，24订单取消扣除团员奖励，25订单取消扣除下单返利活动奖励，26订单取消扣除邀请返利活动奖励，27订单增加积分，28订单增加金币，29订单恢复增加积分，30订单恢复增加团员奖励，31订单恢复增加下单返利活动奖励，32订单恢复增加邀请返利活动奖励，33营销推送，34商家退款-扣费，35运营短信，36自动任务-APP推送，37自动任务-短信，38渠道推广分佣（上级），39渠道推广退款（上级），40物料返现补贴-扣费，41市场部拉新提成-扣费，42商家获客费用-扣费，43用户返现-扣费（通用活动），44订单助力加返-增加用户积分，45餐餐有返中间人返佣，46订单助力加返-扣除用户积分，47助力领现金现金任务-扣除代理，48助力领现金-可提现金币-扣除代理，49经纪人-扣费，50手动增加中间人余额-扣除代理';


insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '增加余额', 'middleman_statement_add',1, '', t.id, 'finance', '增加余额', 2 from bwc_authority_temp as t where t.`key`='middleman_statement' and type=0);
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '增加余额-接口', 'MiddlemanStatement.add',2, 'POST', t.id, 'finance', '增加余额-接口', 2 from bwc_authority_temp as t where t.`key`='middleman_statement_add' and type=1);
