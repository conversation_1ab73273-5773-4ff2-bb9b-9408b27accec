-- 创建表order_third_failure_record
CREATE TABLE `bwc_order_third_failure_record` (
                                                  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                                  `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
                                                  `failure_reason` varchar(255) NOT NULL DEFAULT '' COMMENT '失败原因',
                                                  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                  PRIMARY KEY (`id`),
                                                  KEY `idx_order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='第三方订单失败原因';

