CREATE TABLE `bwc_user_feedback_prompt`
(
    `id`                   int(11) NOT NULL AUTO_INCREMENT,
    `user_id`              int(11) unsigned NOT NULL DEFAULT 0 COMMENT 'bwc_user.id C用户id',
    `last_feedback_prompt` timestamp NULL DEFAULT NULL COMMENT '上一次弹窗的时间',
    `feedback_click`       tinyint(1) unsigned DEFAULT 0 COMMENT '用户点击：1，写好评，鼓励一下 2，反馈 3，下次再说',
    `platform`             varchar(32) NOT NULL COMMENT '平台类型：安卓，IOS',
    `last_rating_prompt`   timestamp NULL DEFAULT NULL COMMENT 'iOS 用户的评分弹窗的显示时间',
    `data_state`           tinyint(4) unsigned DEFAULT 0,
    `create_time`          timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`          timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY                    `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户评价表';

INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`) VALUES (NULL, 'string', '引导APP评价弹窗上线时间例如（2024-09-11 00:00:00）', 'prompt_app_review_online',  NOW(), '', 0);
