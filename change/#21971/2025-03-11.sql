-- 订单管理
update bwc_authority_temp as t set t.`group`='order' where t.`key`='order' and t.type=0;
update bwc_authority_temp as t1
    join (
    select id from bwc_authority_temp where `key`='order'
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='order';
update bwc_authority_temp as t1
    join (
    select t3.id from bwc_authority_temp as t3
    join (select id from bwc_authority_temp where `key`='order') as t4
    on t3.parent_id=t4.id and t3.type=1
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='order';

update bwc_authority_temp as t set t.`group`='order' where t.`key`='mt_order' and t.type=0;
update bwc_authority_temp as t1
    join (
    select id from bwc_authority_temp where `key`='mt_order'
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='order';
update bwc_authority_temp as t1
    join (
    select t3.id from bwc_authority_temp as t3
    join (select id from bwc_authority_temp where `key`='mt_order') as t4
    on t3.parent_id=t4.id and t3.type=1
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='order';
update bwc_authority_temp as t set t.`group`='order' where t.`key`='order_audit_list' and t.type=0;
update bwc_authority_temp as t1
    join (
    select id from bwc_authority_temp where `key`='order_audit_list'
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='order';
update bwc_authority_temp as t1
    join (
    select t3.id from bwc_authority_temp as t3
    join (select id from bwc_authority_temp where `key`='order_audit_list') as t4
    on t3.parent_id=t4.id and t3.type=1
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='order';
update bwc_authority_temp as t set t.`group`='order' where t.`key`='OrderPayout' and t.type=0;
update bwc_authority_temp as t1
    join (
    select id from bwc_authority_temp where `key`='OrderPayout'
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='order';
update bwc_authority_temp as t1
    join (
    select t3.id from bwc_authority_temp as t3
    join (select id from bwc_authority_temp where `key`='OrderPayout') as t4
    on t3.parent_id=t4.id and t3.type=1
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='order';
-- 任务管理
update bwc_authority_temp as t set t.`group`='task' where t.`key`='task' and t.type=0;
update bwc_authority_temp as t1
    join (
    select id from bwc_authority_temp where `key`='task'
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='task';
update bwc_authority_temp as t1
    join (
    select t3.id from bwc_authority_temp as t3
    join (select id from bwc_authority_temp where `key`='task') as t4
    on t3.parent_id=t4.id and t3.type=1
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='task';
update bwc_authority_temp as t set t.`group`='task' where t.`key`='taskCycleCreate' and t.type=0;
update bwc_authority_temp as t1
    join (
    select id from bwc_authority_temp where `key`='taskCycleCreate'
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='task';
update bwc_authority_temp as t1
    join (
    select t3.id from bwc_authority_temp as t3
    join (select id from bwc_authority_temp where `key`='taskCycleCreate') as t4
    on t3.parent_id=t4.id and t3.type=1
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='task';
update bwc_authority_temp as t set t.`group`='task' where t.`key`='task_views' and t.type=0;
update bwc_authority_temp as t1
    join (
    select id from bwc_authority_temp where `key`='task_views'
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='task';
update bwc_authority_temp as t1
    join (
    select t3.id from bwc_authority_temp as t3
    join (select id from bwc_authority_temp where `key`='task_views') as t4
    on t3.parent_id=t4.id and t3.type=1
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='task';
-- 运营
update bwc_authority_temp as t set t.`group`='operate' where t.`key`='agent_activity' and t.type=0;
update bwc_authority_temp as t1
    join (
    select id from bwc_authority_temp where `key`='agent_activity'
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='operate';
update bwc_authority_temp as t1
    join (
    select t3.id from bwc_authority_temp as t3
    join (select id from bwc_authority_temp where `key`='agent_activity') as t4
    on t3.parent_id=t4.id and t3.type=1
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='operate';
update bwc_authority_temp as t set t.`group`='operate' where t.`key`='urban_activity' and t.type=0;
update bwc_authority_temp as t1
    join (
    select id from bwc_authority_temp where `key`='urban_activity'
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='operate';
update bwc_authority_temp as t1
    join (
    select t3.id from bwc_authority_temp as t3
    join (select id from bwc_authority_temp where `key`='urban_activity') as t4
    on t3.parent_id=t4.id and t3.type=1
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='operate';
update bwc_authority_temp as t set t.`group`='operate' where t.`key`='urban_activity_total' and t.type=0;
update bwc_authority_temp as t1
    join (
    select id from bwc_authority_temp where `key`='urban_activity_total'
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='operate';
update bwc_authority_temp as t1
    join (
    select t3.id from bwc_authority_temp as t3
    join (select id from bwc_authority_temp where `key`='urban_activity_total') as t4
    on t3.parent_id=t4.id and t3.type=1
    ) as t2
on t1.parent_id=t2.id
    set t1.`group`='operate';
-- 新增crm权限
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
values('crm列表', 'crm', 0, '', 0, 'business', 'crm列表');
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        'crm列表',
        'crm_list',
        1,
        '',
        t.id,
        'business',
        'crm列表组件'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'crm'
      AND type = 0
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        'crm列表',
        'Crm.get_list',
        2,
        'GET',
        t.id,
        'business',
        'crm列表接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'crm_list'
      AND type = 1
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '查看手机号',
        'Crm.get_mobile',
        2,
        'GET',
        t.id,
        'business',
        '查看手机号接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'crm_list'
      AND type = 1
);

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '店铺信息',
        'crm_store_list',
        1,
        '',
        t.id,
        'business',
        '店铺信息组件'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'crm'
      AND type = 0
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '店铺信息',
        'Crm.store_list',
        2,
        'GET',
        t.id,
        'business',
        '店铺信息接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'crm_store_list'
      AND type = 1
);

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '跟进信息',
        'crm_follow_list',
        1,
        '',
        t.id,
        'business',
        '跟进信息组件'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'crm'
      AND type = 0
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '跟进信息',
        'Crm.follow_list',
        2,
        'GET',
        t.id,
        'business',
        '跟进信息接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'crm_follow_list'
      AND type = 1
);

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '添加跟进信息',
        'add_crm_follow_list',
        1,
        '',
        t.id,
        'business',
        '添加跟进信息组件'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'crm'
      AND type = 0
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '添加跟进信息',
        'Crm.add_follow_up',
        2,
        'POST',
        t.id,
        'business',
        '添加跟进信息接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'add_crm_follow_list'
      AND type = 1
);

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '标记流失用户',
        'crm_tag_lost_users',
        1,
        '',
        t.id,
        'business',
        '标记流失用户组件'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'crm'
      AND type = 0
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '标记流失用户',
        'Crm.tag_lost_users',
        2,
        'POST',
        t.id,
        'business',
        '标记流失用户接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'crm_tag_lost_users'
      AND type = 1
);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) (
    SELECT
        '取消标记流失用户',
        'Crm.cancel_tag_lost_users',
        2,
        'POST',
        t.id,
        'business',
        '取消标记流失用户接口'
    FROM
        bwc_authority_temp AS t
    WHERE
            t.KEY = 'crm_tag_lost_users'
      AND type = 1
);

-- 商家跟进纪录表
CREATE TABLE `bwc_seller_follow_up`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `seller_id`   int(11) NOT NULL DEFAULT '0' COMMENT '商家表id',
    `content`     text      NOT NULL COMMENT '跟进内容',
    `release_num` int(11) NOT NULL DEFAULT '0' COMMENT '商家放单次数',
    `create_by`   int(11) NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_by`   int(11) NOT NULL DEFAULT '0' COMMENT '修改人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
    PRIMARY KEY (`id`),
    KEY           `idx_seller_id` (`seller_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家跟进记录表';
-- 商家关联流失原因表
CREATE TABLE `bwc_seller_tag_lost`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `seller_id`   int(11) NOT NULL DEFAULT '0' COMMENT '商家表id',
    `reason_id`   int(11) NOT NULL COMMENT '1店铺倒闭 2商家认为放单效果不好找竞对了 3封控原因不合作 4改使用官方霸王餐放单',
    `reason`      text      NOT NULL COMMENT '原因',
    `create_by`   int(11) NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_by`   int(11) NOT NULL DEFAULT '0' COMMENT '修改人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
    PRIMARY KEY (`id`),
    KEY           `idx_seller_id` (`seller_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标记为流失用户表';
-- 放单信息等统计表
CREATE TABLE `bwc_seller_statistics`
(
    `id`                          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `seller_id`                   int(10) NOT NULL DEFAULT '0',
    `last_release_time`           datetime                DEFAULT NULL COMMENT '最近放单时间',
    `total_release_number`        int(11) DEFAULT '0' COMMENT '全部放单次数',
    `total_seller_bill_fee`       decimal(10, 2)          DEFAULT NULL COMMENT '全部账单金额',
    `consumption_interval_score`  decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '消费间隔得分',
    `consumption_frequency_score` decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '消费频率得分',
    `consumption_amount_score`    decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '消费金额得分',
    `score_update_time`           datetime                DEFAULT NULL COMMENT '分数变更时间',
    `today_has_activity`          tinyint(1) NOT NULL DEFAULT '0' COMMENT '今日是否放活动：0未放，1已放，每日0点更新',
    `follow_day`                  timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最新一次跟进时间',
    `data_state`                  tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`                 timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                 timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                           `idx_seller_id` (`seller_id`) USING BTREE,
    KEY                           `idx_follow_day` (`follow_day`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家放单信息等统计';
-- 创建分类高峰期表
CREATE TABLE `bwc_store_category_peak_period`
(
    `id`                int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `store_category_id` int(11) NOT NULL DEFAULT '0' COMMENT '店铺分类id',
    `peak_period`       varchar(256) NOT NULL DEFAULT '' COMMENT '高峰期时段',
    `create_by`         int(11) NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_by`         int(11) NOT NULL DEFAULT '0' COMMENT '修改人',
    `gmt_created`       timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺分类高峰期';
-- 初始化标签的高峰期
INSERT INTO `bwc_store_category_peak_period` (`store_category_id`, `peak_period`) VALUES (1, '6:30-9:00');
INSERT INTO `bwc_store_category_peak_period` (`store_category_id`, `peak_period`) VALUES (2, '10:00-12:00,17:30-20:30');
INSERT INTO `bwc_store_category_peak_period` (`store_category_id`, `peak_period`) VALUES (3, '21:00-2:00');
INSERT INTO `bwc_store_category_peak_period` (`store_category_id`, `peak_period`) VALUES (4, '7:30-9:30,14:00-17:00,18:00-21:00');
INSERT INTO `bwc_store_category_peak_period` (`store_category_id`, `peak_period`) VALUES (5, '14:00-17:00,19:00-22:00');
INSERT INTO `bwc_store_category_peak_period` (`store_category_id`, `peak_period`) VALUES (6, '18:00-21:00');
INSERT INTO `bwc_store_category_peak_period` (`store_category_id`, `peak_period`) VALUES (7, '7:00-9:00,17:00-20:00');


-- java用到的表
CREATE TABLE if not exists `bwc_merchant_settled_follow_up`
(
    `id`                  int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `merchant_settled_id` int(11)          NOT NULL DEFAULT '0' COMMENT '商家入驻申请记录id',
    `content`             text             NOT NULL COMMENT '跟进内容',
    `create_by`           int(11)          NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_by`           int(11)          NOT NULL DEFAULT '0' COMMENT '修改人',
    `gmt_created`         timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`        timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete`           tinyint(1)       NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
    PRIMARY KEY (`id`),
    KEY `idx_merchant_settled` (`merchant_settled_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家入驻申请跟进记录表';

-- 权限
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group` ) select '跟进记录','merchant_settled_follow_up',1,'',(select id from bwc_authority_temp where `key` = 'merchant' ),'log';
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group` ) select '跟进记录明细接口','merchantSettled.follow.up.detail',2,'GET',(select id from bwc_authority_temp where `key` = 'merchant_settled_follow_up' ),'log';
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group` ) select '跟进记录新增接口','merchantSettled.follow.up',2,'POST',(select id from bwc_authority_temp where `key` = 'merchant_settled_follow_up' ),'log';

CREATE TABLE `bwc_seller_tag`
(
    `id`           int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `tag_name`     varchar(64)      NOT NULL DEFAULT '' COMMENT '标签名字',
    `tag_color`    varchar(32)      NOT NULL DEFAULT '' COMMENT '标签颜色',
    `tag_level`    tinyint(1)       NOT NULL DEFAULT '0' COMMENT '标签等级 0:红色 1:橙色 2:蓝色 3:绿色 4:灰色',
    `tag_desc`     varchar(256)     NOT NULL DEFAULT '' COMMENT '标签说明',
    `rs_condition` tinyint(1)       NOT NULL DEFAULT '0' COMMENT 'rs条件 0:小于 1:大于',
    `fs_condition` tinyint(1)       NOT NULL DEFAULT '0' COMMENT 'fs条件 0:小于 1:大于',
    `ms_condition` tinyint(1)       NOT NULL DEFAULT '0' COMMENT 'ms条件 0:小于 1:大于',
    `spel_rule`    varchar(1024)    NOT NULL DEFAULT '' COMMENT 'spel规则表达式',
    `create_by`    int(11)          NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_by`    int(11)          NOT NULL DEFAULT '0' COMMENT '修改人',
    `gmt_created`  timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete`    tinyint(1)       NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='商家标签表';

CREATE TABLE `bwc_seller_tag_relation`
(
    `id`            int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `seller_id`     int(11)          NOT NULL DEFAULT '0' COMMENT '商家id',
    `seller_tag_id` int(11)          NOT NULL DEFAULT '0' COMMENT '商家标签id',
    `create_by`     int(11)          NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_by`     int(11)          NOT NULL DEFAULT '0' COMMENT '修改人',
    `gmt_created`   timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`  timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete`     tinyint(1)       NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
    PRIMARY KEY (`id`),
    KEY `idx_seller_tag_id` (`seller_tag_id`),
    KEY `idx_seller_id` (`seller_id`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='商家标签关联表';

-- 初始化数据
insert into bwc_seller_tag (tag_name,tag_level,tag_desc,rs_condition,fs_condition,ms_condition,spel_rule) values ('高价值用户',0,'最近消费日期较近、消费频次较高、消费金额较高',1,1,1,'#rs >= #rsAverage and #fs >= #fsAverage and #ms >= #msAverage');
insert into bwc_seller_tag (tag_name,tag_level,tag_desc,rs_condition,fs_condition,ms_condition,spel_rule) values ('重点保持用户',1,'最近消费日期较远，消费频次较高，消费金额较高',0,1,1,'#rs < #rsAverage and #fs >= #fsAverage and #ms >= #msAverage');
insert into bwc_seller_tag (tag_name,tag_level,tag_desc,rs_condition,fs_condition,ms_condition,spel_rule) values ('重点发展用户',1,'最近消费日期较近，消费金额较高，但是消费频次不高',1,0,1,'#rs >= #rsAverage and #fs < #fsAverage and #ms >= #msAverage');
insert into bwc_seller_tag (tag_name,tag_level,tag_desc,rs_condition,fs_condition,ms_condition,spel_rule) values ('重点挽留用户',1,'最近消费日期较远，消费频次较低，但是消费金额较高',0,0,1,'#rs < #rsAverage and #fs < #fsAverage and #ms >= #msAverage');
insert into bwc_seller_tag (tag_name,tag_level,tag_desc,rs_condition,fs_condition,ms_condition,spel_rule) values ('一般价值用户',2,'最近消费日期较近、消费频次较高、消费金额较不高',1,1,0,'#rs >= #rsAverage and #fs >= #fsAverage and #ms < #msAverage');
insert into bwc_seller_tag (tag_name,tag_level,tag_desc,rs_condition,fs_condition,ms_condition,spel_rule) values ('一般保持用户',2,'最近消费日期较远、消费频次较高、消费金额不高',0,1,0,'#rs < #rsAverage and #fs >= #fsAverage and #ms < #msAverage');
insert into bwc_seller_tag (tag_name,tag_level,tag_desc,rs_condition,fs_condition,ms_condition,spel_rule) values ('一般发展用户',2,'最近消费日期较近、消费频次不高、消费金额不高',1,0,0,'#rs >= #rsAverage and #fs < #fsAverage and #ms < #msAverage');
insert into bwc_seller_tag (tag_name,tag_level,tag_desc,rs_condition,fs_condition,ms_condition,spel_rule) values ('潜在用户',3,'最近消费日期较远、消费频次不高、消费金额不高',0,0,0,'#rs < #rsAverage and #fs < #fsAverage and #ms < #msAverage');