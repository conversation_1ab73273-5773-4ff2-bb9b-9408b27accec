CREATE TABLE `bwc_seller_bill_order_mt`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `seller_bill_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '账单id',
    `order_id`       int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
    `data_state`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time`    timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_seller_bill_id` (`seller_bill_id`),
    KEY              `idx_order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='美团账单关联订单表';

-- ALTER TABLE `bwc_order_mt` ADD INDEX `idx_type_completion_time` (`type`, `completion_time`) USING BTREE;
-- ALTER TABLE `bwc_order_mt`
--     ADD COLUMN `settlement` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '1已结算，其他未结算' AFTER `agent_income`;
ALTER TABLE `bwc_order_mt`
    ADD COLUMN `settlement` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '1已结算，其他未结算' AFTER `agent_income`,
ADD INDEX `idx_type_completion_time` (`type`, `completion_time`) USING BTREE;

-- 注意语句

INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`) VALUES (NULL, 'string', '新版美团商家账单日期', 'mt_seller_bill_date',  "2025-02-07", '', 0);

ALTER TABLE `bwc_excel_download_task`
    MODIFY COLUMN `excel_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '1.全部订单,2.未结算订单,3.商家账单批量导出excel,4.商家账单导出excel,5.发账单导出excel,6.发店铺账单导出excel,7.美团霸王餐账单导出excel,8.导出吃吃龟格式的excel,9.导出吃吃龟未结算账单,10.导出吃吃龟多个商家excel的zip,11.任务excel,13.订单明细导出,14.代理数据统计导出,15.提成结算导出,16.数据统计导出,17.提现账号excel,18.订单审核记录excel,19.修改账单记录excel,20.任务修改记录excel,21.工单中心-工单列表-工单明细导出,22.工单中心-明细统计-工作量明细导出,23.饿了么账单excel,24.推广-推广分组-查询数据-推广数据导出,25.财务-商家退款记录导出,26.饿了么专享账单excel,27.商务-线索管理-列表数据导出,28.灰太狼账单excel,29.店铺开票支付记录,30.标记店铺每日账单导出,31.批量订单行为导出， 32：商家端招商加盟，33. 美团对账单' AFTER `task_finish_time`;
