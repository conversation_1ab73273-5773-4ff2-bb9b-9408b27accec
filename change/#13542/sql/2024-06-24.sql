-- 创建user_order_switch_ctrl表
CREATE TABLE `bwc_user_order_switch_ctrl` (
                                              `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                              `order_switch_way_ctrl` tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单开关切换方式 0默认; 1开启; 2关闭',
                                              `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '操作人id',
                                              `user_id` int(11) NOT NULL COMMENT '用户id',
                                              `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                              `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 修改order_verify表
ALTER TABLE `bwc_order_verify`
    ADD COLUMN `order_switch_way_ctrl` TINYINT ( 1 ) NOT NULL DEFAULT 0 COMMENT '订单开关切换方式 0默认; 1开启; 2关闭' AFTER `is_server_proxy`;

-- 切换开关全局控制
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
( 'string', '切换方式开关', 'global_order_switch_way_ctrl', '2', '', 0 );

-- 增加订单切换方式开关版本
INSERT INTO `bwc_version_compatible` ( `version_name`, `min_version`, `version_desc`, `data_state`, `create_time`, `update_time` )
VALUES
( 'order_switch_way_ctrl_v', 20250, '订单切换方式开关版本', 0, '2024-06-24 10:50:20', '2024-06-24 10:50:20' );



INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description` )
SELECT '用户传图审核订单', 'user_order_switch_way_ctrl', 1, '', t.`id`, 'user', '用户传图审核订单'
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'user' AND `type` = 0) AS t;

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description` )
SELECT '用户传图审核订单', 'user.order_switch_way_ctrl', 2, 'POST', `t`.`id`, 'user', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'user_order_switch_way_ctrl' AND `type` = 1) AS t;
