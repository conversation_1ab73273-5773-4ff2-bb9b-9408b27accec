CREATE TABLE `bwc_store_category`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name`        varchar(255)       DEFAULT NULL COMMENT '品类名称',
    `description` text COMMENT '品类描述',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='店铺品类';

CREATE TABLE `bwc_store_category_relation`
(
    `id`          INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `store_id`    INT(11) UNSIGNED NOT NULL COMMENT '店铺ID，关联bwc_store.id',
    `category_id` INT(11) UNSIGNED NOT NULL COMMENT '品类ID，关联bwc_store_category.id',
    `data_state`  TINYINT(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `idx_store_id` (`store_id`) USING BTREE,
    KEY           `idx_category_id` (`category_id`) USING BTREE
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='店铺与品类关联表';

CREATE TABLE `bwc_store_category_tag_relation`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `store_id`    int(11) unsigned NOT NULL COMMENT '店铺ID，关联bwc_store.id',
    `category_tag_id` int(11) unsigned NOT NULL COMMENT '品类标签ID，关联bwc_store_category_tag.id',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `idx_store_id` (`store_id`) USING BTREE,
    KEY           `idx_category_tag_id` (`category_tag_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='店铺与品类标签关联表';

INSERT INTO `bwc_store_category` (`id`, `name`, `description`, `data_state`, `create_time`, `update_time`)
VALUES (1, '早餐', '早餐类食品', 0, '2024-09-06 11:09:17', '2024-09-06 11:09:17');
INSERT INTO `bwc_store_category` (`id`, `name`, `description`, `data_state`, `create_time`, `update_time`)
VALUES (2, '正餐', '正餐类食品', 0, '2024-09-06 11:09:17', '2024-09-06 11:09:17');
INSERT INTO `bwc_store_category` (`id`, `name`, `description`, `data_state`, `create_time`, `update_time`)
VALUES (3, '夜宵', '夜宵类食品', 0, '2024-09-06 11:09:17', '2024-09-06 11:09:17');
INSERT INTO `bwc_store_category` (`id`, `name`, `description`, `data_state`, `create_time`, `update_time`)
VALUES (4, '饮品', '各类饮品', 0, '2024-09-06 11:09:17', '2024-09-06 11:09:17');
INSERT INTO `bwc_store_category` (`id`, `name`, `description`, `data_state`, `create_time`, `update_time`)
VALUES (5, '甜品', '甜品和点心', 0, '2024-09-06 11:09:17', '2024-09-06 11:09:17');
INSERT INTO `bwc_store_category` (`id`, `name`, `description`, `data_state`, `create_time`, `update_time`)
VALUES (6, '零售', '零售商品', 0, '2024-09-06 11:09:17', '2024-09-06 11:09:17');


CREATE TABLE `bwc_category_tag`
(
    `id`          INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `category_id` INT(11) UNSIGNED NOT NULL COMMENT '品类ID，关联bwc_store_category.id',
    `tag`         VARCHAR(255) NOT NULL COMMENT '品类标签',
    `data_state`  TINYINT(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `idx_category_id` (`category_id`) USING BTREE
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='品类与品类标签的对应表';

-- 插入品类与品类标签数据
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (1, 1, '包子粥铺', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (2, 1, '饺子馄饨', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (3, 1, '饭团早点', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (4, 2, '快餐便当', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (5, 2, '汉堡薯条', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (6, 2, '意面披萨', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (7, 2, '地方菜系', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (8, 2, '能量西餐', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (9, 2, '夹馍饼类', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (10, 2, '日韩料理', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (11, 2, '香锅干锅', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (12, 2, '异国料理', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (13, 2, '火锅冒菜', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (14, 2, '米粉面馆', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (15, 2, '轻食沙拉', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (16, 2, '东南亚菜', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (17, 3, '烧烤夜宵', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (18, 3, '特色小吃', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (19, 3, '炸鸡炸串', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (20, 3, '鸭脖卤味', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (21, 4, '奶茶果汁', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (22, 4, '咖啡', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (23, 5, '面包蛋糕', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (24, 5, '冰凉甜品', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (25, 5, '生日蛋糕', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (26, 5, '水果捞', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (27, 5, '中西糕点', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (28, 6, '水果果切', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (29, 6, '鲜花礼品', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (30, 6, '超市便利', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (31, 6, '生鲜菜市场', 0, '2024-09-23 13:42:02', '2024-09-23 13:42:02');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (32, 6, '宠物用品', 0, '2024-09-23 13:42:02', '2024-10-22 16:54:18');
INSERT INTO `bwc_category_tag` (`id`, `category_id`, `tag`, `data_state`, `create_time`, `update_time`) VALUES (33, 6, '看病买药', 0, '2024-09-23 13:42:02', '2024-10-22 16:54:28');