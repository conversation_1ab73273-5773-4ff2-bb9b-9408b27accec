ALTER TABLE `bwc_middleman`
    ADD COLUMN `single_withdrawal_min_amount` decimal(10,2) unsigned NOT NULL DEFAULT '100.00' COMMENT '单笔提现最小金额';
UPDATE `bwc_middleman`
SET `single_withdrawal_min_amount` = 100.00;
-- 权限
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (NULL, '修改中间人单笔提现最小金额', 'edit_single_withdrawal_min_amount', 1, '', 19, 'business', '', 2, 0, '2023-11-07 15:37:38', '2023-11-07 15:41:34');
