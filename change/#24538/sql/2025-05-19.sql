-- 仅仅修改type字段备注信息
ALTER TABLE `bwc_agent_billing_record`
    MODIFY COLUMN `type` TINYINT ( 3 ) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型 1充值，2短信通知扣费，3短信验证码扣费，4用户返现扣费，5邀请奖励扣费，6机审接口付费，7每单服务费，8中间人打款，9管理员扣款，10渠道推广分佣，11渠道推广退款，12商家支付打款给代理，13代理提现，14代理提现驳回，15APP推送通知，16ai外呼，17全区域分成扣费，18全区域分成收入，19活动完成-下单返利，20活动完成-邀请返利，21订单取消扣除积分，22订单扣除积分，23订单扣除金币，24订单取消扣除团员奖励，25订单取消扣除下单返利活动奖励，26订单取消扣除邀请返利活动奖励，27订单增加积分，28订单增加金币，29订单恢复增加积分，30订单恢复增加团员奖励，31订单恢复增加下单返利活动奖励，32订单恢复增加邀请返利活动奖励，33营销推送，34商家退款-扣费，35运营短信，36自动任务-APP推送，37自动任务-短信，38渠道推广分佣（上级），39渠道推广退款（上级），40评价卡返现支出-线上，41评价卡市场部拉新提成-扣费，42评价卡商家佣金支出-线上，43新人通用大牌返现支出，44订单助力加返-增加用户积分，45评价卡经纪人支出-线上，46订单助力加返-扣除用户积分，47助力领现金现金任务-扣除代理，48助力领现金-可提现金币-扣除代理，49经纪人-扣费，50手动增加中间人余额-扣除代理' AFTER `agent_id`;