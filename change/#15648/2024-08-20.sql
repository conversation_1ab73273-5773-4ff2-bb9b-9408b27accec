CREATE TABLE `bwc_seller_bill_change_status`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT,
    `seller_bill_id` int(11) NOT NULL DEFAULT '0' COMMENT '账单表id',
    `change_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '账单修改状态：0未修改，1有改账，2有改账被刷账',
    `task_id`        int(11) NOT NULL DEFAULT '0' COMMENT '账单对应的任务id',
    `data_state`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`    timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`    timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY              `idx_seller_bill_id` (`seller_bill_id`) USING BTREE,
    KEY              `idx_create_time` (`create_time`) USING BTREE,
    KEY              `idx_task_id` (`task_id`) USING BTREE,
    KEY              `idx_change_status` (`change_status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家账单修改/刷新状态表';