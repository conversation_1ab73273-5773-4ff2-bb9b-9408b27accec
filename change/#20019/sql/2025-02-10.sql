-- 增加order_mt_gp表
CREATE TABLE `bwc_order_mt_gp` (
                                   `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                   `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
                                   `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
                                   `type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '类型 1美团团购; 2点评团购',
                                   `take_out_order_no` varchar(255) NOT NULL DEFAULT '' COMMENT '外卖订单号',
                                   `cash_back_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '返现类型：1：统一返现 0：独立返现',
                                   `product_index_id` varchar(255) NOT NULL DEFAULT '' COMMENT '团单标识id',
                                   `mt_phone` varchar(11) NOT NULL DEFAULT '' COMMENT '用户美团手机号',
                                   `poi_event_id` varchar(50) NOT NULL DEFAULT '' COMMENT '美团活动id',
                                   `user_event_id` varchar(255) NOT NULL DEFAULT '' COMMENT '用户美团任务 id',
                                   `seller_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商家名称，同店铺名称',
                                   `cover` varchar(255) NOT NULL DEFAULT '' COMMENT 'poi 头图',
                                   `address` varchar(255) NOT NULL DEFAULT '' COMMENT '⻔店地址, 包含交叉路',
                                   `meal_name` varchar(255) NOT NULL DEFAULT '' COMMENT '团单名称',
                                   `meal_cover` varchar(255) NOT NULL DEFAULT '' COMMENT '团单封面',
                                   `category_name` varchar(50) NOT NULL DEFAULT '' COMMENT '主营品类名称',
                                   `mt_score` varchar(255) NOT NULL DEFAULT '' COMMENT '美团评分',
                                   `pay_price` int(11) NOT NULL DEFAULT '0' COMMENT '⽀付价, 优惠后价格, 单位分',
                                   `market_price` int(11) NOT NULL DEFAULT '0' COMMENT '原价, 单位分',
                                   `subsidy_ratio` int(11) NOT NULL DEFAULT '0' COMMENT '渠道补贴比例，万分比，默认为 0，补贴⽐例最大为渠道结佣的 100% 即补贴范围：[0, 10000] = [0%,100%]',
                                   `unified_cash_back_headquarter_ratio` int(11) NOT NULL DEFAULT '0' COMMENT '统一返现总部抽成比例（万分比）',
                                   `unified_cash_back_agent_ratio` int(11) NOT NULL DEFAULT '0' COMMENT '统一返现代理抽成比例（万分比）',
                                   `org_user_ratio` int(11) NOT NULL DEFAULT '0' COMMENT '原始⽤户佣⾦⽐例',
                                   `org_media_ratio` int(11) NOT NULL DEFAULT '0' COMMENT '原始渠道佣⾦⽐例',
                                   `user_ratio` int(11) NOT NULL DEFAULT '0' COMMENT '⽤户佣⾦⽐例（叠加渠道补贴后的结果）',
                                   `user_comment_ratio` int(11) NOT NULL DEFAULT '0' COMMENT '⽤户追加评价佣金比例，直接展示给⽤户',
                                   `media_ratio` int(11) NOT NULL DEFAULT '0' COMMENT '渠道佣⾦⽐例（扣减渠道补贴后的结果）',
                                   `user_max_commission` int(11) NOT NULL DEFAULT '0' COMMENT '⽤户佣⾦上限（叠加渠道补贴后的结果）',
                                   `media_max_commission` int(11) NOT NULL DEFAULT '-1' COMMENT '渠道佣⾦上限（扣减渠道补贴后的结果）',
                                   `order_limit_exact_time` varchar(20) NOT NULL DEFAULT '' COMMENT '⽤户任务结束时间，格式：毫秒字符串',
                                   `cancel_time` int(11) NOT NULL DEFAULT '0' COMMENT '⾃动取消时间,报名后多⻓时间内必须下单，单位：分',
                                   `consume_time` int(11) NOT NULL DEFAULT '0' COMMENT '下单后多⻓时间内必须核销，单位：天,计算⽅式：报名时间 + x ⽇的 23:59:59',
                                   `delay_cash_back_time` int(11) NOT NULL DEFAULT '0' COMMENT '延迟返现时间，单位：小时(完成后 + x ⼩时)',
                                   `comment_time` int(11) NOT NULL DEFAULT '0' COMMENT '核销后多⻓时间内必须评价，单位：天,计算⽅式：核销时间 + x ⽇的 23:59:59',
                                   `comment_text_length` int(11) NOT NULL DEFAULT '0' COMMENT '评价字数要求',
                                   `comment_picture_count` int(11) NOT NULL DEFAULT '0' COMMENT '评价图⽚数量要求',
                                   `action_url` text NOT NULL COMMENT '用户行为链接',
                                   `real_charge` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实付金额',
                                   `task_receipt_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '接单价格',
                                   `agent_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '代理收入',
                                   `order_time` int(11) NOT NULL DEFAULT '0' COMMENT '下单时间，从美团回调推送中取得',
                                   `task_start_time` varchar(13) NOT NULL DEFAULT '' COMMENT '用户任务开始时间，从美团回调推送中取得',
                                   `task_end_time` varchar(13) NOT NULL DEFAULT '' COMMENT '用户任务结束时间，从美团回调推送中取得',
                                   `is_hx` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否核销 0否 1是',
                                   `is_hx_cash_back` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否核销返现 0否 1是',
                                   `hx_time` int(11) NOT NULL DEFAULT '0' COMMENT '核销完成时间，收到美团第一次推送code=100的时候',
                                   `hx_cash_back` int(11) NOT NULL DEFAULT '0' COMMENT '核销返现金额，单位：分',
                                   `hx_expected_cash_back_time` int(11) NOT NULL DEFAULT '0' COMMENT '核销返现预计到账时间',
                                   `is_fk` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否反馈 0否 1是',
                                   `is_fk_cash_back` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否反馈返现 0否 1是',
                                   `fk_time` int(11) NOT NULL DEFAULT '0' COMMENT '反馈完成时间，收到美团第二次推送code=100的时候',
                                   `fk_cash_back` int(11) NOT NULL DEFAULT '0' COMMENT '反馈返现金额',
                                   `fk_expected_cash_back_time` int(11) NOT NULL DEFAULT '0' COMMENT '反馈返现预计到账时间',
                                   `fail_reason_msg` varchar(255) NOT NULL DEFAULT '' COMMENT '失败原因',
                                   `code` int(11) NOT NULL DEFAULT '0' COMMENT 'code值；1已报名; 2 已下单; 3已核销，待返现；4已返现；5已反馈，待返现；6已完成；7反馈不达标',
                                   `longitude` varchar(20) NOT NULL DEFAULT '' COMMENT '经度',
                                   `latitude` varchar(20) NOT NULL DEFAULT '' COMMENT '纬度',
                                   `distance` double(10,2) NOT NULL DEFAULT '0.00' COMMENT '下单时的距离',
                                   `client_ip` varchar(30) NOT NULL DEFAULT '' COMMENT '客户端 Ip(⽤户⻛控校验)',
                                   `mtg_gp_push_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '美团官方团购推送时间戳，毫秒级',
                                   `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
                                   `completion_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
                                   `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   PRIMARY KEY (`id`),
                                   KEY `idx_order_id` (`order_id`) USING BTREE,
                                   KEY `idx_type_completion_time` (`type`,`completion_time`) USING BTREE,
                                   KEY `idx_mt_phone_product_index_id` (`order_id`,`product_index_id`,`mt_phone`,`data_state`) USING BTREE,
                                   KEY `idx_user_event_id` (`user_event_id`),
                                   KEY `idx_create_time` (`create_time`),
                                   KEY `idx_order_id_type` (`order_id`,`type`,`data_state`,`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


-- 到店团购版本
INSERT INTO `bwc_version_compatible` ( `version_name`, `min_version`, `version_desc`, `data_state`, `create_time`, `update_time` )
VALUES
    ( 'in_store_gp_v', 20490, '美团官方团购', 0, '2025-02-10 14:42:59', '2025-02-10 14:47:30' );

-- 修改代理表(agent)
ALTER TABLE `bwc_agent` ADD COLUMN `mtg_gp_activity_enabled` TINYINT ( 1 ) NOT NULL DEFAULT 1 COMMENT '美团团购活动启用 1启用 2禁用' AFTER `mt_activity_enabled`,
    ADD COLUMN `mtg_gp_subsidy_ratio` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '美团团购统一返现渠道补贴比例（万分比）' AFTER `ele_feedback_main_deduct_ratio`,
    ADD COLUMN `mtg_gp_unified_cash_back_headquarter_ratio` INT ( 11 ) NOT NULL DEFAULT 5000 COMMENT '美团团购统一返现总部抽成比例（万分比）' AFTER `mtg_gp_subsidy_ratio`,
    ADD COLUMN `mtg_gp_unified_cash_back_agent_ratio` INT ( 11 ) NOT NULL DEFAULT 5000 COMMENT '美团团购统一返现代理抽成比例（万分比）' AFTER `mtg_gp_unified_cash_back_headquarter_ratio`;

-- 美团官方团购活动启用控制
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform`)
VALUES
    ( 'string', '美团官方团购活动启用控制', 'mtg_gp_act_enabled', '1', '', 0);

INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform`)
VALUES
    ( 'string', '美团团购订单弹窗内容', 'mtg_gp_popup_content', '本订单最多可以有两次返现，在平台核销完成订单后，可获取一次返现;核销后可反馈评价，反馈为您在店内消费的真实反馈，反馈后可再得到一次返现。您可自由决定是否参加【反馈再返】，若您超时未反馈，则仅获得核销完成的返现。', '', 0);

INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform`)
VALUES
    ( 'string', '美团团购滚动条活动内容', 'mtg_gp_scroll_content', '本订单最多可以有两次返现，在平台核销完成订单后，可获取一次返现;核销后可反馈评价，反馈为您在店内消费的真实反馈，反馈后可再得到一次返现。您可自由决定是否参加【反馈再返】，若您超时未反馈，则仅获得核销完成的返现。', '', 0);


INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
    ( 'string', '美团团购订单详情活动弹窗开关', 'mtg_gp_popup_switch', '1', '', 0 );


-- 增加美团团购配置
INSERT INTO `bwc_mt_config` ( `name`, `key`, `value`, `data_state`, `create_time`, `update_time` )
VALUES
    ( '美团团购p参数', 'mtg_gp_p', '78fe5ed7316847799d8d9ba21c8a5e7f', 0, '2025-02-11 14:12:31', '2025-02-11 14:12:31' );
INSERT INTO `bwc_mt_config` ( `name`, `key`, `value`, `data_state`, `create_time`, `update_time` )
VALUES
    ( '美团团购获取商家列表接口', 'mtg_gp_seller_list_url', 'https://offsiteact.meituan.com/act/daodian/get_list', 0, '2025-02-11 14:15:51', '2025-02-11 14:15:51' );
INSERT INTO `bwc_mt_config` ( `name`, `key`, `value`, `data_state`, `create_time`, `update_time` )
VALUES
    ( '美团团购活动报名接口', 'mtg_gp_create_order', 'https://offsiteact.meituan.com/act/daodian/open/mission', 0, '2025-02-11 14:17:25', '2025-02-11 14:17:25' );
INSERT INTO `bwc_mt_config` ( `name`, `key`, `value`, `data_state`, `create_time`, `update_time` )
VALUES
    ( '美团团购取消订单接口', 'mtg_gp_cancel_event_url', 'https://offsiteact.meituan.com/act/daodian/cancel/mission', 0, '2025-02-11 14:18:33', '2025-02-11 14:18:42' );
INSERT INTO `bwc_mt_config` ( `name`, `key`, `value`, `data_state`, `create_time`, `update_time` )
VALUES
    ( '查询订单状态接口', 'mtg_gp_query_event_progress_url', 'https://offsiteact.meituan.com/act/daodian/query/mission/progress', 0, '2025-02-11 14:19:36', '2025-02-11 14:19:36' );
INSERT INTO `bwc_mt_config` ( `name`, `key`, `value`, `data_state` )
VALUES
    ( '超时通知tp完成url', 'mtg_gp_timeout_notify_url', 'https://xxyx-public-api.xiaoxiaoyouxuan.com/mtg_gp/timeout_completed', 0 );

-- 增加美团团购平台
INSERT INTO `bwc_business_platform` (
    `platform_name`,
    `platform_abbreviation`,
    `platform_logo`,
    `main_color`,
    `contrast_color`,
    `task_details_advertisement_position`,
    `order_example_images_config`,
    `task_registration_instructions`,
    `task_matters_needing_attention`,
    `task_detailed_rules`,
    `task_detailed_rules_old`,
    `task_detailed_rules_order_limit`,
    `text_color`,
    `icon_bg_color`,
    `index_bg_image`,
    `activity_flow`,
    `order_flow`,
    `machine_audit`,
    `is_order_reminder`,
    `reminder_frequency`,
    `reminder_text`,
    `modify_frequency_time`,
    `order_submit_time`,
    `is_timeout_order_reminder`,
    `timeout_time`,
    `timeout_reminder_text`,
    `data_state`,
    `create_time`,
    `task_registration_instructions_order_limit`
)
VALUES
    (
        '美团团购',
        'mtg_gp',
        'https://image.xiaoxiaoyouxuan.com/admin/image_20241126101709e27b798e92181338.png',
        '#E8AA0C',
        '#0A0909',
        'mt_task_extension',
        '[{\"title\":\"团购订单号\",\"key\":\"take_out_order_no\",\"example\":\"https:\\/\\/image.xiaoxiaoyouxuan.com\\/admin\\/image_202203121709248211ac9e13.png\"},{\"title\":\"团购卷码\",\"key\":\"order_phone_tail\",\"example\":\"https:\\/\\/image.xiaoxiaoyouxuan.com\\/admin\\/image_202203121709282c0edfc8f2.png\"},{\"title\":\"订单截图\",\"key\":\"task_out_order_images\",\"example\":\"https:\\/\\/image.xiaoxiaoyouxuan.com\\/admin\\/image_2022031217091526b6eb218d.png\"},{\"title\":\"评价截图\",\"key\":\"task_out_evaluate_images\",\"example\":\"https:\\/\\/image.xiaoxiaoyouxuan.com\\/admin\\/image_20220312170911d80cc7d52b.png\"}]',
        '{\"evaluation_text\":\"报名手机号需与{highlightStart}美团注册账号一致{highlightEnd}的手机号，否则无法返现\\r\\n报名成功后，请不要修改美团账号绑定的手机号，否则无法返现\\r\\n需{highlightStart}先报名后下单{highlightEnd}，该活动仅识别报名后首单，{highlightStart}取消报名或取消美团订单{highlightEnd}以及{highlightStart}中途退款均无法返现{highlightEnd}\\r\\n订单完成后约{highlightStart}24小时返利至美团钱包-余额{highlightEnd}，请注意查收\\r\\n该活动需在报名后{cancelTime}分钟在美团完成下单，下单后{consumeTime}天内完成团单核销，超时名额会被取消，请及时前往平台完成下单。如您想额外得取反馈奖励需要在核销后{commentTime}天内在美团完成评价。\"}',
        '1. 当日名额当日订单有效，评价截图当晚12点之前提交，当晚10点之后报名的次日早上9点前提交评价截图，超时无积分奖励.\n2. 严禁再向商家索要任何额外返现，否则永久拉黑.\n3. 作业未按照商家要求（带图／文等）及未满五星点评，无积分奖励.\n4. 严禁下预定单指定时间的订单，商家未到营业时间严禁下单，否则无积分奖励.\n5. 漏送东西可以让商家下次补送不要退款，实际支付金额必须满足商家要求或因退款造成实付未满，则无积分奖励.\n6. 成功参与活动后，对商家的点评作业不可以删除，否则永久拉黑.\n7. 点评作业的图片必须是拍收到的餐品真实图片，不要拍到带有二维码或者用之前用过的图片，被平台屏蔽图片未显示的可能造成无积分奖励.\n8. 对于有门店新客要求的商家，之前在其店铺下过单的用户不可以参与活动，对于限制复购时间的商家请在时间间隔后参与活动，否则无积分奖励.\n9.  每天同一店铺的活动只能参与一次，多下无积分奖励.\n10.不是饮品店不要点一大堆饮料下单，违者无积分奖励.\n11.餐品问题可以取消活动报名，与商家协商处理.\n12.严禁私自联系商家，有问题找客服咨询后在说.',
        '{highlightWeightStart}*活动规则{highlightWeightEnd}：<br />①请{highlightStart}先报名后下单{highlightEnd}，确认报名成功后在下单<br />②神券省钱包、准时宝、优享送、开通平台会员、购买平台加量红包、“神抢手”活动券包及门店折扣券等产生的费用是不计入实付金额的，{highlightStart}实付未满会导致无法获得返利哦~{highlightEnd}<br />③下单前核对商家活动平台及名称地址是否一致，{highlightStart}下错平台或者店铺会导致无法获得返利哦{highlightEnd}<br /><br />{highlightWeightStart}*反馈要求{highlightWeightEnd}：<br />①上传截图需满足要求，不允许复制粘贴、灌水内容及与本次餐品无关内容提交反馈，不可删除评价，否则将收回本单相应积分<br />②若评价显示\"部分文字或图片已折叠隐藏展示\"或是反馈内容并非餐品实拍图，将扣除部分积分<br /><br />{highlightWeightStart}*报名间隔{highlightWeightEnd}：<br />一家店铺一天仅可报名一次，同个美团/饿了么账号也请勿连续下单，一个订单仅可提交一次<br /><br />{highlightWeightStart}*禁止行为{highlightWeightEnd}：<br />包含但不限于向商家索要返利、虚假订单、多平台返利、删除评价、多次实付不满、订单退款、恶意的非真实的反馈等，否则该账号将会使用受限！',
        '1.当日名额当日订单有效，评价截图当晚12点之前提交，当晚10点之后报名的次日早上9点前提交评价截图，超时无法返现。\r\n\r\n2.严禁私联商家索要任何额外返现，漏送除外，但收到返现金额不能超过订单金额的30%否则永久拉黑。\r\n\r\n3.作业未按照商家要求(例带图+文字+五星)及前端特殊要求提醒，无法返现。\r\n\r\n4.点评作业的图片必须是拍收到的餐品真实图片，个人原因被平台屏蔽图片未显示的可能造成无法返现（如：拍摄包装图片/图片相似/角度雷同/盗图等都会导致吞图）\r\n\r\n5.餐品问题可以取消活动报名，再与商家协商处理\r\n\r\n6.严禁一个订单重复参加活动！否则全网拉黑哦~\r\n2.严禁私联商家索要任何额外返现，漏送除外，但收到返现金额不能超过订单金额的30%否则永久拉黑。\r\n\r\n3.作业未按照商家要求(例带图+文字+五星)及前端特殊要求提醒，无积分奖励.\r\n\r\n4.点评作业的图片必须是拍收到的餐品真实图片，个人原因被平台屏蔽图片未显示的可能造成无积分奖励.（如：拍摄包装图片/图片相似/角度雷同/盗图等都会导致吞图）\r\n\r\n5.餐品问题可以取消活动报名，再与商家协商处理\r\n\r\n6.严禁一个订单重复参加活动！否则全网拉黑哦~',
        '{highlightWeightStart}*活动规则{highlightWeightEnd}：<br />①请{highlightStart}先报名后下单{highlightEnd}，确认报名成功后在下单<br />②团购平台各种红包、券包、及门店红包券包等产生抵扣的费用是不计入实付金额的，{highlightStart}实付未满会导致扣除对应金额哦~{highlightEnd}<br />③下单前核对商家活动平台及名称地址是否一致，{highlightStart}下错平台或者店铺会导致无法获得返利哦{highlightEnd}<br />④暂{highlightStart}不支持先团隔日后消费{highlightEnd}，参与活动订单的团购下单时间以及核销完成时间都仅限活动当日<br />⑤如部分退款，请截图退款详情一并上传，若导致实付不足将从奖励积分中扣除<br /><br />{highlightWeightStart}*提交规则{highlightWeightEnd}：<br />①订单号提交时间：<br />需在{highlightStart}报名成功后3h内提交单号{highlightEnd}，超时名额会取消<br />若订单超时取消，您将会被{highlightStart}扣除本单使用的晓晓红包{highlightEnd} 或 {highlightStart}后续所有订单（美团官方除外）均需在报名成功后{orderTimeoutMinutesTag}内{highlightEnd}提交订单号（优先扣除晓晓红包）<br />②反馈截图提交时间：<br />需在{highlightStart}当日24点前提交反馈截图{highlightEnd}（21点后报名的可次日凌晨3点前提交），超时未提交会扣除对应积分，次日13点前还未提交，会自动关闭订单<br /><br />{highlightWeightStart}*积分扣除规则{highlightWeightEnd}：<br />①反馈折叠 - 扣5元<br />②实付不满 - 扣不满部分（向上取整）<br />③24点前未提交反馈（21点后报名的除外) - 扣除2元<br /><br />{highlightWeightStart}*反馈要求{highlightWeightEnd}：<br />①上传截图需满足要求，不允许复制粘贴、灌水内容及与本次餐品无关内容提交反馈，不可删除评价，否则将收回本单相应积分<br />②若评价显示\"部分文字或图片已折叠隐藏展示\"或是反馈内容并非团购内容实拍图，将扣除部分积分<br /><br />{highlightWeightStart}*报名间隔{highlightWeightEnd}：<br />一家店铺一天仅可报名一次，同个团购平台账号也请勿连续下单，一个订单仅可提交一次<br /><br />{highlightWeightStart}*禁止行为{highlightWeightEnd}：<br />包含但不限于向商家索要返利、虚假订单、多平台返利、删除评价、多次实付不满、订单退款、恶意的非真实的反馈等，否则该账号将会使用受限，并可能导致活动无法获取返利！',
        '#0A0909',
        '#E8AA0C',
        'https://image.xiaoxiaoyouxuan.com/admin/image_202207111153444c65c32a9a.png',
        '{\"first\":{\"title\":\"抢名额，后团购\",\"content\":\"抢名额时，查看活动要求哦～\"},\"second\":{\"title\":\"团购下单并提交团购订单号\",\"content\":\"需在{highlightStart}报名后3小时内{highlightEnd}提交，{highlightStart}超时名额会被取消哦～{highlightEnd}\"},\"third\":{\"title\":\"到店并体验后提交订单反馈截图\",\"content\":\"请在当日内到店体验并核验完成订单，需在{highlightStart}当日24点前提交，当日24点后会减少2元，次日13点后将自动关闭订单{highlightEnd}\"},\"fourth\":{\"title\":\"根据要求完善信息后等待返现\",\"content\":\"{highlightStart}提现秒到账{highlightEnd}。<br><br>禁止事项：{highlightStart}禁止用户多平台领取返利及任何形式联系商家索要红包/返利，{highlightEnd}否则{highlightStart}视为用户严重违规，平台将予以全平台通报并取消该用户后续参与活动的权利{highlightEnd}\"}}',
        '{\"second\":{\"title\":\"去团购下单，锁定名额\",\"warm_prompt\":{\"title\":\"前往团购平台下单\",\"placeSkip\":{\"btnText\":\"前往美团外卖\",\"appId\":\"wx2c348cf579062e56\",\"originalId\":\"gh_72a4eb2d4324\",\"path\":\"packages/index/search/search\"},\"redEnvelopeTurnOn\":true,\"redEnvelopeSkip\":{\"btnText\":\"立即领取外卖红包\",\"appId\":\"wxde8ac0a21135c07d\",\"originalId\":\"gh_870576f3c6f9\",\"path\":\"/index/pages/h5/h5?weburl=https%3A%2F%2Fclick.meituan.com%2Ft%3Ft%3D1%26c%3D2%26p%3DH5VL975zSdI5&lch=cps:waimai:5:42355891f188691b1bfb3d6c48ccc1ff:001:33:441129&f_token=1&f_userId=1\"},\"content\":\"使用红包下单，享{highlightDarkRedStart}订单截图免上传{highlightDarkRedEnd}特权\"},\"order_no\":{\"title\":\"提交订单号\",\"content\":\"需要在{highlightStart}报名后3小时内{highlightEnd}提交，{highlightStart}超时视为自动放弃活动{highlightEnd}\",\"form\":{\"take_out_order_no\":{\"name\":\"团购订单号\",\"example_picture\":\"https://image.xiaoxiaoyouxuan.com/admin/image_2022083110175666566df82d.jpg\"}}}},\"third\":{\"title\":\"到店体验后提交订单和反馈截图\",\"content\":\"需要在{highlightStart}当日24点前{highlightEnd}提交，{highlightStart}当日24点后提交会减少2元，次日13点后将自动关闭订单{highlightEnd}。{highlightStart}注：反馈被折叠减少5元。{highlightEnd}\",\"content-late\":\"需要在{highlightStart}次日3点前{highlightEnd}提交，{highlightStart}次日13点后将自动关闭订单{highlightEnd}。{highlightStart}注：反馈被折叠减少5元。{highlightEnd}\",\"content-expired\":\"需要在{highlightStart}次日13点前{highlightEnd}提交，{highlightStart}超时视为自动放弃活动{highlightEnd}。{highlightStart}注：反馈被折叠减少5元。{highlightEnd}\",\"form\":{\"task_out_order_images\":{\"name\":\"订单完成截图（请勿长截图）\",\"example_picture\":\"https://image.xiaoxiaoyouxuan.com/admin/image_20231101115711f18eac174d.png\",\"example_picture2\":\"https://image.xiaoxiaoyouxuan.com/admin/image_20231101115711f18eac174d.png\",\"tip_picture_all\":\"已完成字样+店铺名+实付金额+订单号+下单时间\",\"tip_picture\":\"订单号+下单时间\",\"tip_picture2\":\"店铺名+实付金额\",\"orderSkip\":{\"btnText\":\"快速截图\",\"appId\":\"wx2c348cf579062e56\",\"originalId\":\"gh_72a4eb2d4324\",\"path\":\"pages/orders/orders\"}},\"task_out_evaluate_images\":{\"name\":\"评价截图（请勿长截图）\",\"description\":\"店铺名称+评价(店名无法显示完整需附地址截图)\",\"example_picture\":\"https://image.xiaoxiaoyouxuan.com/admin/image_20220311150234bb21b8cfb6.png\",\"commentSkip\":{\"btnText\":\"快速截图\",\"appId\":\"wx2c348cf579062e56\",\"originalId\":\"gh_72a4eb2d4324\",\"path\":\"sub_inner_pages/evaluate-list/evaluate-list.html\"}}}},\"third-no-praise\":{\"title\":\"到店体验后上传已完成截图\",\"content\":\"需要在{highlightStart}当日24点前{highlightEnd}提交，{highlightStart}当日24点后提交会减少2元，次日13点后将自动关闭订单{highlightEnd}。{highlightStart}注：反馈被折叠减少5元。{highlightEnd}\",\"content-late\":\"需要在{highlightStart}次日3点前{highlightEnd}提交，{highlightStart}次日13点后将自动关闭订单{highlightEnd}。{highlightStart}注：反馈被折叠减少5元。{highlightEnd}\",\"content-expired\":\"需要在{highlightStart}次日13点前{highlightEnd}提交，{highlightStart}超时视为自动放弃活动{highlightEnd}。{highlightStart}注：反馈被折叠减少5元。{highlightEnd}\",\"form\":{\"task_out_evaluate_images\":{\"name\":\"订单完成截图（请勿长截图）\",\"description\":\"已完成字样+店铺名+实付金额+订单号+下单时间\",\"example_picture\":\"https://image.xiaoxiaoyouxuan.com/admin/image_20231101115711f18eac174d.png\",\"commentSkip\":{\"btnText\":\"快速截图\",\"appId\":\"wx2c348cf579062e56\",\"originalId\":\"gh_72a4eb2d4324\",\"path\":\"pages/orders/orders\"}}}}}',
        1,
        1,
        2,
        '该活动需在报名后{highlightStart}{cancelTime}分钟在美团完成下单，下单后{consumeTime}天内完成团单核销{highlightEnd}，超时名额会被取消，请及时前往平台完成下单。{highlightStart}如您想额外得取反馈奖励需要在核销后{commentTime}天内在美团完成评价{highlightEnd}。',
        1722319212,
        30,
        1,
        '23:29:59',
        '系统检测到您的报名时间较晚，{highlightStart}该活动下单完成付款时间不得晚于当日24点{highlightEnd}，超时名额会被取消哦',
        0,
        '2024-08-01 11:56:16',
        '团购平台各种红包、券包、及门店红包券包等产生抵扣的费用是不计入实付金额的，{highlightStart}实付未满会导致扣除对应金额哦~{highlightEnd}\n下单前核对商家活动平台及名称地址是否一致，{highlightStart}下错平台或者店铺会导致无法获得返利哦～{highlightEnd}'
    );

-- 增加mtg_gp
INSERT INTO `bwc_business_platform_ext` ( `platform_abbreviation`, `min_version`, `max_version`, `activity_flow`, `order_flow`, `create_time`, `update_time`, `order_flow_order_limit`, `activity_flow_order_limit` )
VALUES
    ( 'mtg_gp', 20490, 20490, '{\"first\":{\"title\":\"先抢名额，后团购\",\"content\":\"先确认报名成功，再跳转下单，否则无法识别并返利\"},\"second\":{\"title\":\"跳转团购平台下单\",\"content\":\"需在{highlightStart}报名后30分钟内{highlightEnd}前往团购平台下单，{highlightStart}超时名额会被取消哦～{highlightEnd}\",\"redEnvelopeTurnOn\":false,\"redEnvelopeSkip\":{\"btnText\":\"立即领取外卖红包\",\"appId\":\"wxde8ac0a21135c07d\",\"originalId\":\"gh_870576f3c6f9\",\"path\":\"/index/pages/h5/h5?weburl=https%3A%2F%2Fclick.meituan.com%2Ft%3Ft%3D1%26c%3D2%26p%3DH5VL975zSdI5&lch=cps:waimai:5:42355891f188691b1bfb3d6c48ccc1ff:001:33:441129&f_token=1&f_userId=1\"}},\"third\":{\"title\":\"订单送达后提交订单反馈截图\",\"content\":\"需在{highlightStart}下单后2天内在美团{highlightEnd}完成团购核销，否则会{highlightStart}返现失败{highlightEnd}哦~\"},\"fourth\":{\"title\":\"核销完成等待返利到账\",\"content\":\"{highlightStart}官方审核中，核销完成约24h返现至美团钱包-余额{highlightEnd}\"},\"fifth\":{\"title\":\"反馈获取额外返利\",\"content\":\"(本步为额外返现，可选参加)需在{highlightStart}核销后2天内在美团{highlightEnd}完成评价反馈，可额外获得反馈奖励。超时不参与视为自动放弃，订单则仅有核销返。\"},\"sixth\":{\"title\":\"反馈完成等待返利到账\",\"content\":\"{highlightStart}官方审核中，反馈完成约24h返现至美团钱包-余额{highlightEnd}\"}}', '{\"second\":{\"title\":\"去团购平台下单，锁定名额\",\"placeSkip\":{\"btnText\":\"一键进店下单\",\"appId\":\"wx2c348cf579062e56\",\"originalId\":\"gh_72a4eb2d4324\",\"path\":\"packages/index/search/search\"},\"redEnvelopeTurnOn\":false,\"redEnvelopeSkip\":{\"btnText\":\"立即领取外卖红包\",\"appId\":\"wxde8ac0a21135c07d\",\"originalId\":\"gh_870576f3c6f9\",\"path\":\"/index/pages/h5/h5?weburl=https%3A%2F%2Fclick.meituan.com%2Ft%3Ft%3D1%26c%3D2%26p%3DH5VL975zSdI5&lch=cps:waimai:5:42355891f188691b1bfb3d6c48ccc1ff:001:33:441129&f_token=1&f_userId=1\"},\"content\":\"需在报名后{highlightStart}{cancel_time}分钟内{highlightEnd}完成下单，{highlightStart}超时名额会被取消哦~{highlightEnd}\"},\"third\":{\"title\":\"下单后完成团购核销\",\"content\":\"需在{highlightStart}下单后{consume_time}天内{highlightEnd}完成团购核销，否则会{highlightStart}返现失败{highlightEnd}哦~\"},\"fourth\":{\"title\":\"核销完成等待返利到账\",\"content\":\"{highlightStart}官方审核中，核销完成{delay_cash_back_time}返现至美团/大众点评钱包-余额{highlightEnd}\"},\"fifth\":{\"title\":\"反馈获取额外返利\",\"content\":\"(本步为额外返现，可选参加)需在{highlightStart}核销后{comment_time}天内{highlightEnd}完成评价反馈，可额外获得反馈奖励。超时不参与视为自动放弃，订单则仅有核销返。\"},\"sixth\":{\"title\":\"反馈完成等待返利到账\",\"content\":\"{highlightStart}官方审核中，反馈完成{delay_cash_back_time}返现至美团/大众点评钱包-余额{highlightEnd}\"}}', '2023-10-26 11:24:30', '2025-02-17 09:21:57', NULL, NULL );

-- 增加任务失败状态
ALTER TABLE `bwc_order` MODIFY COLUMN `cancel_status` TINYINT ( 1 ) UNSIGNED NOT NULL DEFAULT 0 COMMENT '1用户取消 2超时取消 3过期取消 4审核人工取消 5退款取消 6任务失败' AFTER `order_status`;


INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform`, `create_time`, `update_time` )
VALUES
    ( 'string', '美团团购下单弹窗开关（1：开 0：关）', 'mtg_gp_order_text_enabled', '1', '', 0, '2024-12-18 22:28:26', '2024-12-27 00:04:57' );
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform`, `create_time`, `update_time` )
VALUES
    ( 'string', '美团团购活动下单文案', 'mtg_gp_order_text', '由于与美团合作升级，{highlightTextStart}该订单完成约24小时后返现至美团/大众点评APP钱包余额{highlightTextEnd}，可在余额中查看返现金额明细。', '', 0, '2024-12-18 22:28:26', '2025-02-18 09:46:39' );



