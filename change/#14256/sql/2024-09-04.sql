-- 创建表task_cash_back_type
CREATE TABLE `bwc_task_cash_back_type` (
                                           `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                           `task_id` int(11) NOT NULL DEFAULT '0',
                                           `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型：默认按金额返现0，按比例返现1',
                                           `ratio` int(11) NOT NULL DEFAULT '0' COMMENT '注意这里是，万分比',
                                           `max_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最高返现金额',
                                           `service_fee` decimal(10,2) DEFAULT '0.00' COMMENT '城市服务费，对应agent表的sale_release_task_commission_fee和seller_release_task_commission_fee字段值',
                                           `gross_profit` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '任务的每单毛利',
                                           `agent_profit` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '差价',
                                           `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
                                           `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                           `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `idx_task_id` (`task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务关联表，用来控制任务按固定金额返现还是按比例返现';

-- 循环创建任务task_cycle_create_ext
CREATE TABLE `bwc_task_cycle_create_ext` (
                                             `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                             `cycle_create_task_id` int(11) NOT NULL DEFAULT '0',
                                             `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型：默认按金额返现0，按比例返现1',
                                             `ratio` int(11) NOT NULL DEFAULT '0' COMMENT '注意这里是，万分比',
                                             `max_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最高返现金额',
                                             `gross_profit` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '任务的每单毛利',
                                             `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                             `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                             PRIMARY KEY (`id`),
                                             UNIQUE KEY `idx_cyc_task_id` (`cycle_create_task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务关联表，用来控制任务按固定金额返现还是按比例返现';

-- 比例订单费
CREATE TABLE `bwc_order_ratio_info` (
                                        `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `order_id` int(1) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
                                        `ratio` int(10) NOT NULL DEFAULT '0' COMMENT '返现比例',
                                        `max_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最高返',
                                        `task_receipt_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '接单价格',
                                        `fee` decimal(10,2) DEFAULT '0.00' COMMENT '差额费',
                                        `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE,
                                        KEY `idx_order_id` (`order_id`) USING HASH
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4;

-- 增加admin_daily_profit_change_log
CREATE TABLE `bwc_admin_daily_profit_change_log` (
                                                     `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                     `daily_profit_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '放单人id，bwc_admin.id',
                                                     `order_id` int(11) NOT NULL DEFAULT '0',
                                                     `task_receipt_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '接单价格',
                                                     `cashback_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际返现',
                                                     `order_cashback_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单返现',
                                                     `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                     `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                     PRIMARY KEY (`id`) USING BTREE,
                                                     KEY `idx_daily_profit` (`daily_profit_id`,`order_id`,`create_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 增加比例任务版本
INSERT INTO `bwc_version_compatible` ( `version_name`, `min_version`, `version_desc`, `data_state`, `create_time`, `update_time` )
VALUES
( 'ratio_task_v', 20390, '比例任务活动', 0, '2024-09-29 09:55:51', '2024-09-29 09:55:51' );

-- 是否开启按照比例返现
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
( 'string', '发布任务是否开启按比例返现(1开，0关)', 'create_task_for_ratio', '0', '', 0 );

-- 增加配置
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
( 'string', '商家端按比例返现 最低返现比例设定', 'seller_end_task_min_ratio', '4000', '', 0 );
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
( 'string', '后台按比例返现，最低返现比例设定', 'manage_end_task_min_ratio', '3000', '', 0 );
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
( 'string', '商家端按比例返现最高返要求最低金额', 'seller_end_task_min_cashback_amount', '10', '', 0 );
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
( 'string', '后台按比例返现最高返 要求最低金额', 'manage_end_task_min_cashback_amount', '10', '', 0 );

INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
( 'string', '比例任务最小返现比例text', 'ratio_task_min_cashback_ratio_text', '以用户实付金额*百分比计算返现金额，后续账单金额按照(最高返金额+服务费)*活动名额计算，账单生成后进行差额返还', '', 0 );
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
( 'string', '比例任务最高返金额text', 'ratio_task_max_cashback_amount_text', '用户实付金额*返现比例计算得出的返现金额，超过最高返现金额时，会以最高返进行用户返现', '', 0 );
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
( 'string', '商家端新增任务提示：按实付返现', 'seller_notice_cashback_text', '用户实付达到门槛金额后固定返现，后续账单金额按照含佣金额*活动名额计算，账单生成后进行差额返还', '', 0 );

-- 增加权限
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description` )
SELECT '解绑门店', 'store_unbind_store_repo', 1, '', t.`id`, 'business', '解绑门店'
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'store' AND `type` = 0) AS t;

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description` )
SELECT '解绑门店', 'store.unbind_store_repo', 2, 'POST', `t`.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'store_unbind_store_repo' AND `type` = 1) AS t;

-- 增加比例任务锚点值
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
    ( 'string', '首页列表比例任务排序锚点值', 'index_list_ratio_task_sort_anchor', '15', '', 0 );