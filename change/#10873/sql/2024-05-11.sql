CREATE TABLE `bwc_order_remark`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `order_id`       int(11) NOT NULL COMMENT '订单id',
    `remark`         varchar(255) NOT NULL DEFAULT '' COMMENT '订单备注',
    `create_by_name` varchar(32)  NOT NULL DEFAULT '' COMMENT '修改人名称',
    `create_by`      int(11) NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_by`      int(11) NOT NULL DEFAULT '0' COMMENT '修改人',
    `gmt_created`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_modified`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_delete`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
    PRIMARY KEY (`id`),
    KEY              `idx_order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='订单备注表';
