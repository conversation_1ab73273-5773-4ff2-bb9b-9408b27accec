ALTER TABLE `bwc_seller`
    ADD COLUMN `pre_order_switch` tinyint(1) unsigned NOT NULL DEFAULT '2' COMMENT '是否开启预售单量充值开关：1开启，2关闭';

ALTER TABLE `bwc_seller_money_record`
    MODIFY COLUMN `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1收支记录，2提现记录，3预存单量';

CREATE TABLE `bwc_admin_seller_relation`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `admin_id`    int(11) unsigned NOT NULL COMMENT '销售id，bwc_admin.id',
    `seller_id`   int(11) unsigned NOT NULL COMMENT '商家id，bwc_seller.id',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态，0表示正常，1表示删除',
    `created_by`  int(11) NOT NULL DEFAULT '0' COMMENT 'bwc_admin.id 管理员ID',
    `updated_by`  int(11) DEFAULT NULL COMMENT 'bwc_admin.id 管理员ID',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_admin_id` (`admin_id`) USING BTREE,
    KEY           `idx_seller_id` (`seller_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4;

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '开启预存单量充值入口', 'seller_open_pre_order_switch', 1, '', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '关闭预存单量充值入口', 'seller_close_pre_order_switch', 1, '', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller' AND `type` = 0) AS t;

-- INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
-- SELECT '开启预存单量充值入口-接口', 'Seller.open_pre_order_switch', 2, 'POST', t.`id`, 'business', ''
-- FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_open_pre_order_switch' AND `type` = 1) AS t;

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '充值记录', 'seller_page_recharge_record', 1, '', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '充值记录-接口', 'Seller.recharge_record', 2, 'GET', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_page_recharge_record' AND `type` = 1) AS t;

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`,
                                  `data_state`, `create_time`, `update_time`)
VALUES ('关联商家管理', 'admin_seller', 0, '', 0, 'business', '', 2, 0, '2023-08-10 14:04:08', '2023-08-10 14:04:13');
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '关联商家管理列表', 'admin_seller_list', 1, '', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'admin_seller' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '关联商家管理导出', 'admin_seller_export', 1, '', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'admin_seller' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '关联商家管理列表-接口', 'AdminSeller.list', 2, 'GET', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'admin_seller_list' AND `type` = 1) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '关联商家管理导出-接口', 'AdminSeller.export', 2, 'POST', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'admin_seller_export' AND `type` = 1) AS t;

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '查看店铺', 'admin_seller_get_store', 1, '', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'admin_seller' AND `type` = 0) AS t;


INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '查看手机号', 'admin_seller_get_mobile', 1, '', t.`id`, 'business', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'admin_seller' AND `type` = 0) AS t;


ALTER TABLE `bwc_excel_download_task`
    MODIFY COLUMN `excel_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '1.全部订单,2.未结算订单,3.商家账单批量导出excel,4.商家账单导出excel,5.发账单导出excel,6.发店铺账单导出excel,7.美团霸王餐账单导出excel,8.导出吃吃龟格式的excel,9.导出吃吃龟未结算账单,10.导出吃吃龟多个商家excel的zip,11.任务excel,13.订单明细导出,14.代理数据统计导出,15.提成结算导出,16.数据统计导出,17.提现账号excel,18.订单审核记录excel,19.修改账单记录excel,20.任务修改记录excel,21.工单中心-工单列表-工单明细导出,22.工单中心-明细统计-工作量明细导出,23.饿了么账单excel,24.推广-推广分组-查询数据-推广数据导出,25.财务-商家退款记录导出,26.饿了么专享账单excel,27.商务-线索管理-列表数据导出,28.灰太狼账单excel,29.店铺开票支付记录,30.标记店铺每日账单导出,31.批量订单行为导出， 32：商家端招商加盟，33. 美团对账单, 34:美团官方团购对账单, 35:美团官方团购账单导出Excel, 36:三方用户订单导出, 42:代理消费记录明细导出，40. 关联商家信息';

ALTER TABLE `bwc_seller_money_record`
    ADD COLUMN `pre_order_volume` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '预存单量';



ALTER TABLE `bwc_agent`
    ADD COLUMN `pre_order_volume_price` DECIMAL(10, 2) NOT NULL DEFAULT '0.00' COMMENT '预存单量金额' AFTER `current_balance`,
ADD COLUMN `pre_order_volume` INT(11) NOT NULL DEFAULT '0' COMMENT '预存单量' AFTER `pre_order_volume_price`;
ALTER TABLE `bwc_agent`
    ADD COLUMN `is_enable_pre_order_volume` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '是否启用预存单量 1启用 0禁用' AFTER `pre_order_volume_price`;



CREATE TABLE `bwc_task_pre_order_volume`
(
    `id`               int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `seller_id`        int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商家id，bwc_seller.id',
    `store_id`         int(11) unsigned NOT NULL DEFAULT '0' COMMENT '店铺id，bwc_store.id',
    `task_id`          int(11) unsigned NOT NULL DEFAULT '0' COMMENT '任务id，bwc_task.id',
    `task_cycle_id`    int(11) unsigned NOT NULL DEFAULT '0' COMMENT '循环任务id，bwc_task_cycle_create.id\n',
    `pre_order_volume` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '使用的预存单量',
    `service_fee`      decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '每单服务费',
    `create_time`      timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`      timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                `idx_seller_id` (`seller_id`),
    KEY                `idx_store_id` (`store_id`),
    KEY                `idx_task_id` (`task_id`) USING BTREE,
    KEY                `idx_task_cycle_id` (`task_cycle_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='任务创建预存单量预设';

CREATE TABLE `bwc_seller_pre_order_volume`
(
    `id`                         int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `seller_id`                  int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商家id，bwc_seller.id',
    `seller_bill_id`             int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'bwc_seller_bill_order.id',
    `price`                      decimal(10, 2)     DEFAULT '0.00' COMMENT '价值',
    `unit_price`                 decimal(10, 2)     DEFAULT '0.00' COMMENT '单价',
    `pre_order_volume`           int(11) unsigned NOT NULL DEFAULT '0' COMMENT '总的预存单量',
    `available_pre_order_volume` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '剩余预存单量',
    `data_state`                 tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态，0表示正常，1表示删除',
    `create_time`                timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                          `idx_seller_id` (`seller_id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='商家预存单量购买记录表';

CREATE TABLE `bwc_pre_order_task_service_fee`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `task_id`     int(11) unsigned NOT NULL DEFAULT '0' COMMENT '任务id',
    `service_fee` decimal(10, 2) unsigned NOT NULL DEFAULT '0.00' COMMENT '等值服务费',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_task_id` (`task_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='使用预存单量的任务的服务费';


---- 新SQL问题

CREATE TABLE `bwc_seller_pre_order_volume_transaction`
(
    `id`                         int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `seller_id`                  int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商家id，bwc_seller.id',
    `seller_pre_order_volume_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'bwc_seller_pre_order_volume.id',
    `task_id`                    int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'bwc_task.id任务id',
    `task_cycle_id`              int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'bwc_task_cycle_create.id循环任务',
    `pre_order_volume`           int(11) unsigned NOT NULL DEFAULT '0' COMMENT '预存单量',
    `refund_pre_order_volume`    int(11) unsigned NOT NULL DEFAULT '0' COMMENT '退款预存单量',
    `remaining_pre_order_volume` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '剩余预存单量',
    `data_state`                 tinyint(4) NOT NULL DEFAULT '0',
    `create_time`                timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                          `idx_seller_id` (`seller_id`),
    KEY                          `idx_seller_pre_order_volume_id` (`seller_pre_order_volume_id`),
    KEY                          `idx_task_id` (`task_id`),
    KEY                          `idx_task_cycle_id` (`task_cycle_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家预存单量消费记录表';

CREATE TABLE `bwc_task_cycle_pre_order_volume_everyday`
(
    `id`                         int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `cycle_create_task_id`       int(11) unsigned NOT NULL DEFAULT '0' COMMENT '循环创建任务id',
    `task_date`                  date               DEFAULT NULL COMMENT '任务日期',
    `seller_pre_order_volume_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'bwc_seller_pre_order_volume.id\n',
    `seller_pre_order_volume`    int(11) unsigned NOT NULL DEFAULT '0',
    `data_state`                 tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time`                timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `bwc_task_avg_pre_order_volume_price`
(
    `id`                         int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    `task_id`                    int(11) unsigned NOT NULL DEFAULT '0' COMMENT '任务id，bwc_task.id',
    `task_cycle_id`              int(11) unsigned NOT NULL DEFAULT '0' COMMENT '循环任务id，bwc_task_cycle_create.id\n',
    `avg_pre_order_volume_price` decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '平均预存单量价格',
    `data_state`                 tinyint(1) unsigned NOT NULL DEFAULT '0',
    `create_time`                timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`                timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                          `idx_task_id` (`task_id`) USING BTREE,
    KEY                          `idx_task_cycle_id` (`task_cycle_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务创建预存单量预设';


ALTER TABLE `bwc_seller_pre_order_volume_transaction`
    ADD COLUMN `seller_bill_order_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'bwc_seller_bill_order.id' AFTER `task_cycle_id`,
ADD INDEX `idx_seller_bill_order` (`seller_bill_order_id`);

ALTER TABLE `bwc_seller_bill`
    ADD COLUMN `pre_order_volume_bill_fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预存单量金额' AFTER `bill_fee`;

-- 慢的SQL
ALTER TABLE `bwc_seller_bill_order`
    MODIFY COLUMN `pay_method` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '4.首信易-支付宝 5.首信易-微信 8.微信小程序';


ALTER TABLE `bwc_seller_bill_order_rele`
    ADD COLUMN `pre_order_volume` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '预存单量' AFTER `type`,
    MODIFY COLUMN `type` TINYINT(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '第三方订单类型：1账单，2提现到银行卡，3退款，4活动预支付 5充值 6预存单量充值';
