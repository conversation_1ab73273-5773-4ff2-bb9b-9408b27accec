INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`) VALUES (NULL, 'string', '超级返＆试吃官活动下单文案', 'cjf_scg_order_text', '“超级返&试吃官”活动的返现金额，将在{highlightTextStart}订单完成后约24小时后返现至美团钱包-余额{highlightTextEnd}，可在余额中查看返现金额明细。', '', 0);
INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`) VALUES (NULL, 'string', '超级返图标', 'cjf_icon', 'https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_202405230912299590bda1c4780975.png', '', 0);
INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`) VALUES (NULL, 'string', '试吃官图标', 'scg_icon', 'https://xxyx-1310097496.cos.ap-shanghai.myqcloud.com/admin/allType_202405230913327c744648dc990505.png', '', 0);
INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`) VALUES (NULL, 'string', '超级返＆试吃官活动下单弹窗开关（1：开 0：关）', 'cjf_scg_order_text_enabled', '1', '', 0);
INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`) VALUES (NULL, 'string', '超级返＆试吃官活动下单返现的示例图', 'cjf_scg_order_image', '["https://image.xiaoxiaoyouxuan.com/admin/image_20240520133445a3e66a548c416012.png","https://image.xiaoxiaoyouxuan.com/admin/image_2024052013345890981695a0821642.png","https://image.xiaoxiaoyouxuan.com/admin/image_2024052013351014030c0828861486.png"]', '', 0);
INSERT INTO `bwc_config` (`id`, `type`, `name`, `key`, `value`, `group`, `platform`) VALUES (NULL, 'string', '超级返＆试吃官活动下单完善信息文案', 'cjf_scg_perfect_information_text', '[{"content":"请填写与{highlightTextStart}美团注册账号绑定一致的手机号{highlightTextEnd}"},{"content":"需先报名后下单"},{"content":"报名后不可改美团绑定的手机号"},{"content":"返现金额在{highlightTextStart}订单完成约24小时后返现至美团钱包{highlightTextEnd}"}]', '', 0);

INSERT INTO `bwc_version_compatible` (`id`, `version_name`, `min_version`, `version_desc`, `data_state`, `create_time`, `update_time`) VALUES (NULL, 'cjf_scg_activity_v', 20200, '超级返＆试吃官活动', 0, '2024-05-10 15:02:26', '2024-05-15 11:40:21');
INSERT INTO `bwc_business_platform_ext` (`id`, `platform_abbreviation`, `min_version`, `max_version`, `activity_flow`, `order_flow`, `create_time`, `update_time`) VALUES (NULL, 'mtg', 20200, 20200, '{\"first\":{\"title\":\"先抢名额，后点餐\",\"content\":\"先确认报名成功，再跳转下单，否则无法识别并返利\"},\"second\":{\"title\":\"先领红包，再去下单更优惠哦～\",\"content\":\"需在报名后{highlightStart}1小时内{highlightEnd}完成下单，{highlightStart}超时名额会被取消哦～{highlightEnd}\",\"redEnvelopeTurnOn\":true,\"redEnvelopeSkip\":{\"btnText\":\"立即领取外卖红包\",\"appId\":\"wxde8ac0a21135c07d\",\"originalId\":\"gh_870576f3c6f9\",\"path\":\"/index/pages/h5/h5?weburl=https%3A%2F%2Fclick.meituan.com%2Ft%3Ft%3D1%26c%3D2%26p%3DkWWiLb5zyeF1&lch=cps:waimai:5:9d05b2935ca1712bb495f1955be8a5b7:001:33:434486&f_token=1&f_userId=1\"}},\"third\":{\"title\":\"收到货品后去反馈\",\"content\":\"下单后需在{highlightStart}次日23点前完成评价{highlightEnd}，否则会{highlightStart}返现失败{highlightEnd}哦~\"},\"fourth\":{\"title\":\"订单完成后，等待返利到账\",\"content\":\"订单完成约{highlightStart}24小时后返利至美团钱包-余额{highlightEnd}\"}}', '{\"second\":{\"title\":\"领红包下单，锁定名额\",\"placeSkip\":{\"btnText\":\"一键进店下单\",\"appId\":\"wx2c348cf579062e56\",\"originalId\":\"gh_72a4eb2d4324\",\"path\":\"packages/index/search/search\"},\"redEnvelopeTurnOn\":true,\"redEnvelopeSkip\":{\"btnText\":\"立即领取外卖红包\",\"appId\":\"wxde8ac0a21135c07d\",\"originalId\":\"gh_870576f3c6f9\",\"path\":\"/index/pages/h5/h5?weburl=https%3A%2F%2Fclick.meituan.com%2Ft%3Ft%3D1%26c%3D2%26p%3DkWWiLb5zyeF1&lch=cps:waimai:5:9d05b2935ca1712bb495f1955be8a5b7:001:33:434486&f_token=1&f_userId=1\"},\"content\":\"需在报名后{highlightStart}1小时内{highlightEnd}完成下单，{highlightStart}超时名额会被取消哦~{highlightEnd}\"},\"third\":{\"title\":\"收到货品后反馈\",\"content\":\"下单后需在{highlightStart}次日23点前完成评价，{highlightEnd}否则会{highlightStart}返现失败{highlightEnd}哦~\",\"is_praise\":1},\"fourth\":{\"title\":\"订单完成后，等待返利到账\",\"content\":\"订单完成约{highlightStart}24小时后返利至美团钱包-余额{highlightEnd}\"}}', '2023-10-26 11:24:30', '2024-05-20 14:15:04');


CREATE TABLE `bwc_user_popup_setting` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户id，bwc_user.id',
    `type` int(11) NOT NULL DEFAULT '0' COMMENT '弹窗类型：1，试吃官超级返弹窗',
    `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1：开启 0：关闭',
    `data_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id_type` (`user_id`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

UPDATE `bwc_business_platform` SET `platform_name` = '美团' WHERE `platform_abbreviation` = 'mtg' LIMIT 1;

ALTER TABLE `bwc_order_mt` MODIFY COLUMN `type` tinyint(3) NOT NULL DEFAULT 'new_default_value' COMMENT '类型 1餐标类型; 2比例类型 3：统一返现';

ALTER TABLE `bwc_order_mt` ADD COLUMN `subsidy_ratio` int(11) NOT NULL DEFAULT '0' COMMENT '渠道补贴比例，万分比，默认为 0，补贴⽐例最⼤为渠道结佣的 100% 即补贴范围：[0, 10000] = [0%,100%]' AFTER `type`;
ALTER TABLE `bwc_order_mt` ADD COLUMN `commission_ratio` int(11) NOT NULL DEFAULT '0' COMMENT '美团统一返现结算该城市佣金比例（万分比）' AFTER `subsidy_ratio`;
ALTER TABLE `bwc_order_mt` ADD COLUMN `media_ratio` int(11) NOT NULL DEFAULT '0' COMMENT '美团统一返现渠道佣⾦⽐例（万分比）' AFTER `commission_ratio`;

ALTER TABLE bwc_agent ADD COLUMN subsidy_ratio int(11) NOT NULL COMMENT '美团统一返现渠道补贴比例（万分比）';
ALTER TABLE bwc_agent ADD COLUMN commission_ratio int(11) NOT NULL COMMENT '美团统一返现结算该城市佣金比例（万分比）';
-- 默认50%，万分比
UPDATE bwc_agent SET commission_ratio = 5000;


