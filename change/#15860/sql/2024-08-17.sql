-- 增加代理排序表
CREATE TABLE `bwc_agent_sort` (
                                  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                  `agent_id` int(11) NOT NULL DEFAULT '0',
                                  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                  PRIMARY KEY (`id`) USING BTREE,
                                  KEY `idx_agent_id` (`agent_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 增加记录
INSERT INTO `bwc_agent_sort` ( `agent_id`, `create_time`, `update_time` )
VALUES
( 12, '2024-08-17 10:45:10', '2024-08-17 10:45:10' );