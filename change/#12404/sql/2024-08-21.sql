INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '批量标记坏账', 'batch_bad_debt', 1, '', t.`id`, 'finance', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_bill' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '批量取消标记坏账', 'batch_cancel_bad_debt', 1, '', t.`id`, 'finance', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'seller_bill' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '批量标记坏账-接口', 'SellerBill.bad_debt', 2, 'POST', t.`id`, 'finance', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'batch_bad_debt' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '批量取消标记坏账-接口', 'SellerBill.cancel_bad_debt', 2, 'POST', t.`id`, 'finance', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'batch_cancel_bad_debt' AND `type` = 0) AS t;
