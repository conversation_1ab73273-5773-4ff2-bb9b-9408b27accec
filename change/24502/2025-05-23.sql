insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '标记坏账记录导出',
            'bad_seller_bill_tag_export',
            1,
            '',
            t.id,
            'finance',
            '标记坏账记录导出',
            2
     from bwc_authority_temp as t
     where t.`key` = 'seller_bill'
       and type = 0);
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '标记坏账记录导出-接口',
            'SellerBill.bad_seller_bill_tag_export',
            2,
            'POST',
            t.id,
            'finance',
            '标记坏账记录导出-接口',
            2
     from bwc_authority_temp as t
     where t.`key` = 'bad_seller_bill_tag_export'
       and type = 1);


insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '标记坏账记录',
            'bad_tag_record',
            1,
            '',
            t.id,
            'finance',
            '标记坏账记录',
            2
     from bwc_authority_temp as t
     where t.`key` = 'seller_bill'
       and type = 0);
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '标记坏账记录-接口',
            'SellerBill.bad_tag_record',
            2,
            'POST',
            t.id,
            'finance',
            '标记坏账记录-接口',
            2
     from bwc_authority_temp as t
     where t.`key` = 'bad_tag_record'
       and type = 1);

-- 手动修改
-- ALTER TABLE `bwc_excel_download_task`
--     MODIFY COLUMN `excel_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '1.全部订单,2.未结算订单,3.商家账单批量导出excel,4.商家账单导出excel,5.发账单导出excel,6.发店铺账单导出excel,7.美团霸王餐账单导出excel,8.导出吃吃龟格式的excel,9.导出吃吃龟未结算账单,10.导出吃吃龟多个商家excel的zip,11.任务excel,13.订单明细导出,14.代理数据统计导出,15.提成结算导出,16.数据统计导出,17.提现账号excel,18.订单审核记录excel,19.修改账单记录excel,20.任务修改记录excel,21.工单中心-工单列表-工单明细导出,22.工单中心-明细统计-工作量明细导出,23.饿了么账单excel,24.推广-推广分组-查询数据-推广数据导出,25.财务-商家退款记录导出,26.饿了么专享账单excel,27.商务-线索管理-列表数据导出,28.灰太狼账单excel,29.店铺开票支付记录,30.标记店铺每日账单导出,31.批量订单行为导出， 32：商家端招商加盟，33. 美团对账单, 34:美团官方团购对账单, 35:美团官方团购账单导出Excel,36:第三方任务订单，40. 关联商家信息, 41:快照导出, 42代理消费记录明细导出, 43新增店铺导出 44美团官方商家账单批量导出 45灰太狼商家账单批量导出 46饿了么专享商家账单批量导出';


CREATE TABLE `bwc_seller_bill_bad_tag_log`
(
    `id`             int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `seller_bill_id` int(11) NOT NULL COMMENT '账单id， bwc_seller_bill.id',
    `admin_id`       int(11) DEFAULT NULL COMMENT '操作人ID，bwc_admin.id',
    `admin_name`     varchar(64)        DEFAULT NULL COMMENT '操作人名称',
    `type`           tinyint(2) unsigned DEFAULT '1' COMMENT '操作类型：1 标记为坏账、2 取消标记坏账、3 批量标记坏账、4批量取消标记坏账',
    `data_state`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态0正常1删除',
    `create_time`    timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY              `idx_admin_id` (`admin_id`),
    KEY              `idx_create_time` (`create_time`),
    KEY              `idx_seller_bill_id` (`seller_bill_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='坏账标记日志记录表';
