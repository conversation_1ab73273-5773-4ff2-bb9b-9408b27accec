INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES ('运营短信', 'operate_sms_push', 0, '', 0, 'operate', '', 2, 0, '2023-08-10 14:04:08', '2023-08-10 14:04:13');
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '运营短信列表', 'operate_sms_push_list', 1, '', t.`id`, 'operate', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'operate_sms_push' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '运营短信详情', 'operate_sms_push_show', 1, '', t.`id`, 'operate', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'operate_sms_push' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '运营短信创建', 'operate_sms_push_create', 1, '', t.`id`, 'operate', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'operate_sms_push' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '运营短信删除', 'operate_sms_push_destroy', 1, '', t.`id`, 'operate', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'operate_sms_push' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`) SELECT '运营短信日志', 'operate_sms_push_log', 1, '', t.`id`, 'operate', '' FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'operate_sms_push' AND `type` = 0) AS t;
