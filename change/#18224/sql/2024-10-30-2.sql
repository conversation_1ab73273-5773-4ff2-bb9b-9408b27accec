-- 用户标识
CREATE TABLE `bwc_user_sid` (
                                `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
                                `user_id` int(10) NOT NULL COMMENT '用户id',
                                `red_link` varchar(255) NOT NULL DEFAULT '' COMMENT '红包链接',
                                `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 新增order_meituan_callback表
CREATE TABLE `bwc_order_meituan_callback` (
                                              `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                              `order_no` varchar(50) NOT NULL COMMENT '订单号',
                                              `business_line` int(11) DEFAULT NULL COMMENT '美团-业务线',
                                              `sub_business_line` int(11) DEFAULT NULL COMMENT '美团-子业务线',
                                              `act_id` int(11) DEFAULT NULL COMMENT '美团-活动id，可以在联盟活动列表中查看获取',
                                              `quantity` int(11) DEFAULT NULL COMMENT '美团-商品数量',
                                              `pay_time` varchar(20) DEFAULT NULL COMMENT '美团-订单支付时间，10位时间戳',
                                              `mod_time` varchar(20) DEFAULT NULL COMMENT '美团-订单信息修改时间，10位时间戳',
                                              `pay_price` decimal(10,2) DEFAULT '0.00' COMMENT '美团-订单用户实际支付金额',
                                              `profit` decimal(10,2) DEFAULT '0.00' COMMENT '美团-订单预估返佣金额\n\n',
                                              `cpa_profit` decimal(10,2) DEFAULT '0.00' COMMENT '美团-订单预估cpa总收益（优选、话费券）',
                                              `sid` varchar(255) DEFAULT NULL COMMENT '美团-订单对应的推广位sid',
                                              `app_key` varchar(255) DEFAULT NULL COMMENT '美团-订单对应的appkey，外卖、话费、闪购、优选、酒店订单会返回该字段',
                                              `sms_title` varchar(255) DEFAULT NULL COMMENT '美团-订单标题',
                                              `status` int(11) DEFAULT NULL COMMENT '美团-订单状态，外卖、话费、闪购、优选、酒店订单会返回该字段\n\n1 已付款\n\n8 已完成\n\n9 已退款或风控',
                                              `trade_type_list` json DEFAULT NULL COMMENT '美团-订单的奖励类型\n\n话费订单类型返回该字段\n\n3 首购奖励\n\n5 留存奖励\n\n优选订单类型返回该字段\n\n2 cps\n\n3 首购奖励',
                                              `risk_order` int(11) DEFAULT NULL COMMENT '美团-0表示非风控订单，1表示风控订单',
                                              `refund_profit` decimal(10,2) DEFAULT NULL COMMENT '美团-订单需要扣除的返佣金额，外卖、话费、闪购、优选、酒店订单若发生退款会返回该字段',
                                              `cpa_refund_profit` decimal(10,2) DEFAULT NULL COMMENT '美团-订单需要扣除的cpa返佣金额（优选、话费券）',
                                              `refund_info_list` json DEFAULT NULL COMMENT '美团-退款列表',
                                              `refund_profit_list` json DEFAULT NULL COMMENT '美团-退款佣金明细',
                                              `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
                                              `create_by` bigint(255) NOT NULL DEFAULT '0',
                                              `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                              `update_by` bigint(20) NOT NULL DEFAULT '0',
                                              `gmt_update` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                              PRIMARY KEY (`id`) USING BTREE,
                                              UNIQUE KEY `idx_order_no` (`order_no`) USING BTREE,
                                              KEY `idx_gmt_create` (`gmt_create`) USING BTREE,
                                              KEY `idx_sid` (`sid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';