-- 增加app订单号授权检测总开关配置
INSERT INTO `bwc_config` ( `type`, `name`, `key`, `value`, `group`, `platform` )
VALUES
    ( 'string', 'app订单号授权检测总开关', 'app_order_no_check_enabled', '0', '', 0 );

-- 修改agent表
ALTER TABLE `bwc_agent`
    ADD COLUMN `app_order_no_check_enabled` TINYINT ( 1 ) NOT NULL DEFAULT 2 COMMENT 'app订单号检测开关 1开;2关' AFTER `sort_rule`;

-- 更新配置
UPDATE `bwc_config`
SET `value` = '{\"android\":{\"mt\":{\"login_route\":\"https://h5.waimai.meituan.com/login\",\"order_detail_link\":\"https://h5.waimai.meituan.com/waimai/mindex/order-detail?mtOrderViewId=\",\"order_detail_route\":\"https://h5.waimai.meituan.com/waimai/mindex/order-detail\",\"order_list_route\":\"https://h5.waimai.meituan.com/waimai/mindex/olist\",\"order_detail_req\":\"https://i.waimai.meituan.com/openh5/order/manager/v3/detail\",\"order_list_link\":\"https://h5.waimai.meituan.com/waimai/mindex/olist\",\"order_detail_req_list\":[\"https://i.waimai.meituan.com/openh5/order/manager/v3/detail\",\"https://wx-shangou.meituan.com/quickbuy/v1/order/detail\"],\"order_detail_route_list\":[\"https://h5.waimai.meituan.com/waimai/mindex/order-detail\",\"https://cactivityapi-sc.waimai.meituan.com/h5/package/order-info/order-info\"],\"order_req_url\":\"https://i.waimai.meituan.com/openh5/order/manager/v3/detail\"},\"ele\":{\"login_route\":\"https://h5.ele.me/login/\",\"order_detail_link\":\"https://h5.ele.me/2021001185671035/pages/ele-order-detail-tb/ele-order-detail-tb?orderId=\",\"order_detail_route\":\"https://h5.ele.me/2021001185671035/pages/ele-order-detail-tb/ele-order-detail-tb\",\"order_list_route\":\"https://h5.ele.me/minisite/pages/order-list/index\",\"order_detail_req\":\"//alsc-buy2.ele.me/h5/mtop.alsc.eleme.order.detail.miniapp.build\",\"order_list_link\":\"https://h5.ele.me/minisite/pages/order-list/index\",\"order_detail_req_list\":[\"//alsc-buy2.ele.me/h5/mtop.alsc.eleme.order.detail.miniapp.build\",\"//alsc-buy2.ele.me/h5/mtop.me.ele.newretail.trade.order.getorderdetail\"],\"order_detail_route_list\":[\"https://h5.ele.me/2021001185671035/pages/ele-order-detail-tb/ele-order-detail-tb\",\"https://h5.ele.me/newretail/tr/order-detail\"],\"order_req_url\":\"//alsc-buy2.ele.me/h5/mtop.alsc.eleme.order.detail.miniapp.build\"}},\"ios\":{\"mt\":{\"login_route\":\"https://h5.waimai.meituan.com/login\",\"order_detail_link\":\"https://h5.waimai.meituan.com/waimai/mindex/order-detail?mtOrderViewId=\",\"order_detail_route\":\"https://h5.waimai.meituan.com/waimai/mindex/order-detail\",\"order_list_route\":\"https://h5.waimai.meituan.com/waimai/mindex/olist\",\"order_detail_req\":\"https://i.waimai.meituan.com/openh5/order/manager/v3/detail\",\"order_list_link\":\"https://h5.waimai.meituan.com/waimai/mindex/olist\",\"order_detail_req_list\":[\"https://i.waimai.meituan.com/openh5/order/manager/v3/detail\",\"https://wx-shangou.meituan.com/quickbuy/v1/order/detail\"],\"order_detail_route_list\":[\"https://h5.waimai.meituan.com/waimai/mindex/order-detail\",\"https://cactivityapi-sc.waimai.meituan.com/h5/package/order-info/order-info\"]},\"ele\":{\"login_route\":\"https://h5.ele.me/login/\",\"order_detail_link\":\"https://h5.ele.me/2021001185671035/pages/ele-order-detail-tb/ele-order-detail-tb?orderId=\",\"order_detail_route\":\"https://h5.ele.me/2021001185671035/pages/ele-order-detail-tb/ele-order-detail-tb\",\"order_list_route\":\"https://h5.ele.me/minisite/pages/order-list/index\",\"order_detail_req\":\"https://alsc-buy2.ele.me/h5/mtop.alsc.eleme.order.detail.miniapp.build\",\"login_req_1\":\"https://ipassport.ele.me/newlogin/sms/login\",\"login_req_2\":\"https://ipassport.ele.me/newlogin/simLogin\",\"order_list_link\":\"https://h5.ele.me/minisite/pages/order-list/index\",\"order_detail_req_list\":[\"https://alsc-buy2.ele.me/h5/mtop.alsc.eleme.order.detail.miniapp.build\",\"https://alsc-buy2.ele.me/h5/mtop.me.ele.newretail.trade.order.getorderdetail\"],\"order_detail_route_list\":[\"https://h5.ele.me/2021001185671035/pages/ele-order-detail-tb/ele-order-detail-tb\",\"https://h5.ele.me/newretail/tr/order-detail\"]}}}'
WHERE
    `key` = 'third_order_link';

-- 新增order_check表
CREATE TABLE `bwc_order_check` (
                                   `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                   `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
                                   `app_order_no_check_enabled` tinyint(1) NOT NULL DEFAULT '2' COMMENT 'app订单号检测开关 1开;2关',
                                   `order_info_popup_reminder` tinyint(1) NOT NULL DEFAULT '1' COMMENT '订单信息弹窗提醒 1开； 2关',
                                   `order_cancel_popup_reminder` tinyint(1) NOT NULL DEFAULT '1' COMMENT '订单取消/退款弹窗提交 1开; 2关',
                                   `open_edit_take_out_order_no` tinyint(1) NOT NULL DEFAULT '1' COMMENT '开启编辑外卖单号 1开；2关',
                                   `allow_detect_order` tinyint(1) NOT NULL DEFAULT '1' COMMENT '允许检测单号 1开; 2关',
                                   `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   PRIMARY KEY (`id`),
                                   UNIQUE KEY `idx_order_id` (`order_id`,`app_order_no_check_enabled`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单号校验关联';


