CREATE TABLE `bwc_task_time_relation`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_id`         int(11) unsigned NOT NULL COMMENT 'bwc_task.id',
    `task_start_time` datetime  NOT NULL COMMENT '活动开始时间',
    `task_end_time`   datetime  NOT NULL COMMENT '活动结束时间',
    `data_state`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`     timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY               `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务时间段表';

CREATE TABLE `bwc_task_cycle_time_relation`
(
    `id`                   int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_cycle_create_id` int(11) unsigned NOT NULL COMMENT 'bwc_task_cycle_create.id',
    `task_start_time`      datetime  NOT NULL COMMENT '活动开始时间',
    `task_end_time`        datetime  NOT NULL COMMENT '活动结束时间',
    `data_state`           tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`          timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`          timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                    `idx_task_cycle_create_id` (`task_cycle_create_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='循环发布任务时间段表';


CREATE TABLE `bwc_order_task_time_relation`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_id`        int(11) unsigned NOT NULL COMMENT 'bwc_order.id',
    `task_id`         int(11) unsigned NOT NULL COMMENT 'bwc_task.id',
    `task_start_time` datetime  NOT NULL COMMENT '活动开始时间',
    `task_end_time`   datetime  NOT NULL COMMENT '活动结束时间',
    `data_state`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time`     timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`     timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY               `idx_order_id` (`order_id`),
    KEY               `idx_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单报名对应多任务时间段表';
