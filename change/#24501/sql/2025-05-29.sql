INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (NULL, '城市挑战赛查询（下单）', 'city_challenge_activity_order_query', 0, '', (select id from bwc_authority_temp as t where t.`key` = 'CityChallenge' and type = 0), 'operate', '城市挑战赛查询（下单）', 2, 0, '2025-03-10 14:50:07', '2025-03-10 14:50:07');
INSERT INTO `bwc_authority_temp` (`id`, `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`, `data_state`, `create_time`, `update_time`) VALUES (NULL, '城市挑战赛查询（邀请）', 'city_challenge_activity_invite_query', 0, '', (select id from bwc_authority_temp as t where t.`key` = 'CityChallenge' and type = 0), 'operate', '城市挑战赛查询（邀请）', 2, 0, '2025-03-10 14:50:07', '2025-03-10 14:50:07');


insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '城市挑战赛查询（下单）列表',
            'city_challenge_activity_order_list',
            1,
            '',
            t.id,
            'operate',
            '城市挑战赛查询（下单）列表',
            2
     from bwc_authority_temp as t
     where t.`key` = 'city_challenge_activity_order_query'
       and type = 0);
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '城市挑战赛查询（下单）-列表接口',
            'CityChallengeActivity.order_list',
            2,
            'POST',
            t.id,
            'operate',
            '城市挑战赛查询（下单）-列表接口',
            2
     from bwc_authority_temp as t
     where t.`key` = 'city_challenge_activity_order_list'
       and type = 1);


insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '城市挑战赛查询（邀请）列表',
            'city_challenge_activity_invite_list',
            1,
            '',
            t.id,
            'operate',
            '城市挑战赛查询（邀请）列表',
            2
     from bwc_authority_temp as t
     where t.`key` = 'city_challenge_activity_invite_query'
       and type = 0);
insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`, description, disabled)
    (select '城市挑战赛查询（邀请）-列表接口',
            'CityChallengeActivity.invite_list',
            2,
            'POST',
            t.id,
            'operate',
            '城市挑战赛查询（邀请）-列表接口',
            2
     from bwc_authority_temp as t
     where t.`key` = 'city_challenge_activity_invite_list'
       and type = 1);
