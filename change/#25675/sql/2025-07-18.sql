ALTER TABLE `bwc_suggestion`
    ADD COLUMN `handle_content` TEXT DEFAULT NULL COMMENT '处理结果',
  ADD COLUMN `handle_images` VARCHAR(1500) DEFAULT NULL COMMENT '处理截图',
  ADD COLUMN `follow_content` TEXT DEFAULT NULL COMMENT '跟进结果',
  ADD COLUMN `follow_images` VARCHAR(1500) DEFAULT NULL COMMENT '跟进截图';

ALTER TABLE `bwc_suggestion`
    ADD COLUMN `is_follow` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '跟进状态：1未跟进，2已跟进';

ALTER TABLE `bwc_suggestion`
    ADD COLUMN `follow_admin_id` INT(10) NOT NULL DEFAULT 0 COMMENT '跟进人ID',
  MODIFY COLUMN `deal_admin_id` INT(10) NOT NULL DEFAULT 0 COMMENT '处理人ID';

ALTER TABLE bwc_suggestion
    ADD COLUMN follow_time TIMESTAMP NULL DEFAULT NULL COMMENT '跟进时间';

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '用户意见反馈跟进', 'suggestion_follow', 1, '', t.`id`, 'user', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'suggestion' AND `type` = 0) AS t;

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '用户意见反馈跟进记录', 'suggestion_follow_detail', 1, '', t.`id`, 'user', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'suggestion' AND `type` = 0) AS t;

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '用户意见反馈处理记录', 'suggestion_handle_detail', 1, '', t.`id`, 'user', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'suggestion' AND `type` = 0) AS t;

INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '用户意见反馈跟进-接口', 'Suggestion.follow', 2, 'POST', t.`id`, 'user', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'suggestion_follow' AND `type` = 1) AS t;
