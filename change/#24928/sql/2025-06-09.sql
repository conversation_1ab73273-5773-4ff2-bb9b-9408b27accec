INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '通用活动是否开启传图审核，默认上线开关开启', 'is_general_activity_image_review_enabled', '1', '', 0);

ALTER TABLE `bwc_order_general`
    ADD COLUMN `enable_image_review` tinyint(1) NOT NULL DEFAULT '0' COMMENT '通用活动是否开启传图审核 (0=disabled, 1=enabled)';


ALTER TABLE `bwc_user_popup_setting`
    MODIFY COLUMN `type` int(11) NOT NULL DEFAULT '0' COMMENT '弹窗类型：1-试吃官超级返弹窗，2-关注公众号不再提醒弹窗，5-通用活动详情正在进行中';


