-- SELECT
--     *
-- FROM
--     bwc_store
-- WHERE
--     store_type IN (
--         SELECT
--             id
--         FROM
--             bwc_business_platform
--         WHERE
--             platform_abbreviation IN ( 'general_mt', 'general_ele' ));
--
--

-- 更新订单
update bwc_order set platform_id = 1 where store_id IN (521318, 524884);
update bwc_order set platform_id = 2 where store_id IN (518660, 525096);

-- 更新任务
update bwc_task set task_type = 1 where store_id IN (521318, 524884);
update bwc_task set task_type = 2 where store_id IN (518660, 525096);


-- 更新店铺
update bwc_store set store_type = 1 where id IN (521318, 524884);
update bwc_store set store_type = 2 where id IN (518660, 525096);


