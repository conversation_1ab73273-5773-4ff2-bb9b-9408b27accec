INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`)
    (select '实名记录', 'show_identity_card_no', 1, '', t.id, 'user', '实名记录组件', 2 from bwc_authority_temp as t where t.key= 'user' and type=0);
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description`, `disabled`)
    (select '实名记录接口', 'user.show_identity_card_no', 2, 'POST', t.id, 'user', '实名记录接口', 2 from bwc_authority_temp as t where t.key= 'show_identity_card_no' and type=1);

-- 添加索引
ALTER TABLE `bwc_user_identity_record`
    ADD INDEX `idx_identity_card_id`(`identity_card_id`) USING BTREE;
