-- 灰太狼订单机审情况和灰太狼审核情况表
CREATE TABLE `bwc_order_htl_status` (
                                        `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                        `order_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',
                                        `machine_audit_status` int(11) NOT NULL DEFAULT '0' COMMENT '订单机审状态 0未通过 1通过',
                                        `htl_audit_status` int(11) NOT NULL DEFAULT '0' COMMENT '灰太狼审核状态  0未通过; 1通过',
                                        `htl_audit_data` varchar(2000) NOT NULL DEFAULT '' COMMENT '灰太狼审核原始数据',
                                        `machine_audit_data` varchar(2000) NOT NULL DEFAULT '' COMMENT '机审原始数据',
                                        `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        PRIMARY KEY (`id`),
                                        KEY `idx_order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='灰太狼订单机审情况和灰太狼审核情况表';
