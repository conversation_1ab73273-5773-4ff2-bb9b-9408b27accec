insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`,description, disabled)
VALUES('用户延迟审核配置', 'user_delayed_audit_config', 0, '', 0, 'system', '', 2);

insert into bwc_authority_temp(`name`, `key`, type, method, parent_id, `group`,description, disabled)
select '用户延迟审核配置提交', 'user_delayed_audit_config_submit', 1, '', id, 'system', '', 2 from bwc_authority_temp where `key`='user_delayed_audit_config' and type=0;

INSERT INTO `bwc_config` (`type`, `name`, `key`, `value`, `group`, `platform`) VALUES ('string', '用户延迟审核配置版本', 'delayed_audit_config_version', '30080', '', 0);

CREATE TABLE `bwc_delayed_audit_config`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `type`        varchar(50)  NOT NULL DEFAULT '' COMMENT '分类：审核登记配置audit_level_config，用户来源：user_source_config',
    `sub_type`    varchar(50)  NOT NULL DEFAULT '' COMMENT '子类:为空的时候，key-name等价id-name',
    `third_type`  varchar(50)  NOT NULL DEFAULT '' COMMENT '三级目录：为空的时候，不存在三级',
    `key`         varchar(100) NOT NULL DEFAULT '' COMMENT 'key',
    `name`        varchar(50)  NOT NULL DEFAULT '' COMMENT '名称',
    `value`       varchar(50)  NOT NULL DEFAULT '' COMMENT 'value',
    `data_state`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0正常，1删除',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ('audit_level_config', '', '', '1', '严格审核', '5');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ('audit_level_config', '', '', '2', '中等审核', '3');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ('audit_level_config', '', '', '3', '宽松审核', '1');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ('audit_level_config', '', '', '4', '信任审核', '0');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ('user_source_config', 'user_promoter', 'first', 'min', '', '0');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ('user_source_config', 'user_promoter', 'first', 'max', '', '10');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ('user_source_config', 'user_promoter', 'first', 'default_level', '', '2');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ('user_source_config', 'user_promoter', 'first', 'cash_back_amount_min', '', '20');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ('user_source_config', 'user_promoter', 'first', 'cash_back_amount_level', '', '1');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'first', 'continuous_order_day', '', '24');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'first', 'continuous_order_num', '', '2');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'first', 'continuous_order_level', '', '1');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'second', 'min', '', '10');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'second', 'max', '', '20');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'second', 'default_level', '', '3');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'second', 'cash_back_amount_min', '', '20');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'second', 'cash_back_amount_level', '', '2');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'second', 'continuous_order_day', '', '24');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'second', 'continuous_order_num', '', '2');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'second', 'continuous_order_level', '', '2');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'third', 'min', '', '20');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'third', 'max', '', '0');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'third', 'default_level', '', '4');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'third', 'cash_back_amount_min', '', '20');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'third', 'cash_back_amount_level', '', '3');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'third', 'continuous_order_day', '', '24');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'third', 'continuous_order_num', '', '2');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'user_promoter', 'third', 'continuous_order_level', '', '3');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'channel_promotion', 'first', 'default_level', '', '4');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'channel_promotion', 'first', 'cash_back_amount_min', '', '20');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'channel_promotion', 'first', 'cash_back_amount_level', '', '2');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'channel_promotion', 'first', 'continuous_order_day', '', '24');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'channel_promotion', 'first', 'continuous_order_num', '', '2');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'channel_promotion', 'first', 'continuous_order_level', '', '2');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'channel_promotion', 'second', 'default_level', '', '4');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'channel_promotion', 'second', 'cash_back_amount_min', '', '20');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'channel_promotion', 'second', 'cash_back_amount_level', '', '3');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'channel_promotion', 'second', 'continuous_order_day', '', '24');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'channel_promotion', 'second', 'continuous_order_num', '', '2');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'channel_promotion', 'second', 'continuous_order_level', '', '3');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'nature', 'first', 'default_level', '', '2');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'nature', 'first', 'cash_back_amount_min', '', '20');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'nature', 'first', 'cash_back_amount_level', '', '1');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'nature', 'first', 'continuous_order_day', '', '24');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'nature', 'first', 'continuous_order_num', '', '2');
INSERT INTO `bwc_delayed_audit_config` (`type`, `sub_type`, `third_type`, `key`, `name`, `value`) VALUES ( 'user_source_config', 'nature', 'first', 'continuous_order_level', '', '1');