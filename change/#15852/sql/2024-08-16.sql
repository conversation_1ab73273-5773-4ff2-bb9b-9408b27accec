-- 增加bwc_user_ext表
CREATE TABLE `bwc_user_ext` (
                                `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                `limit_self_mt` tinyint(1) NOT NULL DEFAULT '0' COMMENT '限制用户能否看见自营的美团数据 1限制; 2不限制',
                                `is_first` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否首次风控',
                                `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '操作人id',
                                `user_id` int(11) NOT NULL COMMENT '用户id',
                                `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                PRIMARY KEY (`id`) USING BTREE,
                                KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;

-- 增加bwc_user_risk_record表
CREATE TABLE `bwc_user_risk_record` (
                                        `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `admin_id` int(11) NOT NULL DEFAULT '0' COMMENT '操作人id',
                                        `user_id` int(11) NOT NULL COMMENT '用户id',
                                        `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                        `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                        PRIMARY KEY (`id`) USING BTREE,
                                        KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- 增加权限
INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description` )
SELECT '风控标记', 'user_limit_user_see_self_mt', 1, '', t.`id`, 'user', '风控标记'
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'user' AND `type` = 0) AS t;

INSERT INTO `bwc_authority_temp` ( `name`, `key`, `type`, `method`, `parent_id`, `group`, `description` )
SELECT '风控标记', 'user.limit_user_see_self_mt', 2, 'POST', `t`.`id`, 'user', ''
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'user_limit_user_see_self_mt' AND `type` = 1) AS t;