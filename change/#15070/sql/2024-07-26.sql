-- 修改business_platform表结构
ALTER TABLE `bwc_business_platform` ADD COLUMN `is_order_reminder` TINYINT ( 1 ) NOT NULL DEFAULT 2 COMMENT '下单提醒开关，1开；2关' AFTER `machine_audit`,
ADD COLUMN `reminder_frequency` TINYINT ( 1 ) NOT NULL DEFAULT 2 COMMENT '提醒频率 1仅提醒一次；2总是提醒' AFTER `is_order_reminder`,
ADD COLUMN `reminder_text` VARCHAR ( 1500 ) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提醒文案' AFTER `reminder_frequency`,
ADD COLUMN `modify_frequency_time` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '修改频率的时间戳' AFTER `reminder_text`,
ADD COLUMN `order_submit_time` INT ( 11 ) NOT NULL DEFAULT 0 COMMENT '下单倒计时时间' AFTER `modify_frequency_time`;

-- 增加订单时间表
CREATE TABLE `bwc_order_time` (
                                  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单id',
                                  `submit_live_time` int(11) NOT NULL DEFAULT '0' COMMENT '提交订单的持续时间',
                                  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                  PRIMARY KEY (`id`),
                                  KEY `idx_order_id` (`order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 修改美团官方默认提示
UPDATE `bwc_business_platform`
SET `is_order_reminder` = 2,
    `reminder_frequency` = 2,
    `reminder_text` = '该活动需在报名后{highlightStart}30分钟完成下单{highlightEnd}，超时名额会被取消，请及时前往平台完成下单。',
    `modify_frequency_time` = 1722319212,
    `order_submit_time` = 60
WHERE
        `platform_abbreviation` = 'mtg';

