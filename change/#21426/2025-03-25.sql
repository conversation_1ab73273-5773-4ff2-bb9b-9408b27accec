ALTER TABLE `bwc_store`
    ADD COLUMN `area_ad_code` varchar(32) NOT NULL DEFAULT 0 COMMENT '所属区县code' AFTER `agent_id`;
ALTER TABLE `bwc_store`
    ADD INDEX `idx_area_ad_code`(`area_ad_code`) USING BTREE;
ALTER TABLE `bwc_store_repo`
    ADD COLUMN `area_ad_code` varchar(32) NOT NULL DEFAULT 0 COMMENT '所属区县code' AFTER `agent_id`;

-- 更新页面权限
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '城市区域数据', 'agent_area_data', 1, '', t.`id`, 'data_dashboard', '城市区域数据列表'
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'agent_dashboard' AND `type` = 0) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '城市区域数据index接口', 'AgentArea.index', 2, 'GET', t.`id`, 'data_dashboard', '城市区域数据index接口'
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'agent_area_data' AND `type` = 1) AS t;
INSERT INTO `bwc_authority_temp` (`name`, `key`, `type`, `method`, `parent_id`, `group`, `description`)
SELECT '城市区域数据列表接口', 'AgentArea.list', 2, 'GET', t.`id`, 'data_dashboard', '城市区域数据列表接口'
FROM (SELECT `id` FROM `bwc_authority_temp` WHERE `key` = 'agent_area_data' AND `type` = 1) AS t;
