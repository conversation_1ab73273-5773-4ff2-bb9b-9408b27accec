<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2021/7/18 11:53
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------

return [
    "roles" => [
        'super' => '',
        'admin' => '',
    ],
    "token_key" => 'xxyx',
    "wechat_offcial_account_appid" => env('WECHAT_OFFCIAL_ACCOUNTS_APPID', 'wxe849c37079e9996d'),
    "wechat_offcial_account_secret" => env('WECHAT_OFFCIAL_ACCOUNTS_SECRET', 'd45ed7d04f008891ddcd147c2c8cbec2'),
    "wechat_offcial_account_appid2" => env('WECHAT_OFFCIAL_ACCOUNTS_APPID2', 'wx89c3f351e3388de8'),
    "wechat_offcial_account_secret2" => env('WECHAT_OFFCIAL_ACCOUNTS_SECRET2', '6c5f5207f72c1ddea579e33b4399a80f'),
    "activity_wechat_offcial_account_appid" => env('ACTIVITY_WECHAT_OFFCIAL_ACCOUNTS_APPID', 'wx4f350116f154717c'),
    "activity_wechat_offcial_account_secret" => env('ACTIVITY_WECHAT_OFFCIAL_ACCOUNTS_SECRET', 'a7a17d29369aadcae9dba4d1aeda3322'),
    "wechat_applet_appid" => env('WECHAT_APPLET_APPID', 'wx37ff4314df1b0450'),
    "wechat_applet_secret" => env("WECHAT_APPLET_SECRET", 'c8dd89c74b16c7ce766e8333540cbe1b'),

    "wechat_mobile_app_appid" => env('WECHAT_MOBILE_APP_APPID', 'wx33f2e0345896f56f'),
    "wechat_mobile_app_secret" => env('WECHAT_MOBILE_APP_SECRET', '2312ad9a7586fd1a8eb1a8ac7c9cfca5'),
    "wechat_dlm_ios_mobile_app_appid" => env('WECHAT_OPEN_PLATFORM_APPID_FOR_DLM_IOS', 'wxedb0e6a613ded0c6'),
    "wechat_dlm_ios_mobile_app_secret" => env('WECHAT_OPEN_PLATFORM_SECRET_FOR_DLM_IOS', 'f00429c889768d88a161bcb61773b282'),
    "wechat_dlm_android_mobile_app_appid" => env('WECHAT_OPEN_PLATFORM_APPID_FOR_DLM_ANDROID', 'wx8bd2fabd1303c996'),
    "wechat_dlm_android_mobile_app_secret" => env('WECHAT_OPEN_PLATFORM_SECRET_FOR_DLM_ANDROID', 'b4a3aff661f2de532a04164ae6cbeeab'),

    //微信小程序授权账号
    "wechat_applet_app_appid" => env('WECHAT_APPLET_APP_APPID', 'wx70b6bc21adce9cf4'),
    "wechat_applet_app_secret" => env('WECHAT_APPLET_APP_SECRET', '5e436903602c26472abf1281fefcddac'),

    //微信小程序授权账号(air)
    "wechat_applet_air_app_appid" => env('WECHAT_APPLET_AIR_APP_APPID', 'wx82042dd743cb5160'),
    "wechat_applet_air_app_secret" => env('WECHAT_APPLET_AIR_APP_SECRET', '79236ff727931422571803aac75fd5eb'),

    //浩熠支付宝打款给商家的appid
    'hy_pay_appid' => env('HY_PAY_APPID', '2021004144697993'),
    // 日常支出 邀请奖励、金币提现、中间人、推广等支出
    'gold_alipay_appid' => env('GOLD_ALIPAY_APPID', '2021003174663271'),
    // 利润支出 用户返现、积分提现 晓晓惠点餐
    'score_alipay_appid' => env('SCORE_ALIPAY_APPID', '2021003174667268'),
    'redirect_base_host' => env('REDIRECT_BASE_HOST', 'https://x-y.icu/'),

    'client_url' => 'https://' . env('HOME_BASE_HOST', 'xxyx-client-api.xiaoxiaoyouxuan.com') . '/',

    'admin_url' => 'https://' . env('ADMIN_BASE_HOST', 'xxyx-admin-api.xiaoxiaoyouxuan.com') . '/',


    'seller_web_url' => env('SELLER_WEB_URL', 'https://seller.xx-yx.cc/'),

    'seller_api_url' => env('SELLER_API_URL', 'https://xxyx-seller-api.xiaoxiaoyouxuan.com/'),

    'seller_web_new_url' => env('SELLER_WEB_NEW_URL', 'https://seller-bill.xiaoxiaoyouxuan.com/'),

    'api_url' => 'https://' . env('API_BASE_HOST', 'xxyx-public-api.xiaoxiaoyouxuan.com') . '/',

    'app_push' => [
        'app_id' => env('APP_PUSH.APP_ID', '8q2PBHQgnE9ysMdD6BFya8'),
        'app_key' => env('APP_PUSH.APP_KEY', 'lzjnH3r1Q46raW3AokLTc1'),
        'master_secret' => env('APP_PUSH.MASTER_SECRET', 'XaQtmic60pAiFUgQ9yQMw4'),
    ],
    'logo_url' => 'https://image.xiaoxiaoyouxuan.com/admin/image_2022080514155127de72f1ec.png',
    'qr_logo_url' => 'https://image.xiaoxiaoyouxuan.com/order/image_20230306145912c86538e994.png',

    // 商家账单公众号
    'seller_bill_wechat_appid' => env('SELLER_BILL_WECHAT_APPID', 'wx76954481cd53d253'),
    'seller_bill_wechat_secret' => env('SELLER_BILL_WECHAT_SECRET', '117db5d2662797e45924d45964d66edc'),
    // 首易信支付
    'pay_easy_net_merchant_id' => env('PAY_EASY_NET_MERCHANT_ID', '891857272'),
    'pay_easy_net_private_path' => '../secret/xxyx_cert/xxyx_private.pfx',
    'pay_easy_net_public_path' => '../secret/pay_easy_net/test.cer',
    'pay_easy_net_private_psd' => '123456',
    'pay_easy_net_alipay_app_id' => '2021003125667194',
    // 商家支付公众号
    'pay_easy_net_wechat_appid' => env('SELLER_BILL_WECHAT_APPID', 'wx4287659b06d9230a'),
    'pay_easy_net_wechat_secret' => env('SELLER_BILL_WECHAT_SECRET', '0e620bc9752cd552e6472db60e711123'),
    // 商家支付小程序
    'pay_easy_net_wechat_app_appid' => env('SELLER_BILL_WECHAT_APP_APPID', 'wx5707a1c6a0ff2d5e'),
    'pay_easy_net_wechat_app_app_secret' => env('SELLER_BILL_WECHAT_APP_APP_SECRET', '3fa478051818ec284d00018e7c079677'),

    // cos/cdn
    'cos_host' => env('COS_HOST', 'xxyx-1310097496.cos.ap-shanghai.myqcloud.com'),
    'cdn_image_host' => env('CDN_IMAGE_HOST', 'image.xiaoxiaoyouxuan.com'),

    'http_rpc_hyperf_service_host' => env('HTTP_RPC_HYPERF_SERVICE_HOST', 'http://hyperf-service:9501/rpc/http'),
    'http_rpc_java_host' => env('HTTP_RPC_JAVA_HOST', 'http://java-xxpay:8080'),

    'http_rpc_java_workstation_host' => env('HTTP_RPC_JAVA_WORKSTATION_HOST', 'http://java-xxworkstation:8080'),

    'http_rpc_java_open_host' => env('HTTP_RPC_JAVA_OPEN_HOST', "http://java-xxopen:8080"),

    'http_rpc_java_ccyf_host' => env('HTTP_RPC_JAVA_CCYF_HOST', "http://java-catermate-admin:8080"),

    'http_rpc_java_message_host' => env('HTTP_RPC_JAVA_MESSAGE_HOST', 'http://java-xxmessage:8080'),

    'http_client_java_service_host' => env('HTTP_CLIENT_JAVA_SERVICE_HOST', 'http://java-xxclient:8080'),

    'http_java_task_center_host' => env('HTTP_JAVA_TASK_CENTER_HOST', 'https://xxyx-task-center-api.xiaoxiaoyouxuan.com'),
    'http_java_task_center_app_id' => env('HTTP_JAVA_TASK_CENTER_APP_ID', '2025010637347162'),
    'http_java_task_center_app_secret' => env('HTTP_JAVA_TASK_CENTER_APP_SECRET', 'DDZx61bIOS5UkpbLadwIBFqQZbYgM97J'),

    'without_date_time' => env('WITHOUT_DATE_TIME', '2023-09-15 00:00:00'),
    // h5链接
    'h5_url' => env('WEB_BASE_URL', 'https://plus.web.xxyx.ltd/#/'),

    'promotion_url' => env('PROMOTION_URL', 'https://xxyx.web.xxyx.ltd/#/?mark='),

    'agent_activity_host' => env('AGENT_ACTIVITY_HOST', 'https://activity.web.xxyx.ltd'),

    'jpush_login_token_mobile' => env('JPUSH_LOGIN_TOKEN_MOBILE', 'https://api.verification.jpush.cn/v1/web/loginTokenVerify'),

    'jpush_login_token_web' => env('JPUSH_LOGIN_TOKEN_WEB', 'https://api.verification.jpush.cn/v1/web/h5/loginTokenVerify'),

    'htl_base_url' => env('HTL_BASE_URL', 'https://part.szhsbwc.top'),

    'alipay' => [
        'app_id' => env('ALIPAY_APP_ID', '2021003174667268'),
        'merchant_private_key' => dirname(__DIR__) . '/secret/alipay/appPrivateKey.txt',
        'alipay_cert_key' => dirname(__DIR__) . '/secret/alipay/alipayCertPublicKey_RSA2.crt',
        'alipay_root_cert_path' => dirname(__DIR__) . '/secret/alipay/alipayRootCert.crt',
        'merchant_cert_path' => dirname(__DIR__) . '/secret/alipay/appCertPublicKey_2021003174667268.crt',
    ],

    'encrypt_key' => env('ENCRYPT_KEY', 'm26a5e48k9e8'),
    'tencentCloudConfig' => [
        'secretId' => env('TENCENT_CLOUD.SECRET_ID', 'AKIDb4KMAj4AMQGsGQM3WotkMkuqX1HJ9l8H'),
        'secretKey' => env('TENCENT_CLOUD.SECRET_KEY', 'yhCUXLvIRL0RdaIl12pOjqiLmzxmQq8F'),
        'region' => env('TENCENT_CLOUD.REGION', 'ap-shanghai'),
    ],
    'tencentCloudProductsConfig' => [
        'rocketMQ' => [
            'ClusterId' => env('TENCENT_CLOUD_ROCKETMQ.CLUSTER_ID', 'rocketmq-8j7b7v5kmmbg'),
            'NamespaceId' => env('TENCENT_CLOUD_ROCKETMQ.NAMESPACE_ID', 'prod'),
        ],
    ],
];
