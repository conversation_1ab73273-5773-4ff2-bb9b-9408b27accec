<?php

// +----------------------------------------------------------------------
// | 日志设置
// +----------------------------------------------------------------------
return [
    // 默认日志记录通道
    'default'      => env('log.channel', 'file'),
    // 日志记录级别
    'level'        => ['error', 'info', 'sql', 'debug'],
    // 日志类型记录的通道 ['error'=>'email',...]
    'type_channel' => [],
    // 关闭全局日志写入
    'close'        => false,
    // 全局日志处理 支持闭包
    'processor'    => null,

    // 日志通道列表
    'channels'     => [
        'file' => [
            // 日志记录方式
            'type'           => 'File',
            // 日志保存目录
            'path'           => '../runtime/log/system',
            // 单文件日志写入
            'single'         => true,
            // 独立日志级别
            'apart_level'    => ['error', 'info', 'debug', 'critical'],
            // 最大日志文件数量
            'max_files'      => false,
            // 使用JSON格式记录
            'json'           => true,
            // 日志处理
            'processor'      => null,
            // 关闭通道日志写入
            'close'          => false,
            // 时间格式化
            'time_format'   =>    'Y-m-d H:i:s',
            // 日志输出格式化
            'format'         => '[%s][%s] %s',
            // 是否实时写入
            'realtime_write' => false,
        ],
        'analysis' => [
            // 日志记录方式
            'type'           => \app\Logger::class,
            // 日志保存目录
            'path'           => '../runtime/log/analysis',
            // 单文件日志写入
            'single'         => true,
            // 独立日志级别
            'apart_level'    => ['error', 'info', 'debug', 'critical'],
            // 最大日志文件数量
            'max_files'      => false,
            // 使用JSON格式记录
            'json'           => true,
            // 日志处理
            'processor'      => null,
            // 关闭通道日志写入
            'close'          => false,
            // 时间格式
            'time_format'   =>    'Y-m-d H:i:s',
            // 日志输出格式化
            'format'         => '[%s][%s] %s',
            // 是否实时写入
            'realtime_write' => false,
        ]
    ]

];
