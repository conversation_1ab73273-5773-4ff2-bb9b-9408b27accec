{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "http://thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.4.1", "topthink/framework": "^6.0.0", "topthink/think-orm": "^2.0", "topthink/think-multi-app": "^1.0", "liliuwei/thinkphp-jump": "^1.5", "edward1108/edward-captcha": "^1.1", "ext-json": "*", "ext-redis": "*", "qcloud/cos-sdk-v5": ">=2.0", "ext-openssl": "*", "ext-bcmath": "*", "ext-curl": "*", "endroid/qr-code": "^4.3", "php-amqplib/php-amqplib": "~2.8.0", "wechatpay/wechatpay": "^1.4", "phpoffice/phpexcel": "^1.8.2", "phpoffice/phpspreadsheet": "^1.20", "topthink/think-image": "^1.0", "ext-gd": "*", "qcloud_sts/qcloud-sts-sdk": "3.0.6", "tencentcloud/tencentcloud-sdk-php": "^3.0", "ext-mbstring": "*", "alipaysdk/easysdk": "^2.2"}, "require-dev": {"symfony/var-dumper": "^4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app"}, "classmap": ["extend/"]}, "config": {"preferred-install": "dist", "secure-http": false}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}, "repositories": {"packagist": {"type": "composer", "url": "https://mirrors.tencent.com/composer/"}}}