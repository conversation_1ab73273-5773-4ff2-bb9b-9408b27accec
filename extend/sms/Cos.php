<?php


namespace sms;


use app\admin\exception\CurrentException;
use app\common\model\SmsChannel;
use app\common\model\SmsTemplate;
use app\exception\SmsException;
use http\Api;

/**
 * 短信宝SMS
 */
class Cos implements InterfaceSMS
{
    /**
     * 渠道编码
     */
    const CODE_CAPTCHA_SMS = 'COS_CAPTCHA_SMS';

    /**
     * 请求对象
     * @var Api
     */
    protected Api $api;

    /**
     * 配置信息
     * @var array|mixed
     */
    protected array $config = [];

    /**
     * 发送内容
     * @var string
     */
    protected string $text = '';

    /**
     * @throws SmsException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function __construct()
    {
        $channel = SmsChannel::get_one([
            'code' => self::CODE_CAPTCHA_SMS,
            'enabled' => 1
        ]);
        if (empty($channel)) {
            throw new SmsException('Cos短信配置获取失败');
        }
        $this->config = json_decode($channel['config'], true);
        /** @var Api $request */
        $request = app(Api::class);
        $this->api = $request;
    }

    /**
     * 配置短信模板
     * @param $templateCode
     * @param array $data
     * @param string $text
     * @return $this|false
     */
    public function setText($templateCode, array $data = [], string $text = '')
    {
        if (!$text) {
            $smsTemplateModel = app(SmsTemplate::class);
            $text = $smsTemplateModel->fillTemplate($templateCode, $data);
            if (!$text) {
                return false; //没有配置默认模板，或模板内容为空
            }
        }
        $this->text = $text;

        return $this;
    }

    /**
     * 发送短信
     * @param string $mobile
     * @throws \app\admin\exception\CurrentException
     */
    public function send(string $mobile)
    {
        $cosParams = array('u' => $this->config['username'], 'p' => $this->config['apiKey'], 'm' => $mobile, 'c' => $this->text);
        /** @var Api $request */
        $this->api->resetConfig($this->config['server'], 'get', $cosParams);
        $this->api->request();
        $result = $this->api->getResponseBody();
        if ($result !== "0") {
            throw new CurrentException('发送失败');
        }
    }
}