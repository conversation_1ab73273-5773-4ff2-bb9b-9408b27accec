<?php


namespace sms;


use http\Api;

abstract class AbstractSMS
{
    protected array $conf = [];

    protected string $result = '';

    protected bool $success = false;

    const CODE_COC_SMS = 'COC_SMS';

    /**
     * AbstractSMS constructor.
     * @param array $conf
     */
    abstract public function __construct($conf = []);

    /**
     * 发送短信
     * @param string $mobile
     * @param string $text
     */
    abstract public function send($mobile = '',$text = '');


    abstract public function getSuccess();

    public function getResult()
    {
        return $this->result;
    }
}