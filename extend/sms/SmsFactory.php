<?php

namespace sms;

use app\common\model\{SmsChannel, SmsTemplate};
use app\exception\SmsException;

class SmsFactory
{
    const TENCENT_SMS = 'TENCENT_SMS';

    const COS_SMS = 'COS_SMS';

    public function createSms($channelCode)
    {
        switch ($channelCode) {
            case self::TENCENT_SMS:
                return $this->createTencentSms();
            case self::COS_SMS:
                return $this->createCosSms();
        }
    }

    public function createTencentSms(): Tencent
    {
        return new Tencent();
    }

    public function createCosSms(): Cos
    {
        return new Cos();
    }
}