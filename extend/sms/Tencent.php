<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/4/20 9:34
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace sms;

// 导入对应产品模块的client
use app\common\model\SmsChannel;
use app\exception\SmsException;
use TencentCloud\Sms\V20210111\SmsClient;
// 导入要请求接口对应的Request类
use TencentCloud\Sms\V20210111\Models\SendSmsRequest;
use TencentCloud\Common\Credential;

class Tencent implements InterfaceSMS
{
    const CODE_CAPTCHA_SMS = 'TENCENT_CAPTCHA_SMS';

    const CODE_NOTICE_SMS = 'TENCENT_NOTICE_SMS';

    const CODE_ORDER_WARNING_SMS = 'TENCENT_ORDER_WARNING_SMS';

    protected array $config;

    protected Credential $cred;

    protected SmsClient $client;

    protected SendSmsRequest $req;

    protected string $defPrefix = '+86';

    public function setText($channel_code, $data): Tencent
    {
        $channel = SmsChannel::get_one(['code' => $channel_code, 'enabled' => 1]);
        if (empty($channel)) {
            throw new SmsException('腾讯云短信配置获取失败');
        }
        $this->config = json_decode($channel['config'], true);

        $this->cred = new Credential($this->config['SecretId'], $this->config['SecretKey']);
        $this->client = new SmsClient($this->cred, "ap-guangzhou");
        $this->req = new SendSmsRequest();
        $this->req->SmsSdkAppId = $this->config['SDKAppID'];
        $this->req->SignName = $this->config['SignName'];
        $this->req->TemplateId = $this->config['TemplateId'];
        $this->req->TemplateParamSet = $data;
        return $this;
    }

    public function send(string $mobile): \TencentCloud\Sms\V20210111\Models\SendSmsResponse
    {
        $this->req->PhoneNumberSet = [$this->defPrefix . $mobile];
        $result = $this->client->SendSms($this->req);
        return $result;
    }
}