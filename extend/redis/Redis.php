<?php


namespace redis;

use app\admin\exception\CurrentException;
use app\Log;
use app\common\facade\Redis as FRedis;

/**
 * Class Redis
 * @mixin \Redis
 */
class Redis
{
    public static string $user_login_token = 'user:login:token';

    public static string $user_login_sms_code = 'user:login:sms_code';

    public static string $api_access = 'api:access';

    public static string $task_count_number = 'task:count_number';

    public static string $user_login_sms_check = 'user:login:sms_check';

    public static string $wx_web_access_token = 'wx:web:access_token';

    public static string $wx_web_ticket = 'wx_web:ticket';

    public static string $wx_web_user_access_token = 'wx_web:user:access_token';

    public static string $wx_web_user_refresh_token = 'wx_web:user:refresh_token';

    public static string $wx_web_user_openid = 'wx_web:user:openid';

    public static string $task_settlement_page_code = 'task:settlement:page:code';

    public static string $order_creating_user_id = 'order:creating:user_id';

    public static string $app_user_token = 'app_user:token';

    public array $geoKey = [];

    // 公共前缀
    public string $prefix = 'THINK_XXH';

    // redis实例
    public ?\Redis $_instance = null;

    public ?\Redis $_geoInstance = null;

    public array $config = [
        //
        'host' => '***********',

        'port' => '6379',

        'auth' => 'Duwenyi@1688',

        'default_number' => '',

        'key_prefix' => 'xxh_bwc',
    ];

    public array $geoConfig = [
        //
        'host' => '***********',

        'port' => '6379',

        'auth' => 'Duwenyi@1688',

        'default_number' => '',

        'key_prefix' => 'xxh_bwc',
    ];

    /**
     * 防止克隆对象
     */
    private function __clone()
    {
        // TODO: Implement __clone() method.
    }

    public function __construct()
    {
        $config = config('database.connections.redis');
        $this->config = array_merge($this->config, $config);

        $geoConfig = config('database.connections.geo_redis');
        $this->geoConfig = array_merge($this->geoConfig, $geoConfig);

        $this->geoKey[] = $this->getFullKeyName(FRedis::$task_list_date_geo);
    }

    private function checkGeo($arg): bool
    {
        $isGeo = false;

        if (!is_string($arg)) {
            return false;
        }

        array_map(function ($item) use ($arg, &$isGeo) {
            $isGeo = strpos($arg, $item) !== false;
        }, $this->geoKey);

        return $isGeo;
    }

    /**
     * 自动组合redis key
     * like getFullKeyName(1352134578, ['register', 'sms']) => 'JHH:register:sms:1352134578'
     * @param string $str
     * @param array|string $end
     * @return bool|string
     */
    public function getFullKeyName(string $str = '', $end = array())
    {
        if (empty($str)) {
            return false;
        }
        $key = config('database.connections.redis.key_prefix') ? config('database.connections.redis.key_prefix') : $this->prefix;
        if (!empty($end)) {
            $key = $key . ':' . (is_array($end) ? implode(':', $end) : $end);
        }
        return  $key . ':' . $str;
    }

    private function getInstance($client, $config): ?\Redis
    {
        if (is_null($client)) {
            try {
                // 连接redis
                $client = new \Redis();
                $client->connect($config['host'], $config['port']);

                if (!empty($config['auth'])) {
                    $client->auth($config['auth']);
                }

                // 如果有配置默认数据库的话，切到默认数据库
                if (!empty($config['default_number'])) {
                    $client->select($config['default_number']);
                }
            } catch (\Exception $exception) {
                Log::error('redisConnectError', $exception, 'redis链接错误', '');
                throw new CurrentException($exception->getMessage());
            }
        }

        return $client;
    }

    /**
     * @param $name
     * @param $arguments
     * @return false|mixed
     * @throws CurrentException
     */
    public function __call($name, $arguments)
    {
        $isGeo = false;

        // 遍历参数数组
        foreach ($arguments as $arg) {
            // 检查参数是否是字符串类型，并且是否包含指定的值
            if ($this->checkGeo($arg)) {
                $isGeo = true;
            }
        }

        if ($isGeo) {
            $client = $this->getInstance($this->_geoInstance, $this->geoConfig);
        } else {
            $client = $this->getInstance($this->_instance, $this->config);
        }

        return $client ? call_user_func_array([$client, $name], $arguments) : false;
    }

}
