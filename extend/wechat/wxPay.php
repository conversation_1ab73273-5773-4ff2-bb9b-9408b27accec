<?php


namespace wechat;

use app\admin\exception\CurrentException;
use app\client\service\WechatService;
use think\facade\App;
use WeChatPay\Builder;
use WeChatPay\Crypto\Rsa;
use WeChatPay\Util\PemUtil;

class wxPay
{
   protected string $merchantId = '1615166726';

   protected string $merchantPrivateKeyFilePath;

   protected $merchantPrivateKeyInstance;

   protected string $platformCertificateFilePath;

   protected $platformPublicKeyInstance;

    protected string $platformCertificateSerial;

   protected string $merchantCertificateSerial = '3DB2F8D0AFC6DD2C7E06052D7CCDCC44B01EA432';

   protected string $appId;

    /**
     * @var \WeChatPay\BuilderChainable
     */
    private \WeChatPay\BuilderChainable $instance;


    public function __construct()
   {
       [$this->appId,] = invoke([WechatService::class, 'choseWechatOffcialAccountAppInfo']);

       $this->merchantPrivateKeyFilePath = 'file://' . App::getRootPath() . 'public/static/wechat/merchant/apiclient_key.pem';
       $this->merchantPrivateKeyInstance = Rsa::from($this->merchantPrivateKeyFilePath, Rsa::KEY_TYPE_PRIVATE);
       $this->platformCertificateFilePath = 'file://' . App::getRootPath() . 'public/static/wechat/merchant/cert.pem';// 注意 `file://` 开头协议不能少
        // 加载「平台证书」公钥
        $this->platformPublicKeyInstance = Rsa::from($this->platformCertificateFilePath, Rsa::KEY_TYPE_PUBLIC);
        // 解析「平台证书」序列号，「平台证书」当前五年一换，缓存后就是个常量
        $this->platformCertificateSerial = PemUtil::parseCertificateSerialNo($this->platformCertificateFilePath);

       $this->instance = Builder::factory([
           'mchid'      => $this->merchantId,
           'serial'     => $this->merchantCertificateSerial,
           'privateKey' => $this->merchantPrivateKeyInstance,
           'certs'      => [
               $this->platformCertificateSerial => $this->platformPublicKeyInstance,
           ],
       ]);
   }

   public function jsapi($order_no, $description, $callbackUrl, $price, $openid, $time_expire = null)
   {
       try {
           $data = [
               'mchid'        => $this->merchantId,
               'out_trade_no' => $order_no,
               'appid'        => $this->appId,
               'description'  => $description,
               'notify_url'   => $callbackUrl,
               'amount'       => [
                   'total'    => $price,
                   'currency' => 'CNY'
               ],
               'payer' => [
                   'openid' => $openid
               ]
           ];
           if (!empty($time_expire)) {
               $data['time_expire'] = $time_expire;
           }
           $resp = $this->instance
               ->v3->pay->transactions->jsapi
               ->post(['json' => $data]);

           return $resp->getBody();
       } catch (\Exception $e) {
           // 进行错误处理
           throw new CurrentException('not find page', 404);
       }
   }

   public function sign($message)
   {
       openssl_sign($message, $raw_sign, $this->merchantPrivateKeyInstance, 'sha256WithRSAEncryption');
       return base64_encode($raw_sign);
   }
}