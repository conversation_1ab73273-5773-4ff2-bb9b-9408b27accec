<?php

use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

class Rabbit
{
    # 交换机

    /*
     * 任务浏览统计
     */
    const EXT_TASK_VIEWS = 'xxyx.task.views';

    /*
     * 美团任务浏览统计
     */
    const EXT_MT_TASK_VIEWS = 'xxyx.mt.task.views';

    /**
     * 订单相关
     */
    const EXT_ORDER = "xxyx.order";

    /**
     * 支付宝转账相关
     */
    const EXT_TRANSFER = "xxyx.transfer";

    /**
     * 账单
     */
    const EXT_SELLER_BILL = "xxyx.seller.bill";

    /**
     * 任务
     */
    const EXT_TASK = 'xxyx.task';

    /**
     * 支付宝
     */
    const EXT_ALIPAY = "xxyx.alipay";

    /**
     * excel
     */
    const EXT_EXCEL = 'xxyx.excel';

    /**
     * user.sid
     */
    const EXT_USER_SID = 'xxyx.user.sid';
    const REFRESH_MT_BILL = 'xxyx.refresh.mt.bill';

    /**
     * 从截图中提取外卖单号
     */
    const EXT_EXTRACT_ORDER_NO = 'xxyx.extract.order_no';

    const EXT_MT_ORDER = 'xxyx.mt.order';

    /**
     * notice
     */
    const EXT_NOTICE = "xxyx.notice";

    #type
    /**
     * 直接匹配
     */
    const DIRECT = 'direct';

    /**
     * 全部发送
     */
    const FANOUT = 'fanout';

    /**
     * 正则匹配
     */
    const TOPIC = 'topic';

    # route_key

    /**
     * 任务浏览统计
     */
    const ROUTE_INC_TASK_VIEWS = 'inc.task.views';

    /**
     * 美团任务浏览统计
     */
    const ROUTE_MT_INC_TASK_VIEWS = 'inc.mt.task.views';

    /**
     * 创建账单
     */
    const ROUTE_CREATE_SELLER_BILL = 'create.seller.bill';

    /**
     * 创建任务
     */
    const ROUTE_CREATE_TASK = 'create.task';

    /**
     * 任务报名 （创建订单）
     */
    const ROUTE_TASK_REGISTRATION = "task.registration";

    /**
     * 美团团购订单，过期
     */
    const ROUTE_MTGP_ORDER_EXPIRED = "mtgp.expired";

    /**
     * 美团团购订单，超时完成
     */
    const ROUTE_MTGP_TIMEOUT_COMPLETED = "mtgp.timeout.completed";

    /**
     * 识图队列路由
     */
    const ROUTE_RECOGNIZE_IMAGES = "recognize.images";

    /**
     * 支付宝转账
     */
    const ROUTE_ALIPAY_TRANSFER = "alipay.transfer";

    /**
     * 单笔转账队列路由
     */
    const ROUTE_UNI_TRANSFER = "transfer.uni";

    /**
     * excel
     */
    const ROUTE_CREATE_EXCEL = "create.excel";

    /**
     * 创建用户sid
     */
    const ROUTE_CREATE_USER_SID = 'create.user.sid';

    /**
     * 创建用户sid
     */
    const ROUTE_EXTRACT_ORDER_NO = 'extract.order_no';

    const ROUTE_HANDLE_EVENT_CODE = 'handle.event.code';

    /**
     * notice
     */
    const ROUTE_NOTICE_ORDER_REVIEW = "order.review";

    # 队列
    /**
     * 任务浏览统计
     */
    const QUEUE_INC_TASK_VIEWS = 'inc.task.views';

    /**
     * 美团任务浏览统计
     */
    const QUEUE_MT_INC_TASK_VIEWS = 'inc.mt.task.views';

    /**
     * 任务报名（创建订单）
     */
    const QUEUE_TASK_REGISTRATION = "task.registration";

    /**
     * 美团团购订单，过期
     */
    const QUEUE_MTGP_ORDER_EXPIRED = "mtgp.expired";

    /**
     * 美团团购订单，超时完成
     */
    const QUEUE_MTGP_TIMEOUT_COMPLETED = "mtgp.timeout.completed";

    /**
     * 识图队列
     */
    const QUEUE_RECOGNIZE_IMAGES = "recognize.images";

    /**
     * 单笔转账队列
     */
    const QUEUE_UNI_TRANSFER = "transfer.uni";

    /**
     * 支付宝转账
     */
    const QUEUE_ALIPAY_TRANSFER = 'alipay.transfer';

    /**
     * 创建账单
     */
    const QUEUE_CREATE_SELLER_BILL = 'create.seller.bill';

    /**
     * 通过excel创建任务
     */
    const QUEUE_CREATE_TASK_BY_EXCEL = 'create.task_by_excel';


    const QUEUE_HANDLE_EVENT_CODE = 'handle.event.code';

    /**
     * excel
     */
    const QUEUE_CREATE_EXCEL = "create.excel";

    /**
     * 创建用户sid
     */
    const QUEUE_CREATE_USER_SID = 'create.user.sid';

    const QUEUE_REFRESH_MT_BILL = 'refresh.mt.bill';

    /**
     * 从截图中提取外卖单号
     */
    const QUEUE_EXTRACT_ORDER_NO = 'extract.order_no';

    /**
     * notice
     */
    const QUEUE_NOTICE_ORDER_REVIEW = "order.review";

    //更新统计数据
    const EXT_DASHBOARD = 'xxyx.dashboard';

    const QUEUE_DASHBOARD = 'create.dashboard';

    const ROUTE_DASHBOARD = 'create.dashboard';

    //生成美团账单
    const EXT_MT_BILL = "xxyx.mt.bill";

    const QUEUE_CREATE_MT_BILL = "create.mt.bill";

    const ROUTE_CREATE_MT_BILL = "create.mt.bill";

    //金刚区埋点统计
    const EXT_AD_CLICK = 'xxyx.ad.click';

    const QUEUE_AD_CLICK = 'create.ad.click';

    const ROUTE_AD_CLICK = 'create.ad.click';

    // 预支付循环发布任务关闭并且退款
    const EXT_TASK_CYCLE_CREATE_PRE = 'xxyx.task_cycle_create_pre';
    const QUEUE_TASK_CYCLE_CREATE_PRE = 'create.task_cycle_create_pre';
    const ROUTE_TASK_CYCLE_CREATE_PRE = 'create.task_cycle_create_pre';

    //更新统计数据
    const EXT_SELLER_STATISTICS = 'xxyx.seller_statistics';
    const QUEUE_SELLER_STATISTICS = 'create.seller_statistics';
    const ROUTE_SELLER_STATISTICS = 'create.seller_statistics';

    protected static function createConnection(): AMQPStreamConnection
    {
        $param = config('amqp.AMQP');
        return new AMQPStreamConnection(
            $param['host'],
            $param['port'],
            $param['login'],
            $param['password'],
            $param['vhost']
        );
    }

    protected static function setChannel($config, &$channel)
    {
        /*
            name: $exchange  创建交换机
            type: direct   直连方式
            passive: false
            durable: true  持久// 交换器将在服务器重启后继续存在
            auto_delete: false //一旦通道关闭，交换器将不会被删除。
        */
        $channel->exchange_declare($config['exchange_name'], $config['exchange_type'], false, true, false);

        if (is_array($config['queue_name'])) {
            foreach ($config['queue_name'] as $queueName) {
                /*
                     name: $queue  创建队列
                     passive: false
                     持久durable: true // //队列将在服务器重启后继续存在
                     互斥exclusive: false // 队列可以通过其他渠道访问
                     auto_delete: false 通道关闭后，队列不会被删除
                 */
                $channel->queue_declare($queueName, false, true, false, false);
                /**
                 * 绑定队列
                 */
                $channel->queue_bind($queueName, $config['exchange_name'], $config['route_key']);
            }
        } else {
            $channel->queue_declare($config['queue_name'], false, true, false, false);
            $channel->queue_bind($config['queue_name'], $config['exchange_name'], $config['route_key']);
        }
    }

    /**
     * 单条投递
     * @param $data
     * @param $config
     */
    public static function pushMessage($data, $config)
    {
        $connection = self::createConnection();
        $channel = $connection->channel();
        self::setChannel($config, $channel);
        /*
             $messageBody:消息体
             content_type:消息的类型 可以不指定
             delivery_mode:消息持久化最关键的参数
             AMQPMessage::DELIVERY_MODE_NON_PERSISTENT = 1;
             AMQPMessage::DELIVERY_MODE_PERSISTENT = 2;
         */
        $message = new AMQPMessage($data, array('content_type' => 'text/plain', 'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT));
        $channel->basic_publish($message, $config['exchange_name'], $config['route_key']);
        $channel->close();
        $connection->close();
    }

    /**
     *  批量投递
     * @param array $data
     * @param $config
     */
    public static function batchPushMessage(Array $data, $config)
    {
        $connection = self::createConnection();
        $channel = $connection->channel();
        self::setChannel($config, $channel);
        /*
             $messageBody:消息体
             content_type:消息的类型 可以不指定
             delivery_mode:消息持久化最关键的参数
             AMQPMessage::DELIVERY_MODE_NON_PERSISTENT = 1;
             AMQPMessage::DELIVERY_MODE_PERSISTENT = 2;
         */
        foreach ($data as $messageBody) {
            $message = new AMQPMessage($messageBody, array('content_type' => 'text/plain', 'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT));
            $channel->batch_basic_publish($message, $config['exchange_name'], $config['route_key']);
        }
        $channel->publish_batch();
        $channel->close();
        $connection->close();
    }
}
