<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/5/30 17:58
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------

use app\admin\exception\CurrentException;

class AppPush
{
    protected string $AppId;

    protected string $AppKey;

    protected string $MasterSecret;

    protected string $domain = 'https://restapi.getui.com';

    protected GTClient $api;

    protected GTNotification $notify;

    protected GTPushRequest $push;

    protected string $title;

    protected string $body;

    protected string $cid;

    protected string $payload;

    protected string $intent;

    protected string $subtitle;

    public function __construct()
    {
        $this->AppId = config('params.app_push.app_id');
        $this->AppKey = config('params.app_push.app_key');
        $this->MasterSecret = config('params.app_push.master_secret');
        $this->api = new GTClient($this->domain, $this->AppKey, $this->AppId, $this->MasterSecret);
        $this->notify = new GTNotification();
        $this->push = new GTPushRequest();
    }

    /**
     * @param $title
     * @param string $subtitle
     * @param string $body
     * @param string $payload
     * @param string $clickType
     * @return AppPush
     */
    public function setNotify($title, string $subtitle = '', string $body = '', string $payload = '', string $clickType = 'intent'): AppPush
    {
        $this->title = $title;
        $this->body = $body;
        $this->payload = $payload;
        $this->subtitle = $subtitle;

        $this->notify->setTitle($title);
        $this->notify->setBody($body);
        $this->notify->setClickType($clickType);
        switch ($clickType) {
            case 'intent':
                $intent = 'intent:#Intent;action=android.intent.action.oppopush;launchFlags=0x14000000;' .
                    'component=moc.nauxuoyoaixoaix.www/io.dcloud.PandoraEntry;S.UP-OL-SU=true;S.payload=' . $payload . ';end';
                $this->intent = $intent;
                $this->notify->setIntent($intent);
                break;
            case 'payload':
                $this->payload = $payload;
                $this->notify->setPayload($payload);
                break;
        }
        $this->notify->setChannelLevel(4);
        return $this;
    }

    public function setPushCid($cid): AppPush
    {
        $this->cid = $cid;
        $this->push->setRequestId("xxyx" . substr(md5(time() . rand(1000, 9999), false), 4, 20));
        $this->push->setCid($cid);
        return $this;
    }

    protected function pushAndroidChannel($options = null)
    {
        $channel = new GTPushChannel();
        $android = new GTAndroid();
        $ups = new GTUps();
        $ups->setNotification($this->notify);
        empty($options) || $ups->setOptions($options);
        $ups->setNotification($this->notify);
        $android->setUps($ups);
        $channel->setAndroid($android);
        $this->push->setPushChannel($channel);
    }

    protected function pushIosChannel()
    {
        $channel = new GTPushChannel();
        $ios = new GTIos();
        empty($this->payload) || $ios->setPayload($this->payload);
        $aps = new GTAps();
        $aps->setSound('default');
        $alert = new GTAlert();
        $alert->setTitle($this->title);
        $alert->setBody($this->body);
        empty($this->subtitle) || $alert->setSubtitle($this->subtitle);
        $aps->setAlert($alert);
        $ios->setAps($aps);
        $channel->setIos($ios);
        $this->push->setPushChannel($channel);
    }

    public function pushIosNotifyMessage()
    {
        $message = new GTPushMessage();
        $message->setNotification($this->notify);
        $settings = new GTSettings();
        $settings->setTtl(24 * 3600 * 1000);
        $this->push->setPushMessage($message);
        $this->push->setSettings($settings);
        $this->pushIosChannel();
        return $this->api->pushApi()->pushToSingleByCid($this->push);
    }

    public function pushAndroidNotifyMessage($channel = true, $options = null)
    {
        $message = new GTPushMessage();
        $message->setNotification($this->notify);
        $settings = new GTSettings();
        $settings->setTtl(-1);
        $this->push->setPushMessage($message);
        $this->push->setSettings($settings);
        if ($channel) {
            $this->pushAndroidChannel($options);
        }
        return $this->api->pushApi()->pushToSingleByCid($this->push);
    }

}