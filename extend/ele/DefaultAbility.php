<?php

namespace ele;

use ele\request\AlibabaAlscUnionElemePromotionStorepromotionGetRequest;
use ele\request\AlibabaAlscUnionElemePromotionStorepromotionQueryRequest;
use ele\request\AlibabaAlscUnionElemeStorepromotionReviewbwcDetailGetRequest;
use ele\request\AlibabaAlscUnionElemeStorepromotionReviewbwcQueryRequest;
use ele\request\AlibabaAlscUnionElemeStorepromotionReviewbwcStockLockRequest;
use ele\request\AlibabaAlscUnionElemeStorepromotionReviewbwcStockReleaseRequest;
use ele\request\AlibabaAlscUnionElemeToolOrderAttrbuteCheckRequest;
use ele\request\AlibabaAlscUnionKbcpxPositiveOrderGetRequest;
use ele\request\AlibabaAlscUnionElemePromotionOfficialactivityGetRequest;
use ele\request\AlibabaAlscUnionKbcpxPunishOrderGetRequest;
use ele\request\AlibabaAlscUnionKbcpxRefundOrderGetRequest;

class DefaultAbility
{
    public TopApiClient $client;

    public function __construct(TopApiClient $client)
    {
        $this->client = $client;
    }

    /**
     * 本地联盟饿了么单店推广店铺列表
     **/
    public function alibabaAlscUnionElemePromotionStorepromotionQuery(AlibabaAlscUnionElemePromotionStorepromotionQueryRequest $request)
    {
        return $this->client->execute('alibaba.alsc.union.eleme.promotion.storepromotion.query', $request->toMap(), $request->toFileParamMap());
    }

    /**
     * 本地生活媒体推广订单明细报表查询
     **/
    public function alibabaAlscUnionKbcpxPositiveOrderGet(AlibabaAlscUnionKbcpxPositiveOrderGetRequest $request)
    {
        return $this->client->execute('alibaba.alsc.union.kbcpx.positive.order.get', $request->toMap(), $request->toFileParamMap());
    }

    /**
     * 本地联盟饿了么单店推广单店铺查询
     **/
    public function alibabaAlscUnionElemePromotionStorepromotionGet(AlibabaAlscUnionElemePromotionStorepromotionGetRequest $request)
    {
        return $this->client->execute('alibaba.alsc.union.eleme.promotion.storepromotion.get', $request->toMap(), $request->toFileParamMap());
    }

    /**
     * 本地联盟饿了么推广官方活动查询
     **/
    public function alibabaAlscUnionElemePromotionOfficialactivityGet(AlibabaAlscUnionElemePromotionOfficialactivityGetRequest $request)
    {
        return $this->client->execute("alibaba.alsc.union.eleme.promotion.officialactivity.get", $request->toMap(), $request->toFileParamMap());
    }

    /**
    本地联盟饿了么评价有礼库存锁定
     **/
    public function alibabaAlscUnionElemeStorepromotionReviewbwcStockLock(AlibabaAlscUnionElemeStorepromotionReviewbwcStockLockRequest $request)
    {
        return $this->client->execute("alibaba.alsc.union.eleme.storepromotion.reviewbwc.stock.lock", $request->toMap(), $request->toFileParamMap());
    }

    /**
    本地联盟饿了么评价有礼锁定库存释放
     **/
    public function alibabaAlscUnionElemeStorepromotionReviewbwcStockRelease(AlibabaAlscUnionElemeStorepromotionReviewbwcStockReleaseRequest $request)
    {
        return $this->client->execute("alibaba.alsc.union.eleme.storepromotion.reviewbwc.stock.release", $request->toMap(), $request->toFileParamMap());
    }

    /**
    本地联盟饿了么评价有礼店铺列表
     **/
    public function alibabaAlscUnionElemeStorepromotionReviewbwcQuery(AlibabaAlscUnionElemeStorepromotionReviewbwcQueryRequest $request)
    {
        return $this->client->execute('alibaba.alsc.union.eleme.storepromotion.reviewbwc.query', $request->toMap(), $request->toFileParamMap());
    }

    /**
    本地联盟饿了么评价有礼店铺详情
     **/
    public function alibabaAlscUnionElemeStorepromotionReviewbwcDetailGet(AlibabaAlscUnionElemeStorepromotionReviewbwcDetailGetRequest $request)
    {
        return $this->client->execute('alibaba.alsc.union.eleme.storepromotion.reviewbwc.detail.get', $request->toMap(), $request->toFileParamMap());
    }

    /**
    本地联盟饿了么归因排查工具
     **/
    public function alibabaAlscUnionElemeToolOrderAttrbuteCheck(AlibabaAlscUnionElemeToolOrderAttrbuteCheckRequest $request)
    {
        return $this->client->execute('alibaba.alsc.union.eleme.tool.order.attrbute.check', $request->toMap(), $request->toFileParamMap());
    }

    /**
    本地生活媒体推广用户维权订单数据报表
     **/
    public function alibabaAlscUnionKbcpxRefundOrderGet(AlibabaAlscUnionKbcpxRefundOrderGetRequest $request)
    {
        return $this->client->execute("alibaba.alsc.union.kbcpx.refund.order.get", $request->toMap(), $request->toFileParamMap());
    }

    /**
    本地生活媒体推广用户反作弊订单数据报表
     **/
    public function alibabaAlscUnionKbcpxPunishOrderGet(AlibabaAlscUnionKbcpxPunishOrderGetRequest $request)
    {
        return $this->client->execute("alibaba.alsc.union.kbcpx.punish.order.get", $request->toMap(), $request->toFileParamMap());
    }
}
