<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/4/20 9:41
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace http;

abstract class Request
{
    #region 基础属性

    // 访问url
    protected string $baseUrl;

    protected $responseRaw;

    // 返回体
    protected $responseBody;

    // 返回头
    protected $responseHeader;

    // 返回详情
    protected array $execInfo = [];

    // 请求详情
    protected array $requestInfo = [];

    // 参数
    protected $params = [];

    // 自定义请求头
    protected array $headers;

    // 连接超时时间
    protected int $connectTimeOut = 10;

    // 等待超时时间
    protected int $timeOut = 10;

    // curl对象
    protected $ch;

    // 错误编号
    protected $errno;

    // 错误信息
    protected $error;

    #endregion

    #region 基础属性get、set方法

    /**
     * 设置url
     * @param $url
     */
    abstract function setBaseUrl($url);

    /**
     * 获取URL
     * @return string
     */
    abstract function getBaseUrl(): string;

    /**
     * 获取请求类型
     * @return string
     */
    abstract function getMethod(): string;

    /**
     * 不同方法处理curl配置
     * @param $ch
     * @return mixed
     */
    abstract function methodCurl();

    /**
     * 设置返回体
     * @param $responseBody
     */
    public function setResponseBody($responseBody)
    {
        $this->responseBody = $responseBody;
    }

    /**
     * 获取返回体
     * @return string|null
     */
    public function getResponseBody()
    {
        return $this->responseBody;
    }

    /**
     * 设置返回头
     * @param $responseHeader
     */
    public function setResponseHeader($responseHeader)
    {
        $this->responseHeader = $responseHeader;
    }

    /**
     * 获取返回头
     * @return string|null
     */
    public function getResponseHeader()
    {
        return $this->responseHeader;
    }

    /**
     * 设置返回详情
     * @param $execInfo
     */
    public function setExecInfo($execInfo)
    {
        $this->execInfo = $execInfo;
    }

    /**
     * 获取返回详情
     * @return mixed
     */
    public function getExecInfo()
    {
        return $this->getExecInfo();
    }

    /**
     * 设置请求详情
     * @param $requestInfo
     */
    public function setRequestInfo($requestInfo)
    {
        $this->requestInfo = $requestInfo;
    }

    public function setResponseRaw($responseRaw)
    {
        $this->responseRaw = $responseRaw;
    }

    public function getResponseRaw()
    {
        return $this->responseRaw;
    }

    /**
     * 添加请求详情
     * @param $key
     * @param $value
     */
    public function addRequestInfo($key, $value)
    {
        $this->requestInfo[$key] = $value;
    }

    /**
     * 获取请求详情
     * @return array
     */
    public function getRequestInfo(): array
    {
        return $this->requestInfo;
    }

    /**
     * 设置参数
     * @param $params
     */
    public function setParams($params)
    {
        $this->params = $params;
    }

    /**
     * 获取参数
     * @return array
     */
    public function getParams(): array
    {
        return $this->params;
    }

    /**
     * 设置请求头
     * @param array $headers
     */
    public function setHeaders(array $headers)
    {
        $this->headers = $headers;
    }

    /**
     * 获取请求头
     * @return array
     */
    public function getHeaders(): array
    {
        return $this->headers;
    }

    /**
     * 设置连接超时时间
     * @param $connectTimeOut
     */
    public function setConnectTimeOut($connectTimeOut)
    {
        $this->connectTimeOut = $connectTimeOut;
    }

    /**
     * 获取连接超时时间
     * @return int
     */
    public function getConnectTimerOut(): int
    {
        return $this->connectTimeOut;
    }

    /**
     * 设置等待超时
     * @param $timeOut
     */
    public function setTimeOut($timeOut)
    {
        $this->timeOut = $timeOut;
    }

    /**
     * 获取等待超时
     * @return int
     */
    public function getTimeOut(): int
    {
        return $this->timeOut;
    }

    /**
     * 设置error信息
     * @param $errno
     * @param $error
     */
    public function setError($errno, $error)
    {
        $this->errno = $errno;
        $this->error = $error;
    }

    /**
     * 获取error信息
     * @return array
     */
    public function getError(): array
    {
        return ['error' => $this->error, 'errno' => $this->errno];
    }

    #endregion

    /**
     * 重置cUrl配置
     */
    public function resetCurl()
    {
        if ($this->ch) {
            @curl_close($this->ch);
        }
        $this->ch = curl_init();
        $url = $this->getUrl();
        $this->addRequestInfo('url', $url);
        curl_setopt($this->ch, CURLOPT_URL, $url);
        curl_setopt($this->ch, CURLOPT_HEADER, true);
        curl_setopt($this->ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($this->ch, CURLOPT_CONNECTTIMEOUT, $this->connectTimeOut);
        curl_setopt($this->ch, CURLOPT_TIMEOUT, $this->timeOut);
        curl_setopt($this->ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($this->ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($this->ch, CURLINFO_HEADER_OUT, true);
        if (!empty($this->headers)) {
            curl_setopt($this->ch, CURLOPT_HTTPHEADER, $this->headers);
            $this->addRequestInfo('headers', $this->headers);
        }
        $this->methodCurl();
        $this->addRequestInfo('params', $this->params);
    }

    /**
     * 发起请求
     */
    public function request()
    {
        $response = curl_exec($this->ch);
        $this->addRequestInfo('curl_info', curl_getinfo($this->ch));

        if ($response === false) {
            $this->setError(curl_errno($this->ch), curl_error($this->ch));
            return;
        }

        $this->setResponseRaw($response);
        [$header, $body] = $this->explodeResponse($response);
        if (!empty($header)) {
            $this->setResponseHeader($header);
        }
        if (!empty($body)) {
            $this->setResponseBody($body);
        }
        $this->setExecInfo(curl_getinfo($this->ch));

        $httpCode = $this->requestInfo['curl_info']['http_code'];

        if ($httpCode < 200 || $httpCode > 209) {
            $this->setError($httpCode, $this->getResponseBody());
        }
    }

    /**
     * response格式化
     * @param $responseCnt
     * @return array|void
     */
    public function explodeResponse($responseCnt)
    {
        $rows = mb_split("\r\n", $responseCnt);
        //第一行不是http头协议信息，则说明没有响应头
        if ($rows && !preg_match('/HTTP\/\d(\.\d)?\s+\d{3}/i', $rows[0])) {
            return ['', $responseCnt];
        }
        //发生了10x状态
        if (
            $rows
            && preg_match('/HTTP\/\d\.\d\s+(100|101|102)/i', $rows[0])
        ) {
            $rows = mb_split("\r\n\r\n", $responseCnt);
            $body = [];
            for ($i = 2; $i < count($rows); $i++)
                $body[] = $rows[$i];
            return [$rows[1], implode("\r\n\r\n", $body)];
        }
        $splitIndex = mb_strpos($responseCnt, "\r\n\r\n", 0, 'utf-8');
        $header = trim(mb_substr($responseCnt, 0, $splitIndex, 'utf-8'));
        $body = trim(mb_substr($responseCnt, $splitIndex + 1, null, 'utf-8'));
        $this->setResponseBody($body);
        $this->setResponseHeader($header);
    }
}