<?php

namespace http;

use app\admin\exception\CurrentException;

class Api
{
    // 访问url
    protected string $baseUrl;

    // 访问类型
    protected string $method = 'get';

    // 返回体
    protected $responseBody;

    // 返回头
    protected $responseHeader;

    // 返回详情
    protected $execInfo;

    // 请求详情
    protected array $requestInfo = [];

    // post参数
    protected $params;

    // 自定义请求头
    protected array $headers;

    // 连接超时时间
    protected int $connectTimeOut = 10;

    // 等待超时时间
    protected int $timeOut = 10;

    // curl对象
    protected $ch;

    // 错误编号
    protected $errno;

    // 错误信息
    protected $error;

    protected $methods = ['get', 'post'];

    public function __construct($url, $method = 'get', $params = [], $headers = [], callable $callback = null)
    {
        if (!$url || !is_string($url)) {
            throw new CurrentException('url不是一个有效的地址');
        }
        $method = strtolower($method);
        if (!in_array($method, $this->methods)) {
            throw new CurrentException('不支持的请求方式：' . $method);
        }
        $this->headers = $headers;
        $this->baseUrl = $url;
        $this->method = $method;
        $this->params = $params;
        $this->initCurl($callback);
    }

    /**
     * @return string
     */
    public function getUrl()
    {
        $url = $this->baseUrl;
        if ($this->method == 'get') {
            $queryStr = http_build_query($this->params);
            if ($queryStr) {
                $url .= (strpos($this->baseUrl, '?') === false ? '?' : '&') . $queryStr;
            }
        }
        return $url;
    }

    /**
     * @param int $timeOut
     */
    public function setTimeOut($timeOut)
    {
        $this->timeOut = $timeOut;
    }

    /**
     * @return int
     */
    public function getTimeOut()
    {
        return $this->timeOut;
    }

    /**
     * 设置是否需要响应头
     * @param bool $needHeader
     */
    public function setChHeader($needHeader = false)
    {
        curl_setopt($this->ch, CURLOPT_HEADER, !!$needHeader);
    }

    /**
     * @return null
     */
    public function getParams()
    {
        return $this->params;
    }


    /**
     * @param $code
     * @return mixed
     */
    public function getCurlInfo()
    {
        return $this->requestInfo;
    }

    /**
     * 初始curl对象设置
     * @param callable $callback
     */
    protected function initCurl(callable $callback = null)
    {
        $url = $this->getUrl();
        $this->requestInfo['url'] = $url;
        if ($this->ch) {
            @curl_close($this->ch);
        }
        $this->ch = curl_init();
        curl_setopt($this->ch, CURLOPT_URL, $url);
        curl_setopt($this->ch, CURLOPT_HEADER, true);
        curl_setopt($this->ch, CURLOPT_AUTOREFERER, 1);
        curl_setopt($this->ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($this->ch, CURLOPT_CONNECTTIMEOUT, $this->connectTimeOut);
        curl_setopt($this->ch, CURLOPT_TIMEOUT, $this->timeOut);
        curl_setopt($this->ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($this->ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($this->ch, CURLINFO_HEADER_OUT, true);
        $callback && $callback($this->ch);
        if (!empty($this->headers)) {
            curl_setopt($this->ch, CURLOPT_HTTPHEADER, $this->headers);
        }

        switch ($this->method) {
            case 'get':
                curl_setopt($this->ch, CURLOPT_POST, 0);
                break;
            case 'post':
                $this->requestInfo['params'] = $this->params;
                curl_setopt($this->ch, CURLOPT_POST, 1);
                curl_setopt($this->ch, CURLOPT_POSTFIELDS, $this->params);
                break;
        }
    }

    public function request()
    {
        $this->requestInfo['response'] = $response = curl_exec($this->ch);
        $this->requestInfo['httpInfo'] = curl_getinfo($this->ch);
        if ($response === false) {
            $this->errno = curl_errno($this->ch);
            $this->error = curl_error($this->ch);
            $this->requestInfo['curl_info'] = curl_getinfo($this->ch);
//            throw new CurrentException($this->error);
        } else {
            [$this->responseHeader, $this->responseBody] = static::explodeResponse($response);
            $this->requestInfo['curl_info'] = $this->execInfo = curl_getinfo($this->ch);
        }
    }

    public function getResponseBody()
    {
        return $this->responseBody;
    }

    public function getResponseHeader()
    {
        return $this->responseHeader;
    }

    /**
     * 切分响应数据
     * @param $responseCnt
     * @return array 成功[header,body],失败:[false,false]
     */
    static public function explodeResponse($responseCnt)
    {
        $rows = mb_split("\r\n",$responseCnt);
        //第一行不是http头协议信息，则说明没有响应头
        if($rows && !preg_match('/HTTP\/\d(\.\d)?\s+\d{3}/i',$rows[0])){
            return ['', $responseCnt];
        }
        //发生了10x状态
        if($rows && preg_match('/HTTP\/\d\.\d\s+(100|101|102)/i',$rows[0])){
            $rows = mb_split("\r\n\r\n",$responseCnt);
            $body = [];
            for ($i = 2;$i<count($rows); $i++)
                $body[] = $rows[$i];
            return [$rows[1], implode("\r\n\r\n",$body)];
        }
        $splitIndex  = mb_strpos($responseCnt,"\r\n\r\n",0,'utf-8');
        $header = trim(mb_substr($responseCnt,0,$splitIndex,'utf-8'));
        $body   = trim(mb_substr($responseCnt,$splitIndex+1,null,'utf-8'));
        return [$header,$body];
    }

}
