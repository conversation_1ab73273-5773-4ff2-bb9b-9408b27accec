<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/4/20 12:34
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace http;

use app\admin\exception\CurrentException;

/**
 * @mixin Get|Post
 */
class Api
{
    protected Request $request;

    /**
     * @param string $url
     * @param string $method
     * @param $params
     * @param array $headers
     * @throws CurrentException
     */
    public function __construct(string $url = '', string $method = 'get', $params = [], array $headers = [])
    {
        if (!empty($url)) {
            $this->resetConfig($url, $method, $params, $headers);
        }
    }

    /**
     * 重新配置curl
     * @param $url
     * @param string $method
     * @param $params
     * @param array $headers
     * @throws CurrentException
     */
    public function resetConfig($url, string $method = 'get', $params = [], array $headers = []): Api
    {
        if (!$url || !is_string($url)) {
            throw new CurrentException('url不是一个有效的地址');
        }
        $method = strtolower($method);
        switch ($method) {
            case 'get':
                /** @var Request $request */
                $request = app(Get::class);
                break;
            case 'post':
                /** @var Request $request */
                $request = app(Post::class);
                break;
            default:
                throw new CurrentException('不支持的请求方式：' . $method);
        }
        // 清除请求记录信息
        $request->setResponseHeader('');
        $request->setResponseBody('');
        $request->setExecInfo([]);
        $request->setError('', '');
        $request->setRequestInfo([]);
        // 重置请求配置
        $request->setBaseUrl($url);
        $request->setHeaders($headers);
        $request->setParams($params);
        $request->resetCurl();
        $this->request = $request;
        return $this;
    }

    public function request(): Api
    {
        $this->request->request();
        return $this;
    }

    public function __call($name, $arguments)
    {
        return $this->request ? call_user_func_array([$this->request, $name], $arguments) : false;
    }
}