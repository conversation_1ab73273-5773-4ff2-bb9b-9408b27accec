<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2022/4/20 10:56
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
namespace http;

class Get extends Request
{
    /**
     * 设置基础URL
     * @param $url
     */
    public function setBaseUrl($url)
    {
        $this->baseUrl = $url;
    }

    /**
     * 获取请求URL
     * @param $url
     * @return string
     */
    public function getUrl(): string
    {
        $url = $this->baseUrl;
        $queryStr = http_build_query($this->params);
        if ($queryStr) {
            $url .= (strpos($this->baseUrl, '?') === false ? '?' : '&') . $queryStr;
        }
        return $url;
    }

    /**
     * 获取基础URL
     * @return string
     */
    public function getBaseUrl(): string
    {
        return $this->baseUrl;
    }

    public function getMethod(): string
    {
        return 'get';
    }

    public function methodCurl()
    {
        curl_setopt($this->ch, CURLOPT_POST, 0);
    }
}