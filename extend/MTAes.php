<?php
// +----------------------------------------------------------------------
// | Create by 小无 [ 晓晓优选 ]
// +----------------------------------------------------------------------
// | Copyright (c) Zhy-小无 All rights reserved.
// +----------------------------------------------------------------------
// | DateTime 2023/6/8 10:57
// +----------------------------------------------------------------------
// | Author: 小无 <<EMAIL>>
// +----------------------------------------------------------------------
use app\common\model\MtConfig;

class MTAes
{
    public static function encrypt($cleartext, $p = ''): string
    {
        $limitLength = 16;
        if (empty($p)) {
            $p = self::getP();
        }
        $zeroIv = substr($p, 0, $limitLength);
        $key = substr($p, 0, $limitLength);

        $encryptedData = openssl_encrypt($cleartext, "AES-128-CBC", $key, OPENSSL_RAW_DATA, $zeroIv);
        return base64_encode($encryptedData);
    }

    /**
     * 获取P参数
     * @return string
     */
    public static function getP(): string
    {
        return (string)MtConfig::where('key', 'p')
            ->where('data_state', '=', 0)
            ->value('value');
    }
}